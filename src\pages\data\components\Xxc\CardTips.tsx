/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-21 16:27:11
 * @LastEditTime: 2025-02-11 17:33:24
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { memo } from 'react';

interface IProps {
  className?: string;
  icon?: React.ReactNode;
  title?: string;
  tips?: string;
}

const StyledCard = styled.section`
  width: ${() => px2vw(285)};
  border-radius: ${() => px2font(16)};
  padding: ${() => px2vh(20)} ${() => px2vw(20)};
  gap: ${() => px2vw(20)};
  .content {
    gap: ${() => px2vh(10)};
    .title {
      font-size: ${() => px2font(16)};
    }
    .desc {
      font-size: ${() => px2font(12)};
    }
  }
`;

export const CardTips = memo((props: IProps) => {
  const { icon, tips, title } = props;
  return (
    <StyledCard className="flex flex-row bg-[#1E1E27]/70">
      <div className="flex flex-col justify-center items-center">{icon}</div>
      <div className="content flex flex-col justify-center ">
        <p className="title text-[#ffffff] font-medium">{title}</p>
        <p className="desc text-[#737373] font-normal">{tips}</p>
      </div>
    </StyledCard>
  );
});
