/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-19 14:22:22
 * @LastEditTime: 2025-01-22 01:07:22
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import { permissionApi } from '@/api';
import { getAllMosqutio } from '@/utils/mosqutio.util';
import { request, useModel } from '@umijs/max';
import { useEffect, useLayoutEffect, useState } from 'react';

const MOSQUITO_LIST_LOCAL_STORAGE_KEY = 'mosquito_list';
const useMosquitoModel = () => {
  // 3 属 7 类
  let mosList: any[] = [];
  try {
    const str = localStorage.getItem(MOSQUITO_LIST_LOCAL_STORAGE_KEY) || '';
    const list = JSON.parse(str);
    if (Array.isArray(list)) {
      mosList = list;
    }
  } catch (e) {
    console.log(e);
  }
  const [mosquitoList, setMosquitoList] = useState<any>(mosList);
  const { getPersist } = useModel('persist');
  useEffect(() => {
    const user = getPersist('user');

    if (!user?.name) {
      return;
    }
    const list = Array.isArray(user.data_permissions) ? user.data_permissions : [];
    const mqlist: any = list.filter((item: any) => item.level === 3);
    setMosquitoList(mqlist);
    localStorage.setItem(MOSQUITO_LIST_LOCAL_STORAGE_KEY, JSON.stringify(mqlist));
    const getData = async () => {
      const result = await request(permissionApi.getDataPermissionList, {
        method: 'GET',
        params: {
          tenant_id: 1,
          type: 'mosquito',
          level: 3,
        },
      });
      if (Array.isArray(result?.data?.list) && result?.data?.list[0]) {
        const list = result?.data?.list[0].children || [];
        const mqlist: any = getAllMosqutio(list);
        setMosquitoList(mqlist);
        localStorage.setItem(MOSQUITO_LIST_LOCAL_STORAGE_KEY, JSON.stringify(mqlist));
      }
    };
    if (!user.data_permissions) {
      getData();
    }
  }, []);

  return {
    mosquitoList,
    setMosquitoList,
  };
};
export default useMosquitoModel;
