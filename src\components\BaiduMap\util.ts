export /**
 * 计算坐标点数组的中心点
 * @param {BMapGL.Point[]} points - 坐标点数组
 * @returns {BMapGL.Point} 中心点坐标
 */
function calculateCenterPoint(points: BMapGL.Point[]) {
  const sumLng = points.reduce((sum, point) => sum + point.lng, 0);
  const sumLat = points.reduce((sum, point) => sum + point.lat, 0);
  return new BMapGL.Point(sumLng / points.length, sumLat / points.length);
}
/**
 * 对百度地图多边形坐标点按逆时针方向排序
 * @param {BMapGL.Point[]} points - 待排序的坐标点数组
 * @returns {BMapGL.Point[]} 排序后的坐标点数组
 */
export function sortPolygonPoints(points: BMapGL.Point[]) {
  if (!points || points.length < 3) {
    return [...points]; // 少于3个点无需排序
  }

  // 1. 计算所有点的中心点
  const center = calculateCenterPoint(points);

  // 2. 计算每个点相对于中心点的角度（弧度）
  function getAngle(point: BMapGL.Point) {
    const dx = point.lng - center.lng;
    const dy = point.lat - center.lat;
    let angle = Math.atan2(dy, dx); // 计算弧度
    return angle < 0 ? angle + 2 * Math.PI : angle; // 转换为0-2π范围
  }

  // 3. 按角度从大到小排序（逆时针方向）
  return [...points].sort((a, b) => {
    const angleA = getAngle(a);
    const angleB = getAngle(b);
    return angleB - angleA; // 降序排列（逆时针）
  });
}
export function enhancedDistanceCalc(
  point: BMapGL.Point,
  lineStart: BMapGL.Point,
  lineEnd: BMapGL.Point,
): number {
  // 使用更精确的距离计算公式
  const x = point.lng,
    y = point.lat;
  const x1 = lineStart.lng,
    y1 = lineStart.lat;
  const x2 = lineEnd.lng,
    y2 = lineEnd.lat;

  const A = x - x1;
  const B = y - y1;
  const C = x2 - x1;
  const D = y2 - y1;

  const dot = A * C + B * D;
  const len_sq = C * C + D * D;
  let param = -1;
  if (len_sq !== 0) param = dot / len_sq;

  let xx, yy;
  if (param < 0) {
    xx = x1;
    yy = y1;
  } else if (param > 1) {
    xx = x2;
    yy = y2;
  } else {
    xx = x1 + param * C;
    yy = y1 + param * D;
  }

  const dx = x - xx;
  const dy = y - yy;
  return Math.sqrt(dx * dx + dy * dy);
}

export function optimizedSimplify(points: BMapGL.Point[], epsilon = 0.00001): BMapGL.Point[] {
  if (!Array.isArray(points) || points.length < 2) {
    return points;
  }

  let maxDistance = 0;
  let maxIndex = 0;
  const end = points.length - 1;

  for (let i = 1; i < end; i++) {
    const distance = enhancedDistanceCalc(points[i], points[0], points[end]);
    if (distance > maxDistance) {
      maxDistance = distance;
      maxIndex = i;
    }
  }

  if (maxDistance > epsilon) {
    const left = optimizedSimplify(points.slice(0, maxIndex + 1), epsilon);
    const right = optimizedSimplify(points.slice(maxIndex), epsilon);
    return left.slice(0, -1).concat(right);
  } else {
    return [points[0], points[end]];
  }
}

export const getBoundaryPoints = (city: string): Promise<BMapGL.Point[]> => {
  return new Promise((resolve, reject) => {
    const cacheKey = `boundary_${city}`;
    const cachedData = localStorage.getItem(cacheKey);

    if (cachedData) {
      try {
        const points = JSON.parse(cachedData).map(
          (p: { lng: number; lat: number }) => new window.BMapGL.Point(p.lng, p.lat),
        );
        console.log('加载缓存边界数据', city);
        resolve(points);
        return;
      } catch (e) {
        console.error('解析缓存失败', e);
        localStorage.removeItem(cacheKey);
      }
    }

    const bd = new window.BMapGL.Boundary();
    bd.get(
      city,
      (rs: any) => {
        const bound = rs.boundaries[0];
        if (!bound) {
          reject(new Error('未找到边界数据'));
          return;
        }
        const pList: BMapGL.Point[] = [];
        const str = bound.replace(' ', '');
        const p = str.split(';');
        for (let j = 0; j < p.length; j++) {
          const [lng, lat] = p[j].split(',');
          pList.push(new window.BMapGL.Point(parseFloat(lng), parseFloat(lat)));
        }

        const simplified = optimizedSimplify(pList, 0.0005);
        console.log(simplified.length);
        // BMapGL.Point is complex, store plain objects
        const storablePoints = simplified.map((p: any) => ({ lng: p.lng, lat: p.lat }));
        localStorage.setItem(cacheKey, JSON.stringify(storablePoints));
        console.log('获取并缓存新边界数据', city);
        resolve(simplified);
      },
      (error: any) => {
        reject(error);
      },
    );
  });
};
