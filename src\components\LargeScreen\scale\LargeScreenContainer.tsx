/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-25 18:23:36
 * @LastEditTime: 2024-12-09 10:47:27
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  children?: React.ReactNode;
}

export const LargeScreenContainer = (props: IProps) => {
  return (
    <div
      className="bg-[#090914]"
      style={{
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: ' translate(-50%, -50%)',
      }}
    >
      {props.children}
    </div>
  );
};
