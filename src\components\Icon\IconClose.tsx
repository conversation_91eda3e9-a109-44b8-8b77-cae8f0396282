import { useState } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 12:06:43
 * @LastEditTime: 2025-02-06 20:50:51
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
  defaultColor?: string;
  onClick?: (e?: any) => void;
}

export const IconClose = (props: IProps) => {
  const { className, onClick, defaultColor = '#667085' } = props;
  const [color, setColor] = useState(defaultColor);

  return (
    <svg
      onClick={onClick}
      onMouseEnter={() => setColor('#0080FF')}
      onMouseLeave={() => setColor(defaultColor)}
      className={`xc-svg shrink-0 ${className ?? ''}`}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 18L18 6M6 6L18 18"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
