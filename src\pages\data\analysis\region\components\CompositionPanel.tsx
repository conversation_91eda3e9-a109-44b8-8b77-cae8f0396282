import React, { useMemo, useRef } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { ReactComponent as IconRegionForm } from '../../assets/region-form.svg';
import { useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import DataSearchForm from '@/pages/data/components/DataSearchForm';
import { useModel } from '@umijs/max';
import { DATE_FMT, formXLabelByTimeUnit, TIME_UNITS } from '@/utils/time.util';
import dayjs from 'dayjs';
import { Spin } from 'antd';
import useMosquitoModel from '@/models/useMosquito';

const CompositionPanel: React.FC = (props: any) => {
  const { menu } = useModel('useMenu', (m) => ({
    menu: m.menu,
    defaultOpenKeys: m.defaultOpenKeys,
  }));
  const queryRef = useRef<any>({});
  const {
    run: getSpeciesCompose,
    data: speciesComposeRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      queryRef.current = { ...data };
      return {
        url: statisticsAPI.getSpeciesCompose,
        method: 'GET',
        params: { ...queryRef.current, tenant_id: 1 },
        headers,
      };
    },
    {
      manual: true,
      throwOnError: true,
    },
  );
  const { mosquitoList } = useMosquitoModel();
  const names = useMemo(() => {
    if (queryRef.current.monitor_type) {
      const list = queryRef.current.monitor_type.split(',');
      return list.map((item: any) => item?.toLowerCase());
    } else {
      return mosquitoList.map((item: any) => item?.code?.toLowerCase());
    }
  }, [mosquitoList, speciesComposeRes]);
  const speciesCompose = useMemo(() => {
    const trendPoints = speciesComposeRes?.trend_points || [];
    const result = names
      .map((name: any, index) => {
        const list: any = trendPoints.map((point: any) => {
          const species = point.species || [];
          const mos = species.find((item: any) => item.monitor_type?.toLowerCase() === name);
          return {
            count: mos?.count || 0,
          };
        });
        return {
          name,
          data: list,
        };
      })
      .filter((item: any) => item.data.some((d: any) => d.count > 0));
    return result;
  }, [speciesComposeRes, names]);

  const option = useMemo(() => {
    const xLabels = speciesComposeRes?.trend_points.map((s: any) => {
      return formXLabelByTimeUnit(s.time_point, queryRef.current.time_unit);
    });
    const optData = {
      tooltip: { trigger: 'axis' },
      legend: {
        selectedMode: false,
        bottom: 0,
      },
      grid: { left: 80, right: 80, top: 60, bottom: 80 },
      xAxis: [
        {
          type: 'category',
          name: '时间',
          nameTextStyle: { color: '#797A85', align: 'center' },
          data: xLabels,
          axisLabel: {
            color: '#797A85',
            overflow: 'truncate',
            ellipsis: '...',
            rotate: 0,
            margin: 20,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: {
        type: 'value',
        name: '蚊虫监测总数（只）',
        nameLocation: 'end',
        nameTextStyle: { color: '#797A85', align: 'center' },
        axisLabel: { color: '#797A85', rotate: 0, margin: 20 },
        splitLine: { lineStyle: { color: '#E4E6EB', type: 'dashed' } },
      },
      series: speciesCompose.map((s: any) => {
        return {
          name: getMosName(s.name),
          type: 'bar',
          barGap: '10%',
          barWidth: '70%',
          stack: 'total',
          data: s.data.map((s: { count: number }) => s.count),
          itemStyle: {
            color: getMosColor(s.name),
          },
        };
      }),
    };
    return optData;
  }, [speciesComposeRes, speciesCompose]);

  const totalCount = useMemo(() => {
    let count = 0;
    speciesComposeRes?.trend_points?.forEach((item: any) => {
      count += item.total_count;
    });
    return count || 0;
  }, [speciesComposeRes]);

  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <IconRegionForm className="text-[#4C84FF]"></IconRegionForm>
          <span>监测区域蚊虫构成比分析</span>
          <span className="text-[12px] text-[#999]">
            （该模块展示的是监测区域蚊种构成随时间的变化趋势）
          </span>
        </span>
      }
    >
      <DataSearchForm menu={menu} onRequest={getSpeciesCompose}></DataSearchForm>
      <div className="text-[14px] text-txt-main">蚊虫监测总数：{totalCount}只</div>
      <div className="w-full h-[400px] flex items-center justify-center">
        {speciesCompose?.length > 0 ? (
          <EChartsCommon option={option} />
        ) : (
          <div className="w-full h-full flex items-center justify-center ">
            {loading ? <Spin /> : <span className="text-txt-sub">暂无数据</span>}
          </div>
        )}
      </div>
    </Panel>
  );
};

export default CompositionPanel;
