/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 18:27:10
 * @LastEditTime: 2025-01-14 22:34:37
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconMultipleModal = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2 18H22M2 18L5 15M2 18L5 21M22 18L19 15M22 18L19 21M7 3H17M12 3V14"
        stroke="#2B65F6"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
