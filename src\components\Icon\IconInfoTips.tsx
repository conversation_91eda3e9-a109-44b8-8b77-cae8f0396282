/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-11 08:56:28
 * @LastEditTime: 2025-01-14 22:33:57
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconInfoTips = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="13"
      height="14"
      viewBox="0 0 13 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="13" height="13" rx="6.5" fill="#5DFE5A" />
      <path
        d="M8 7.5H5M6 9.5H5M9 5.5H5"
        stroke="#0F0F2F"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
