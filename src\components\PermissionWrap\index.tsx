import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import { Button, ButtonProps } from 'antd';
import React from 'react';

export interface IPermissionWrap {
  accessCode?: STATIC_PERMISSION_CODE;
  children?: any;
  useDisabled?: boolean;
  useVisible?: boolean;
}
export default function PermissionWrap(props: IPermissionWrap) {
  const { accessCode, useDisabled, children, useVisible = false } = props;
  if (accessCode && !checkFunPermission(`${accessCode}`)) {
    if (useDisabled) {
      return React.cloneElement(children, { ...children.props, disabled: true });
    }
    if (!useVisible) {
      return null;
    }
    return (
      <span
        style={{
          visibility: 'hidden',
          pointerEvents: 'none',
        }}
      >
        {React.cloneElement(children, {
          ...children.props,
        })}
      </span>
    );
  }
  return props.children || null;
}

export const PermissionButton = (props: ButtonProps & IPermissionWrap) => {
  const { accessCode, useDisabled, children, ...others } = props;
  return (
    <PermissionWrap accessCode={accessCode} useDisabled={useDisabled}>
      <Button {...others}>{children}</Button>
    </PermissionWrap>
  );
};
