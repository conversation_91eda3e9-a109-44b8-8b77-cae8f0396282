/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-08 15:38:38
 * @LastEditTime: 2025-02-12 23:20:15
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export interface IRegionDeviceList {
  key?: string;
  name: string;
  level: string;
  children: IRegionDeviceList[];
  devices: IRegionDevice[];
}

export interface IRegionDevice {
  id: number;
  key?: string;
  tenant_id: number;
  name: string;
  code: string;
  status: number;
  gps: string;
  temp: number;
  hum: number;
  speed: number;
  co2: number;
}

export interface IRegion {
  province?: string;
  city?: string;
  district?: string;
  street?: string;
  device_id?: string;
  lat?: number;
  lng?: number;
  zoom?: number;
}
