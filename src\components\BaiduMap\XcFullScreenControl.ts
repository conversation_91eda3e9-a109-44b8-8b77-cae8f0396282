/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-12 19:31:21
 * @LastEditTime: 2025-02-12 19:31:33
 * @LastEditors: <PERSON><PERSON>
 * @Description: 
 */
import { px2font } from '../LargeScreen/vw/vw';

export class FullScreenControl extends BMapGL.Control {
  constructor(toggleFullscreen: () => void) {
    super();
    this.toggleFullscreen = toggleFullscreen;
    this.defaultAnchor = BMAP_ANCHOR_BOTTOM_RIGHT;
    this.defaultOffset = new BMapGL.Size(20, 20);
  }

  private toggleFullscreen: any;

  initialize(map: any) {
    //创建一个dom元素
    let div = document.createElement('div');
    // div增加svg元素
    // 设置样式
    div.style.cursor = 'pointer';
    div.style.fontSize = px2font(14);
    div.style.width = px2font(36);
    div.style.height = px2font(36);
    div.style.display = 'flex';
    div.style.justifyContent = 'center';
    div.style.alignItems = 'center';
    div.style.borderRadius = px2font(4);
    div.style.backgroundColor = 'rgba(20, 20, 44, 0.5)';
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', px2font(26));
    svg.setAttribute('height', px2font(26));
    svg.setAttribute('viewBox', '0 0 14 14');
    svg.setAttribute('fill', 'none');
    svg.setAttribute('xmlns', 'http://www.w3.org/2000/svg');

    const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    const path =
      'M13 8.33333V9.8C13 10.9201 13 11.4802 12.782 11.908C12.5903 12.2843 12.2843 12.5903 11.908 12.782C11.4802 13 10.9201 13 9.8 13H8.33333M5.66667 1H4.2C3.0799 1 2.51984 1 2.09202 1.21799C1.71569 1.40973 1.40973 1.71569 1.21799 2.09202C1 2.51984 1 3.0799 1 4.2V5.66667M9 5L13 1M13 1H9M13 1V5M5 9L1 13M1 13H5M1 13L1 9';
    pathElement.setAttributeNS(null, 'd', path);
    pathElement.setAttributeNS(null, 'stroke', '#C6C6E3');
    pathElement.setAttributeNS(null, 'strokeLinecap', '#C6C6E3');
    pathElement.setAttributeNS(null, 'strokeLinejoin', '#C6C6E3');

    svg.appendChild(pathElement);
    div.appendChild(svg);

    // 绑定事件-全屏
    div.onclick = () => {
      this.toggleFullscreen();
    };

    // 添加DOM元素到地图中
    map.getContainer().appendChild(div);
    // 将DOM元素返回
    return div;
  }
}
