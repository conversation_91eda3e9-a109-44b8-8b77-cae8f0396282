import { create } from 'zustand';

export interface ChatItem {
  id: number;
  title: string;
  content: string;
}
export interface MessageItem {
  id: number;
  msgType?: string;
  role?: string;
  content?: string;
  ext?: any;
}

export enum AI_STATUS {
  idle = 'idle',
  thinking = 'thinking',
  searching = 'searching',
  error = 'error',
  responding = 'responding',
}
export interface ChatState {
  chatList: ChatItem[];
  selectedId: any;
  messageList: MessageItem[];
  deepThink: boolean;
  onlineSearch: boolean;
  instructInfo?: { instructType: string; data?: any } | null;
  aiStatus?: AI_STATUS;
  setChatList: (list: ChatItem[]) => void;
  setSelectedId: (id: number) => void;
  updateChatMsg: (msg: ChatItem) => void;
  deleteChatMsg: (id: number) => void;
  clearChatMsg: () => void;
  addChatMsg: (msg: ChatItem) => void;
  setMessageList: (list: MessageItem[]) => void;
  updateMessage: (msg: MessageItem) => void;
  deleteMessage: (id: number) => void;
  updateChatState: (state: Partial<ChatState>) => void;
}
const chatState = {
  instructInfo: null,
  deepThink: false,
  onlineSearch: false,
  aiStatus: AI_STATUS.idle,
  chatList: [
    {
      id: 1,
      title: '今天的天气怎么样',
      content: '今天的天气怎么样',
    },
    {
      id: 2,
      title: '查看监测大屏数据',
      content: '查看监测大屏数据',
    },
    {
      id: 3,
      title: '查询设备列表',
      content: '查询设备列表',
    },
    {
      id: 4,
      title: '查询用户列表',
      content: '查询用户列表',
    },
    {
      id: 5,
      title: '查询部门列表',
      content: '查询部门列表',
    },
    {
      id: 6,
      title: '查询角色列表',
      content: '查询角色列表',
    },
    {
      id: 7,
      title: '查询监测记录',
      content: '查询监测记录',
    },
    {
      id: 8,
      title: '查询规则记录',
      content: '查询规则记录',
    },
    {
      id: 9,
      title: '查询验证日志',
      content: '查询验证日志',
    },
    {
      id: 10,
      title: '查询操作日志',
      content: '查询操作日志',
    },
  ],
  selectedId: 0,
  messageList: [
    {
      id: 1,
      role: 'user',
      content: '今天的天气怎么样',
    },
    {
      id: 2,
      role: 'ai',
      content: '好的，我来帮你查询一下天气。',
      ext: {
        date: '2025.06.01', // 日期
        temperature: '22℃', // 温度
        weather: '晴', // 天气
        wind: '无风', // 风
        humidity: '60%', // 湿度
        pressure: '1013hPa', // 压力
        visibility: '10km', // 可见度
        uvIndex: '2', // 紫外线指数
        airQuality: '良', // 空气质量
        holiday: '儿童节', // 节日
      },
    },
    {
      id: 3,
      role: 'ai',
      msgType: 'thinking',
      content: '',
    },
    {
      id: 4,
      role: 'ai',
      msgType: 'image',
      content: '',
      ext: {
        imageList: [
          {
            url: 'https://aiaas.com.cn/static/img_about_jzg_1.362eb977.webp',
          },
          {
            url: 'https://aiaas.com.cn/static/img_about_jzg_2.72302af5.webp',
          },
          {
            url: 'https://aiaas.com.cn/static/img_about_jzg_3.bdad090d.webp',
          },
          {
            url: 'https://aiaas.com.cn/static/img_about_jzg_4.4e0e4160.webp',
          },
        ],
      },
    },
    {
      id: 5,
      role: 'ai',
      msgType: 'search',
      content:
        '正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容正在生成的内容...',
    },
  ],
};

export const useChatStore = create<ChatState>((set) => ({
  ...chatState,
  updateChatState: (state: ChatState) => set({ ...state }),
  setChatList: (list: ChatItem[]) => set({ chatList: list }),
  setSelectedId: (id: number) => set({ selectedId: id }),
  updateChatMsg: (msg: ChatItem) => {
    set((prevState: ChatState) => {
      return {
        chatList: prevState.chatList.map((item) =>
          item.id === msg.id ? { ...item, ...msg } : item,
        ),
      };
    });
  },
  deleteChatMsg: (id: number) => {
    set((prevState: ChatState) => {
      return {
        chatList: prevState.chatList.filter((item) => item.id !== id),
      };
    });
  },
  clearChatMsg: () => {
    set({ messageList: [] });
  },
  addChatMsg: (msg: ChatItem) => {
    set((prevState: ChatState) => {
      return {
        chatList: [msg, ...prevState.chatList],
      };
    });
  },
  setMessageList: (list: MessageItem[]) => set({ messageList: list }),
  updateMessage: (msg: MessageItem) => {
    set((prevState: ChatState) => {
      return {
        messageList: prevState.messageList.map((item) =>
          item.id === msg.id ? { ...item, ...msg } : item,
        ),
      };
    });
  },
  deleteMessage: (id: number) => {
    set((prevState: ChatState) => {
      return {
        messageList: prevState.messageList.filter((item) => item.id !== id),
      };
    });
  },
  appendMessageList: (list: MessageItem[], forward: boolean = false) => {
    set((prevState: ChatState) => {
      return {
        messageList: forward
          ? [...list, ...prevState.messageList]
          : [...prevState.messageList, ...list],
      };
    });
  },
}));
