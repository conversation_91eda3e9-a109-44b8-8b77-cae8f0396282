import { useState } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-20 21:18:57
 * @LastEditTime: 2025-02-13 00:05:01
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const useMonitor = () => {
  const [overview, setOverview] = useState<any>({
    total_devices: 156, // 区域内设备总数
    online_devices: 142, // 在线设备数
    offline_devices: 14, // 离线设备数
    total_count: 142970,
    sub_regions: [
      // 子区域统计
      {
        name: '蒋村街道',
        level: 'street',
        total_devices: 45,
        online_devices: 42,
        offline_devices: 3,
      },
      {
        name: '文新街道',
        level: 'street',
        total_devices: 38,
        online_devices: 35,
        offline_devices: 3,
      },
    ],
    last_day: {
      // 近1天统计
      total_records: 15680, // 24小时内的检测总数
      top_data_type: 'PM2.5', // 监测最多的种类
      top_data_count: 5230, // 该种类的监测总数
      top_device_id: 'DEV2024031001',
      top_device_name: '古荡街道01号检测点',
      top_device_count: 1450, // 该设备的监测数量
    },
    last_week: {
      // 近7天统计
      total_records: 89760,
      top_data_type: 'PM10',
      top_data_count: 32150,
      top_device_id: 'DEV2024031002',
      top_device_name: '蒋村街道05号检测点',
      top_device_count: 8960,
    },
    last_month: {
      // 近30天统计
      total_records: 385920,
      top_data_type: 'PM2.5',
      top_data_count: 128640,
      top_device_id: 'DEV2024031003',
      top_device_name: '文新街道03号检测点',
      top_device_count: 35680,
    },
  });

  // 前几的数据
  const [topRank, setTopRank] = useState<
    {
      data_type: string;
      total_count: number;
      points: any[];
    }[]
  >([]);
  const [deviceOverview, setDeviceOverview] = useState({
    total_devices: 0,
    online_devices: 0,
    offline_devices: 0,
    online_rate: 0,
  });
  const [tab, setTab] = useState('3');

  return {
    tab,
    setTab,
    overview,
    setOverview,
    deviceOverview,
    setDeviceOverview,
    topRank,
    setTopRank,
  };
};

export default useMonitor;
