import { device<PERSON><PERSON> } from '@/api/device';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { COLUMN_RENDER_TYPE, IColumns } from '@/components/XcTable/interface/search.interface';
import { CustomTableBodyItemStatus } from '@/components/XcTable/XcTableBodyItemStatus';
import { DEVICE_STATUS, GLOBAL_MODAL_TYPE } from '@/constants/common';
import { ResponseErrorInfo } from '@/requestErrorConfig';
import { sleep } from '@/utils/common.util';
import { isBlank } from '@/utils/string.util';
import { DATE_FMT } from '@/utils/time.util';
import { ProFormText } from '@ant-design/pro-components';
import { request, useRequest } from '@umijs/max';
import { Button, message, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import AddOrEdit from './components/AddOrEdit';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import { IconAdd } from '@/assets/svg';

const { Text } = Typography;

export default () => {
  const tableRef = useRef<ITableRef>(null);
  const [messageApi, contextHolder] = message.useMessage();
  const [powerChangeVisible, setPowerChangeVisible] = useState(false);

  const statRef = useRef<any>(null);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({
    env_info: {},
    monitor: {},
  });
  const [modalType, setModalType] = useState<GLOBAL_MODAL_TYPE>(GLOBAL_MODAL_TYPE.none);
  /**
   * 监测网列表请求接口
   */
  const deviceListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: deviceAPI.getDeviceGroupList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  // 刷新数据
  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
      statRef.current.refresh();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };
  /**
   * 更新监测网请求接口
   */
  const deviceStatusRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: deviceAPI.putDeviceGroupStatus(id),
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 切换监测网状态
  const onSwitchStatusRequest = async (
    device_id: any,
    { tenant_id, status, offline_reason }: any,
  ) => {
    if (!device_id) {
      messageApi.error('监测网ID不能为空');
      return;
    }
    if (!tenant_id) {
      messageApi.error('客户ID不能为空');
      return;
    }
    try {
      const switchResult = await deviceStatusRequest.run(device_id, {
        tenant_id: tenant_id,
        status: status,
        offline_reason: offline_reason,
      });
      if (switchResult.code === 200) {
        messageApi.success(`${status === 1 ? '启动监测' : '停止监测'}成功`);
        await sleep(1000);
        queryTableData();
        return true;
      }
      messageApi.error(switchResult.msg ?? switchResult.message);
      return false;
    } catch (error) {
      messageApi.error(
        (error as ResponseErrorInfo).info.msg ?? (error as ResponseErrorInfo).info.message,
      );
      return false;
    }
  };

  const requestTable = async (params: any) => {
    const { range_time, status, code, current, pageSize } = params;
    const payload: Record<string, any> = {
      status,
      code,
      page: current,
      page_size: pageSize,
    };
    if (status === '0') {
      delete payload.status;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    }

    const result = await deviceListRequest.run({
      ...payload,
      tenant_id: 1,
    });
    if (result.code === 200) {
      return {
        total: result.data.total,
        data: result.data.list,
      };
    } else {
      message.error(result.message || '请求失败');
    }
    return {
      total: 0,
      data: [],
    };
  };

  // 操作-启停监测
  const onMonitorChange = async (item: any) => {
    setCurrentItem(item);
    if (item.status === 1) {
      setPowerChangeVisible(true);
    } else {
      await onSwitchStatusRequest(item.id, {
        tenant_id: item.tenant_id,
        status: 1,
        offline_reason: '启动监测',
      });
    }
  };

  const showModal = async (item: any, type: GLOBAL_MODAL_TYPE) => {
    setCurrentItem(item);
    setModalType(type);
  };

  const closeModal = () => {
    setModalType(GLOBAL_MODAL_TYPE.none);
  };
  const toggleModalVisible = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };
  const clickDeleteDevice = async () => {
    request(deviceAPI.deviceGroupById(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  const columns: IColumns[] = [
    {
      title: '监测状态',
      width: 120,
      key: 'status',
      hideInTable: true,
      initialValue: '0',
      valueType: 'drop',
      realtime: true,
      valueEnum: {
        0: '全部状态',
        1: '监测中',
        2: '未监测',
      },
    },
    {
      title: '监测网名称',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索监测网名称',
    },
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '监测网名称',
      dataIndex: 'name',
      key: 'name',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '区域',
      dataIndex: 'area',
      key: 'area',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '监测网数量',
      dataIndex: 'device_count',
      key: 'device_count',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '在线监测网',
      dataIndex: 'device_count',
      key: 'device_count',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      key: 'owner',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '操作时间',
      dataIndex: 'created_at',
      key: 'created_at',
      hideInSearch: true,
      render: (_, item: { created_at: any }) => {
        return <span>{dayjs(item.created_at).format('YYYY.MM.DD')}</span>;
      },
    },
    {
      title: '监测状态',
      dataIndex: 'status',
      key: 'status',
      hideInSearch: true,
      render: (_, item: { status: number }) => {
        return (
          <CustomTableBodyItemStatus
            status={item.status === 1 ? 'success' : 'default'}
            text={DEVICE_STATUS[item.status]}
          />
        );
      },
    },
    {
      title: '操作',
      width: 210,
      link: true,
      fixed: 'right',
      align: 'center',
      hideInSearch: true,
      render: (_, item: any) => {
        const monitor = item.status === 1;
        return (
          <div className={'flex items-center justify-end gap-[16px]'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新监测网状态}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => {
                  onMonitorChange(item);
                }}
              >
                {monitor ? '停用' : '启用'}
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新监测网}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, GLOBAL_MODAL_TYPE.edit)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除监测网}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, GLOBAL_MODAL_TYPE.delete)}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex ">
      {contextHolder}
      <div className="flex-1 mb-[16px]">
        <XcTableNew
          ref={tableRef}
          loading={deviceListRequest.loading}
          columns={columns}
          request={requestTable}
          extend={null}
          rowSelection={null}
          searchTitle="监测网清单"
          operator={
            <>
              <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建监测网}>
                <Button
                  type="primary"
                  className={
                    'w-[124px] h-[40px] rounded-[8px] bg-btn-blue text-txt-white text-[14px]'
                  }
                  icon={<IconAdd className="!align-middle" />}
                  onClick={() => showModal({}, GLOBAL_MODAL_TYPE.add)}
                >
                  <span className={'!self-center'}>新增监测网</span>
                </Button>
              </PermissionWrap>
            </>
          }
        />
      </div>
      {GLOBAL_MODAL_TYPE.edit === modalType || GLOBAL_MODAL_TYPE.add === modalType ? (
        <AddOrEdit
          modalType={modalType}
          onClose={closeModal}
          visible={true}
          queryTableData={queryTableData}
          currentItem={currentItem}
        />
      ) : null}
      <XcModalForm
        title="确定停止监测？"
        autoFocusFirstInput
        width={500}
        open={powerChangeVisible}
        onOpenChange={setPowerChangeVisible}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
        }}
        onFinish={async (values) => {
          const { reason } = values;
          return await onSwitchStatusRequest(currentItem.id, {
            tenant_id: currentItem.tenant_id,
            status: 2,
            offline_reason: reason,
          });
        }}
      >
        <div className="my-[40px]">
          <ProFormText
            name={'reason'}
            required={false}
            rules={[{ required: true, message: '请填写停止监测原因...' }]}
            placeholder={'请填写停止监测原因...'}
          />
        </div>
      </XcModalForm>
      <XcModalForm
        title="删除监测网"
        autoFocusFirstInput
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.delete}
        onOpenChange={toggleModalVisible}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
        }}
        onFinish={clickDeleteDevice}
      >
        <div className="my-[40px]">该组网已关联预警规则，删除后关联将消失，是否继续？</div>
      </XcModalForm>
    </div>
  );
};
