import './WelcomeMessage.css';
import weatherImg from './weather-type-1.png';
import SmartMessage from '../SmartMessage/SmartMessage';
import ChartMessage from '../ChartMessage/ChartMessage';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import { request, useModel } from '@umijs/max';
import { CompositionChart } from '../ChartMessage';
import { useEffect, useState } from 'react';
import { ewsAPI } from '@/api';
import dayjs from 'dayjs';
import { ak } from '../../../../../config/baidu';

const localStorageKey = 'today-show-welcome-message';
export default function WelcomeMessage() {
  const [startTime, endTime] = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
  const { defaultRegion } = useModel('useDevice', (m) => ({
    defaultRegion: m.defaultRegion,
  }));
  const [ewsList, setEwsList] = useState<any[]>([]);
  const [visible, setVisible] = useState(false);
  const getWeather = () => {
    request(`https://api.map.baidu.com/weather/v1/?district_id=440300&data_type=now&ak=${ak}`, {
      method: 'GET',
    })
      .then((res) => {
        console.log('weather  ', res.data);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  useEffect(() => {
    // getWeather();
  }, []);
  useEffect(() => {
    request(ewsAPI.getEwsList, {
      params: {
        start_time: startTime,
        end_time: endTime,
        page: 1,
        page_size: 10,
        tenant_id: 1,
      },
    })
      .then((res) => {
        let list = res?.data?.list || [];
        list = list.slice(0, 6);
        setEwsList(list);
      })
      .catch((err) => {
        console.log(err);
      });
  }, []);

  useEffect(() => {
    // const last = localStorage.getItem(localStorageKey);
    const last = '';
    const nowDay = dayjs().format(DATE_FMT.DAY);
    if (last !== nowDay) {
      setVisible(true);
    } else {
      setVisible(false);
    }

    return () => {
      localStorage.setItem(localStorageKey, dayjs().format(DATE_FMT.DAY));
    };
  }, []);

  if (!visible) {
    return null;
  }

  return (
    <div className="WelcomeMessage">
      {visible && (
        <div className="WelcomeMessage-head ">
          <div className="WelcomeMessage-headAvatar"></div>
          <div className="WelcomeMessage-headText">今天我能如何帮助您？</div>
        </div>
      )}
      <div className="WelcomeMessage-cont">
        <div className="WelcomeMessage-weather">
          <div className="WelcomeMessage-title">
            <div className="WelcomeMessage-icon is-weather"></div>
            <div className="WelcomeMessage-text">今天天气</div>
          </div>
          <div className="WelcomeMessage-weatherContent">
            <div className="WelcomeMessage-weatherDetail">
              <div
                className="WelcomeMessage-weatherImg"
                style={{
                  backgroundImage: `url(${weatherImg})`,
                }}
              ></div>
              <div className="WelcomeMessage-weatherDate">
                <div className="WelcomeMessage-weatherDay">01</div>
                <div className="WelcomeMessage-weatherHoliday">儿童节</div>
              </div>
              <div className="WelcomeMessage-weatherInfo">
                <div className="WelcomeMessage-weatherTemp">16℃</div>
                <div className="WelcomeMessage-weatherText">多云转阴</div>
                <div className="WelcomeMessage-weatherAir">
                  2025.06.01 空气质量：<span className="text-[#43C432]">优</span>
                </div>
              </div>
            </div>
            <div className="WelcomeMessage-weatherAiList">
              <SmartMessage
                tag="智能任务"
                content="针对今天的天气帮我罗列潜在隐患列潜在隐患隐患列潜在隐患隐患列潜在隐患隐患列潜在隐患"
              />
              <SmartMessage
                tag="智能任务"
                content="针对今天的天气帮我罗列潜在隐患列潜在隐患隐患列潜在隐患隐患列潜在隐患隐患列潜在隐患"
              />
            </div>
          </div>
        </div>
        <div className="WelcomeMessage-overview">
          <div className="WelcomeMessage-title">
            <div className="WelcomeMessage-icon is-charts"></div>
            <div className="WelcomeMessage-text">智能数据总结</div>
          </div>
          <div className="WelcomeMessage-overviewList">
            <CompositionChart startTime={startTime} endTime={endTime} range={defaultRegion} />
            <ChartMessage />
          </div>
          <div className="WelcomeMessage-ewsList">
            {ewsList.map((item: any, index: number) => {
              return (
                <div className="WelcomeMessage-ewsItem" key={index}>
                  <SmartMessage tag="预警信息" content={`${item.device_name} ${item.condition}`} />
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
