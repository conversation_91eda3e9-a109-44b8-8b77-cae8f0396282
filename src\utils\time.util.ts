import dayjs from 'dayjs';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-12 16:08:25
 * @LastEditTime: 2024-11-12 16:08:32
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const DATE_FMT = {
  DATETIME_LOCAL: 'YYYY-MM-DDTHH:mm',
  DATETIME_LOCAL_SECONDS: 'YYYY-MM-DDTHH:mm:ss',
  DATETIME_LOCAL_MS: 'YYYY-MM-DDTHH:mm:ss.SSS',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
  DAY: 'YYYY-MM-DD',
  MD: 'MM-DD',
  TIME: 'HH:mm',
  TIME_SECONDS: 'HH:mm:ss',
  DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
};

export enum TIME_UNITS {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
  HOUR = 'hour',
}

export const TIME_UNITS_LIST = [
  {
    label: '小时',
    value: TIME_UNITS.HOUR,
  },
  {
    label: '天',
    value: TIME_UNITS.DAY,
  },
  {
    label: '周',
    value: TIME_UNITS.WEEK,
  },

  {
    label: '月',
    value: TIME_UNITS.MONTH,
  },
];

export function getRangeByTimeUnit(unit?: TIME_UNITS): [dayjs.Dayjs, dayjs.Dayjs] {
  const timeUnit = unit || TIME_UNITS.MONTH;
  if (timeUnit === TIME_UNITS.MONTH) {
    return [dayjs().subtract(1, 'month').add(1, 'day').startOf('day'), dayjs().endOf('day')];
  } else if (timeUnit === TIME_UNITS.WEEK) {
    return [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
  } else if (timeUnit === TIME_UNITS.DAY) {
    return [dayjs().startOf('day'), dayjs().endOf('day')];
  } else {
    return [dayjs().startOf('day'), dayjs().endOf('day')];
  }
}

export function getRangeByTimeUnitAndFormat(
  unit?: TIME_UNITS,
  format?: (typeof DATE_FMT)[keyof typeof DATE_FMT],
): [string, string] {
  const range = getRangeByTimeUnit(unit);
  return range.map((item: dayjs.Dayjs) => item.format(format)) as [string, string];
}

export function getWeekDates(weekString: string) {
  // 解析输入的年份和周数
  const [year, week] = weekString.split('-').map(Number);

  // 创建当年1月1日的日期对象
  const firstDayOfYear = new Date(year, 0, 1);

  // 计算1月1日是星期几（0是星期日，1是星期一...6是星期六）
  // 转换为ISO周几（1是星期一，7是星期日）
  let dayOfWeek = firstDayOfYear.getDay() || 7;

  // 计算第一周的第一天（星期一）是哪一天
  const firstMonday = new Date(firstDayOfYear);
  firstMonday.setDate(firstDayOfYear.getDate() - (dayOfWeek - 1));

  // 计算目标周的第一天（星期一）
  const startOfWeek = new Date(firstMonday);
  startOfWeek.setDate(firstMonday.getDate() + (week - 1) * 7);

  // 计算目标周的最后一天（星期日）
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);

  // 格式化日期为"MM.DD"格式
  const formatDate = (date) => {
    const month = date.getMonth() + 1; // 月份从0开始
    const day = date.getDate();
    return `${month}.${day}`;
  };

  return {
    start: formatDate(startOfWeek),
    end: formatDate(endOfWeek),
    fullStart: new Date(startOfWeek), // 返回完整的Date对象
    fullEnd: new Date(endOfWeek), // 返回完整的Date对象
  };
}

export function formXLabelByTimeUnit(str: string, unit?: TIME_UNITS): string {
  let result = str;
  switch (unit) {
    case TIME_UNITS.HOUR:
      result = `${+str < 10 ? '0' + str : str}:00`;
      break;
    case TIME_UNITS.DAY:
      result = str.replace(/^[\d-]{5}/, '');
      break;
    case TIME_UNITS.WEEK:
      result = `${getWeekDates(str).start}-${getWeekDates(str).end}`;
      break;
    case TIME_UNITS.MONTH:
      break;
    default:
      break;
  }
  return result;
}
