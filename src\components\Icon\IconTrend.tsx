/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:35:47
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconTrend = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#7D3FFF" />
      <path
        d="M15.5568 15.5556V12.4444M12.0013 15.5556V11.1111M8.44572 15.5556L8.44572 13.7778M12.6265 8.90111L14.9235 9.76252M11.4674 9.06708L8.97915 10.9333M16.0282 9.5286C16.2886 9.78894 16.2886 10.2111 16.0282 10.4714C15.7679 10.7318 15.3458 10.7318 15.0854 10.4714C14.8251 10.2111 14.8251 9.78894 15.0854 9.5286C15.3458 9.26825 15.7679 9.26825 16.0282 9.5286ZM8.91712 10.8619C9.17747 11.1223 9.17747 11.5444 8.91712 11.8047C8.65677 12.0651 8.23466 12.0651 7.97431 11.8047C7.71397 11.5444 7.71397 11.1223 7.97431 10.8619C8.23466 10.6016 8.65677 10.6016 8.91712 10.8619ZM12.4727 8.19526C12.733 8.45561 12.733 8.87772 12.4727 9.13807C12.2123 9.39842 11.7902 9.39842 11.5299 9.13807C11.2695 8.87772 11.2695 8.45561 11.5299 8.19526C11.7902 7.93491 12.2123 7.93491 12.4727 8.19526Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
