import { request, useRequest } from '@umijs/max';
import { But<PERSON>, message } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { MosquitoAPI } from '@/api';
import { permissionApi } from '@/api/permission';
import { getAllMosqutio } from '@/utils/mosqutio.util';
import { ReloadOutlined } from '@ant-design/icons';
import { MqttDetail } from './MqttDetail';
import XcAntdText from '@/components/XcAntdText';
import { useMemoizedFn } from 'ahooks';
import { GLOBAL_MODAL_TYPE } from '@/constants/common';
import DraggableImageModal from '@/components/DragAbleImageModal/DragAbleImageModal';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { XcTableNew } from '@/components/XcTable/XcTableNew';
import PermissionWrap from '@/components/PermissionWrap';
import {
  AUDIT_VERIFY_DATA_STATUS,
  mosImg,
  otherImg,
  searchImg,
  verifyFailImg,
  verifySuccessImg,
  verifyWaitImg,
} from './constant';
import useMosquitoModel from '@/models/useMosquito';
import XcMosSelect from '@/components/XcMosSelect';

interface IData {
  id: number;
  device_code: string;
  device_name: string;
  monitor_location: string;
  area_type: string;
  ai_result: string;
  image_url: string;
  created_at: string;
  status: number; // -1 没有验证结果数据。 1 待专家验证 2. AI验证通过 3. 专家已验证
  auditor_id?: number;
  auditor_name?: string;
  audit_time?: string;
  other_result?: string;
}

export default () => {
  const { mosquitoList } = useMosquitoModel();
  const tableRef = useRef<any>(null);
  const [lockArea, setLockArea] = useState(false);
  const [previewSrc, setPreviewSrc] = useState('');
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [modalType, setModalType] = useState<GLOBAL_MODAL_TYPE>(GLOBAL_MODAL_TYPE.none);

  const auditListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: MosquitoAPI.getAuditList,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );
  const requestTable = useMemoizedFn(async (params: any) => {
    const { range_time, current, pageSize, ...others } = params || {};

    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
      ...others,
    };
    if (payload.mosquito_type === 'all' || !payload.mosquito_type) {
      delete payload.mosquito_type;
    }
    if (payload.type === 'all') {
      delete payload.type;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    }
    const result = await auditListRequest.run({
      ...payload,
      tenant_id: 1,
    });

    if (result.code === 200) {
      const list = Array.isArray(result?.data?.list) ? result.data.list : [];
      list.forEach((item: any, index: number) => {
        item.index = (current - 1) * pageSize + index + 1;
        item.pageIndex = index;
      });
      return {
        total: result?.data?.total || 0,
        data: list,
      };
    } else {
      message.error(result.message || '请求失败');
    }
    return {
      total: 0,
      data: [],
    };
  });

  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };

  const hidePicModal = () => {
    setPreviewSrc('');
    setLockArea(false);
  };

  const showPicModal = (src: string, lockArea?: boolean) => {
    setLockArea(lockArea || false);
    setPreviewSrc(src);
  };

  const onVisibleChange = (value: boolean, prevValue?: boolean): void => {
    if (!value) {
      hidePicModal();
    }
  };

  const showModal = async (item: any, type: GLOBAL_MODAL_TYPE) => {
    setCurrentItem(item);
    setModalType(type);
  };

  const closeModal = () => {
    setPreviewSrc('');
    setCurrentItem({});
    setModalType(GLOBAL_MODAL_TYPE.none);
  };

  const toggleModalVisible = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const showDeleteModal = useMemoizedFn((item: any) => {
    setDeleteModalVisible(true);
  });
  const closeDeleteModal = useMemoizedFn((visible?: boolean) => {
    if (!visible) {
      if (deleteModalVisible) {
        setDeleteModalVisible(false);
      } else {
        toggleModalVisible(visible || false);
      }
    }
  });
  const clickDelete = useMemoizedFn(async () => {
    request(MosquitoAPI.deleteAudit, {
      method: 'POST',
      data: {
        tenant_id: 1,
        task_id: currentItem.task_id,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        setDeleteModalVisible(false);
        queryTableData();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  });

  const canVerify = checkFunPermission(STATIC_PERMISSION_CODE.专家验证);
  const columns = [
    {
      title: '监测状态',
      width: 160,
      key: 'type',
      hideInTable: true,
      initialValue: 'all',
      valueType: 'drop',
      valueEnum: [
        { label: '全部状态', value: 'all' },
        { label: '待专家验证', value: AUDIT_VERIFY_DATA_STATUS.待专家验证 },
        { label: 'AI验证通过', value: AUDIT_VERIFY_DATA_STATUS.AI验证通过 },
        { label: '专家已验证', value: AUDIT_VERIFY_DATA_STATUS.专家已验证 },
        { label: 'AI验证中', value: AUDIT_VERIFY_DATA_STATUS.AI验证中 },
      ],
    },
    {
      title: '蚊种',
      width: 160,
      key: 'mosquito_type',
      placeholder: '全部蚊种',
      hideInTable: true,
      render: (dom: any, record: any, config: any) => {
        return <XcMosSelect value={record.mosquito_type} style={{ width: 120 }} />;
      },
    },
    {
      title: '创建时间',
      key: 'range_time',
      realtime: true,
      // initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '搜索',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索数据编号/设备名称',
    },
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 100,
      render: (value: any) => {
        return <div className="h-full flex items-center ">{value}</div>;
      },
    },
    {
      title: '编号',
      dataIndex: 'id',
      key: 'id',
      width: 150,
      render: (value: any, item: any) => {
        return (
          <div
            className="h-full flex items-center cursor-pointer"
            onClick={() => {
              showModal(item, GLOBAL_MODAL_TYPE.edit);
            }}
          >
            {value || '--'}
          </div>
        );
      },
    },
    {
      title: '监测详情',
      dataIndex: 'detail',
      key: 'detail',
      width: 480,
      render: (_, item: any) => {
        return (
          <div className="flex items-center w-full overflow-hidden">
            <img
              src={
                item.ai_result?.length > 0
                  ? mosImg
                  : item.other_result?.length > 0
                    ? otherImg
                    : searchImg
              }
              alt=""
              className="size-[56px] object-cover flex-shrink-0 rounded-[8px]"
              onClick={(e) => {
                e.stopPropagation();
                showPicModal(item.image_url);
              }}
            />
            <div className="flex flex-col flex-1 mx-[12px] min-w-0">
              <XcAntdText
                className="flex flex-wrap group-hover:text-txt-blue cursor-pointer font-medium"
                onClick={() => {
                  showModal(item, GLOBAL_MODAL_TYPE.edit);
                }}
              >
                {item.device_name}
              </XcAntdText>
              <div className="mt-[4px] text-txt-sub text-one-row">
                {item.monitor_location} {item.area_type ? `(${item.area_type || '-'})` : ''}
              </div>
              <div className="mt-[4px] text-txt-sub">监测时间：{item.created_at}</div>
            </div>
          </div>
        );
      },
    },
    {
      title: '识别信息',
      dataIndex: 'ai_verify_result',
      key: 'ai_verify_result',
      maxWidth: 360,
      render: (_, item) => {
        // const verifyUser = item.auditor_name
        //   ? `${item.auditor_name}专家已验证`
        //   : `${DEFAULT_EXPERT_USER_NAME}已验证`;
        return (
          <div className="flex flex-col justify-center h-full font-medium">
            <div className="flex  items-center">
              {item.status === AUDIT_VERIFY_DATA_STATUS.AI验证通过 ? (
                <span className="flex items-center text-txt-main ">
                  <img src={verifySuccessImg} className="mr-[4px] size-[20px]" />
                  星小尘已完成识别
                </span>
              ) : item.status === AUDIT_VERIFY_DATA_STATUS.专家已验证 ? (
                <span className="flex items-center text-txt-main">
                  <img src={verifySuccessImg} className="mr-[4px] size-[20px]" />
                  专家已完成识别
                </span>
              ) : item.status === AUDIT_VERIFY_DATA_STATUS.待专家验证 ? (
                <span className="flex items-center text-sys-yellow flex-wrap">
                  <img src={verifyFailImg} className="mr-[4px] size-[20px]" />
                  星小尘已完成识别<span className="text-[#FF7818]">（有部分存疑，请专家识别）</span>
                </span>
              ) : (
                <span className="flex items-center text-sys-yellow">
                  <img src={verifyWaitImg} className="mr-[4px] size-[20px]" />
                  星小尘正加急识别中，请稍等...
                </span>
              )}
            </div>
            {item.status === AUDIT_VERIFY_DATA_STATUS.AI验证中 ? null : (
              <div className="text-txt-third font-normal">
                {item.auditor_name ? item.audit_time : item.created_at}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '识别结果',
      dataIndex: 'expert_verify_result',
      key: 'expert_verify_result',
      render: (_, item) => {
        if (!item.ai_result?.length && !item.other_result?.length) {
          return <div className="flex items-center  h-full text-txt-third">等待识别结果...</div>;
        }
        return (
          <div className="flex flex-col justify-center  h-full">
            <div className="flex flex-wrap gap-[8px]">
              {(item.ai_result || '').split(',').map((str: any, i: number) => {
                return (
                  <span key={i} className="mb-[4px] bg-[#F4F8FD] rounded-[4px] px-[8px]">
                    {str}
                  </span>
                );
              })}
            </div>
            {item?.other_result ? (
              <div className={' text-txt-third w-full text-one-row'} title={item.other_result}>
                其他：{item.other_result}
              </div>
            ) : null}
          </div>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'right',
      width: 110,
      render: (_, item) => {
        return (
          <div className={'flex items-center justify-end gap-[16px] h-full '}>
            <Button
              type="link"
              className="px-[0px]"
              onClick={() => {
                showModal(item, GLOBAL_MODAL_TYPE.edit);
              }}
            >
              {!canVerify ||
              item.status === AUDIT_VERIFY_DATA_STATUS.AI验证通过 ||
              item.status === AUDIT_VERIFY_DATA_STATUS.专家已验证
                ? '查看详情'
                : '专家识别'}
            </Button>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除监测记录}>
              <Button
                type="link"
                className="px-[0px]"
                onClick={() => {
                  showModal(item, GLOBAL_MODAL_TYPE.delete);
                }}
              >
                <span className={'!self-center'}>删除</span>
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    document.querySelectorAll('.ant-table-row').forEach((item, index) => {
      if (index === currentItem?.pageIndex) {
        item.classList.add('ant-table-row-selected');
      } else {
        item.classList.remove('ant-table-row-selected');
      }
    });
  }, [currentItem?.index]);

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        ref={tableRef}
        loading={auditListRequest.loading}
        columns={columns}
        request={requestTable}
        rowSelection={null}
        searchTitle="监测记录"
        operator={
          <>
            <Button
              type="primary"
              className={'rounded-[4px] bg-btn-blue text-txt-white text-[14px]'}
              icon={<ReloadOutlined className="size-[24px]  justify-center !align-middle" />}
              onClick={queryTableData}
            >
              <span className={'!self-center'}>刷新</span>
            </Button>
          </>
        }
      />
      {previewSrc ? (
        <DraggableImageModal
          visible={true}
          title="监测图片"
          src={previewSrc}
          onClose={() => {
            onVisibleChange(false);
          }}
          className={lockArea ? '!w-[calc(100vw-500px)]' : ''}
        ></DraggableImageModal>
      ) : null}
      {modalType === GLOBAL_MODAL_TYPE.edit ? (
        <MqttDetail
          modalType={modalType}
          onClose={closeModal}
          visible={true}
          queryTableData={queryTableData}
          currentItem={currentItem}
          showPicModal={showPicModal}
          mosqutioList={mosquitoList}
          showDeleteModal={showDeleteModal}
        ></MqttDetail>
      ) : null}
      <XcModalForm
        title="删除数据"
        autoFocusFirstInput
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.delete || deleteModalVisible}
        onOpenChange={closeDeleteModal}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
        }}
        onFinish={clickDelete}
      >
        <div className="my-[40px]">您确定删除数据编号({currentItem.id})吗？</div>
      </XcModalForm>
    </div>
  );
};
