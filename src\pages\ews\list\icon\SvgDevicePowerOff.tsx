/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-19 01:00:09
 * @LastEditTime: 2025-01-19 01:00:24
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgDevicePowerOff = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11.5 12L11.5 3" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
      <path
        d="M15.5405 5C18.1962 6.43935 20 9.25348 20 12.4894C20 17.1897 16.1944 21 11.5 21C6.80558 21 3 17.1897 3 12.4894C3 9.25348 4.80377 6.43935 7.45952 5"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};
