/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:33:08
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
  width?: number;
  height?: number;
  borderWidth?: number;
  color?: string;
}

export const IconEllipse2 = (props: IProps) => {
  const { className, borderWidth = 2, color = '#5EFF5A', width = 11, height = 12 } = props;
  return (
    <svg
      className={`xc-svg flex-shrink-0 ${className}`}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx={width / 2}
        cy={height / 2}
        r={(width - borderWidth) / 2}
        stroke={color}
        strokeWidth={borderWidth}
      />
    </svg>
  );
};
