import { useMemoizedFn } from 'ahooks';
import * as echarts from 'echarts';
import { EChartsType } from 'echarts';
import { RendererType } from 'echarts/types/src/util/types.js';
import { debounce } from 'lodash';
import { useEffect, useRef } from 'react';

type OptionType = {
  [T: string]: unknown;
};

const state = {
  width: '100%',
  height: '100%',
};

const EChartsCommon = (props: {
  renderer?: RendererType;
  notMerge?: boolean;
  lazyUpdate?: boolean;
  option: OptionType;
  isFullScreen?: boolean;
  itemSize?: number;
  instanceHandle?: (instance: EChartsType) => void;
}) => {
  const drawDomRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<EChartsType | null>(null);

  const dispose = () => {
    if (!chartRef.current) {
      return;
    }
    chartRef.current.dispose();
    chartRef.current = null;
  };

  const setOption = useMemoizedFn((option: OptionType) => {
    if (!chartRef.current) {
      return;
    }
    const notMerge = props.notMerge;
    const lazyUpdate = props.lazyUpdate;

    const width = drawDomRef.current?.offsetWidth || 1000;
    const itemSize = props.itemSize || 50;
    // @ts-ignore
    const length = option.xAxis?.data?.length || option.xAxis?.[0]?.data?.length || 0;
    option.dataZoom = [
      {
        type: 'inside', // 滑动条类型
        show: false, // 显示滑动条
        xAxisIndex: [0], // 指定作用于 x 轴
        start: 0, // 初始显示的起始位置（百分比）
        end: (width / itemSize / length) * 100, // 初始显示的结束位置（百分比）
        zoomLock: true,
      },
    ];
    chartRef.current.setOption(option, notMerge, lazyUpdate);
  });

  const resize = debounce(() => {
    if (chartRef.current) {
      setOption(props.option);
      chartRef.current.resize();
    }
  }, 50);
  // 初始化组件
  const initChart = (dom: HTMLDivElement | null) => {
    if (chartRef.current) return;
    if (!dom) return;
    // renderer 用于配置渲染方式 可以是 svg 或者 canvas
    const renderer = props.renderer || 'canvas';
    chartRef.current = echarts.init(dom, null, {
      renderer,
      width: 'auto',
      height: 'auto',
    });
    // 执行初始化的任务，例如注册地图
    if (props.instanceHandle) props.instanceHandle(chartRef.current);
    setOption(props.option);
    // 监听屏幕缩放，重新绘制 echart 图表
    window.addEventListener('resize', resize);
  };

  const initHandle = useMemoizedFn(() => {
    dispose();
    if (!chartRef.current) {
      initChart(drawDomRef.current);
    } else {
      setOption(props.option);
    }
  });

  useEffect(() => {
    // 组件卸载
    return () => {
      window.removeEventListener('resize', resize);
      dispose();
    };
  }, []);

  // 每次更新组件都重置
  useEffect(() => {
    initHandle();
  }, [props.option]);

  useEffect(() => {
    if (chartRef.current) {
      chartRef.current.resize();
    }
  }, [props.isFullScreen]);

  return (
    <div
      className="default-chart"
      ref={drawDomRef}
      style={{ width: state.width, height: state.height }}
    />
  );
};

export default EChartsCommon;
