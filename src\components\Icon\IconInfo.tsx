/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-11 08:56:28
 * @LastEditTime: 2025-01-14 22:33:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconInfo = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#2953FF" />
      <path
        d="M13 11.5H10M11 13.5H10M14 9.5H10M16 9.4V14.6C16 15.4401 16 15.8601 15.8365 16.181C15.6927 16.4632 15.4632 16.6927 15.181 16.8365C14.8601 17 14.4401 17 13.6 17H10.4C9.55992 17 9.13988 17 8.81901 16.8365C8.53677 16.6927 8.3073 16.4632 8.16349 16.181C8 15.8601 8 15.4401 8 14.6V9.4C8 8.55992 8 8.13988 8.16349 7.81901C8.3073 7.53677 8.53677 7.3073 8.81901 7.16349C9.13988 7 9.55992 7 10.4 7H13.6C14.4401 7 14.8601 7 15.181 7.16349C15.4632 7.3073 15.6927 7.53677 15.8365 7.81901C16 8.13988 16 8.55992 16 9.4Z"
        stroke="white"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
