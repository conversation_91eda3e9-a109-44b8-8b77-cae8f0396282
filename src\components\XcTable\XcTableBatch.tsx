import { commonStyles } from '@/constants/commonStyles';
import { Divider } from 'antd';
import { IconClose } from '../../assets/svg/index';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:41:16
 * @LastEditTime: 2025-01-30 01:20:35
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  total: number;
  onDelete: () => void;
  onReset: () => void;
  batchRender?: () => React.ReactNode;
}

export const XcTableBatch = (props: IProps) => {
  const { total, onReset, batchRender } = props;

  return (
    <div className="ml-[20px] h-[80px] flex items-center">
      <div className={'flex items-center justify-center size-[12px]'} onClick={onReset}>
        <IconClose className={'size-[24px] cursor-pointer text-txt-sub'} />
      </div>
      <div className={`flex ${commonStyles.normalText14} ml-[16px]`}>
        <span className={`${commonStyles.normalText14}`}>
          已选择
          <span className={`mx-[6px] text-txt-blue ${commonStyles.mediumText14}`}>{total}</span>项
        </span>
      </div>
      <Divider type="vertical" className="border-txt-sub opacity-40 mx-[24px]" />
      <div className={`flex items-center gap-[20px] ${commonStyles.normalText14}`}>
        {batchRender && batchRender()}
        {/* <div className="flex items-center gap-[6px]">
          <IconDownload className="size-[20px]" />
          批量下线
        </div> */}
        {/* <div className="flex items-center gap-[6px]" onClick={onDelete}>
          <IconTrash className="size-[20px]" />
          批量删除
        </div> */}
      </div>
    </div>
  );
};
