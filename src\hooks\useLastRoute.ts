import { useEffect } from 'react';

export const getLastRoute = () => {
  const lastRoute = localStorage.getItem('lastRoute');
  return lastRoute || '';
};

export const useLastRoute = () => {
  useEffect(() => {
    // 定义事件处理函数
    const handleBeforeUnload = () => {
      localStorage.setItem('lastRoute', window.location.pathname);
    };

    // 监听 beforeunload 事件
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 清理事件监听器
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [location.pathname]);

  const lastRoute = localStorage.getItem('lastRoute');

  return lastRoute;
};
