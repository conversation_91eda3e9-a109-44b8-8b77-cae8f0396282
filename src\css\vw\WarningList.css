/* WarningList 组件样式 */
.wl-card-container {
  background: rgba(17, 19, 41, 0.99);
  backdrop-filter: blur(80px);
  border-radius: 8px;
  padding: 24px 30px 30px;
  width: 450px;
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}

.wl-header {
  display: flex;
  align-items: center;
  gap: 4px;
  justify-content: space-between;
}

.wl-header-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: #fff;
  font-weight: 400;
  gap: 10px;
}

.wl-header-count {
  color: rgba(255, 255, 255, 0.60);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 12px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}


.wl-header-value {
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  margin-left: 4px;
}

.wl-split-line {
  background: rgba(255, 255, 255, .05);
  height: 1px;
  margin: 20px auto;
  width: 100%;
}

.wl-list {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
  min-height: 272px;
  overflow: hidden;
}

.wl-list-empty {
  font-size: 14px;
  color: #666;
  font-weight: 400;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wl-list-row {
  display: flex !important;
  flex-direction: column;
  padding-bottom: 20px;
  height: 50px;
  width: 390px;
  overflow: hidden;
  background: rgba(238, 121, 117, 0.10);
  border-radius: 25px;
  margin-bottom: 12px;
  padding: 0 20px;
  justify-content: center;
}

.wl-list-left {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wl-time {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.60);
  font-weight: 400;
  flex-shrink: 0;
}

.wl-device {
  font-family: 'PingFang SC', sans-serif;
  font-size: 12px;
  color: #fff;
}

.wl-list-right {
  line-height: 24px;
  font-size: 18px;
  display: flex;
  color: #fff;
  font-weight: bold;
  font-size: 14px;
}

.wl-list-right.is-high {
  /* background: linear-gradient(90deg, #dd503b 0%, rgba(222, 80, 58, 0.1) 100%); */
  color: #dd503b;
}

.wl-list-right.is-medium {
  /* background: linear-gradient(90deg, #FFA63F 0%, rgba(255, 166, 63, 0.1) 100%); */
  color: #FFA63F;
}

.wl-list-right.is-low {
  /*background: linear-gradient(90deg, #68DBAD 0%, rgba(104, 219, 173, 0.1) 100%);
  */
  color: #68DBAD;
}

.wl-level-list {
  display: flex;
  justify-content: space-between;
}

.wl-level {
  text-align: center;
  width: 64px;
}

.wl-level-title {
  margin-bottom: 10px;
  color: rgba(255, 255, 255, 0.80);
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.wl-level-circle {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 32px;
  background: rgba(61, 132, 218, 0.15);
}
