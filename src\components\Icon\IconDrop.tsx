/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 09:33:36
 * @LastEditTime: 2025-01-14 22:32:42
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDrop = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.5 12H16.5M4 7H19M9 17H14"
        stroke="#667085"
        strokeWidth="1.66667"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
