.XcDrawerFloat {
  width: 100%;
  height: 100%;
  border-radius: 16px;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
}

.XcDrawerFloat-head {
  height: 56px;
  border-radius: 8px;
  display: none;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 29px 24px;
  background: rgba(246, 246, 248, 0.7);
}

.XcDrawer {
  .ant-drawer-content-wrapper {
    background-color: #fff;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    overflow: hidden;
  }

  .ant-drawer-body {
    margin: 0;
    display: flex;
    flex-direction: column;
    min-height: 370px;
    padding: 0 0px;
    overflow: hidden;
  }

  .XcDrawer-head {
    height: 56px;
    background: rgba(246, 246, 248, 0.7);
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin: 8px 8px;
    justify-content: space-between;
    flex-shrink: 0;
  }

  .XcDrawer-title {
    font-size: 16px;
    font-weight: 500;
  }

  .XcDrawer-close {
    width: 16px;
    height: 16px;
    color: #A3A3A3;
    cursor: pointer;
  }

  .XcDrawerScroll {
    flex: 1;
    overflow: auto;

    &::-webkit-scrollbar {
      width: 0;
    }

    &::-webkit-scrollbar-corner,
    &::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar-track {
      display: none;
      background: transparent;
    }
  }
}
