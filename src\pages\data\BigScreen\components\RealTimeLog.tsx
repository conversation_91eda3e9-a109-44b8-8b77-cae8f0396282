import { Carousel } from 'antd';
import { ReactComponent as RealTimeLogIcon } from '../assets/rtl-icon.svg';
import { ReactComponent as RealTimeLogTimeIcon } from '../assets/rtl-time-icon.svg';
import { useEffect, useState } from 'react';
import { BigScreenAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { MOS_ZH } from '@/constants/mosquitor';
import '@/css/vw/RealTimeLog.css';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';

export default function RealTimeLog(props: any) {
  const [list, setList] = useState<any[]>([]);
  const { region, mosquitoType } = props;
  const mosquitoRealtime = useRequest(
    (data?: any, headers?: any) => ({
      url: BigScreenAPI.realtime,
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      initialData: {},
      pollingInterval: 30000,
      onSuccess: (data) => {
        setList(data?.list || []);
      },
    },
  );

  useEffect(() => {
    // console.log('region ', region);
    if (region?.device_id) {
      mosquitoRealtime.run({
        // device_code: region.device_code,
        device_id: region.device_id,
        mosquito_type: mosquitoType,
      });
    }
  }, [region, mosquitoType]);

  return (
    <div className="rtl-card-container">
      <div className="rtl-title-container">
        <RealTimeLogIcon className="rtl-title-icon" />
        <h3 className="rtl-title-text">实时监测</h3>
      </div>
      <div className="rtl-content">
        <Carousel
          dots={false}
          autoplay={list.length > 6 ? true : false}
          vertical
          slidesToShow={list.length > 6 ? 6 : list.length}
        >
          {list.length ? (
            list.map((item: any, index: number) => (
              <div className="rtl-item" key={index}>
                <div className="rtl-item-box">
                  <div className="rtl-item-time">
                    <RealTimeLogTimeIcon className="rtl-item-time-icon" />
                    <span>{dayjs(item.timestamp).format(DATE_FMT.DATE_TIME)}</span>
                  </div>
                  <div className="rtl-item-content text-one-row">
                    {MOS_ZH[item.data_type.toLocaleLowerCase().trim()]} {item.value} 只
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="rtl-item ">
              <div className="rtl-item-box ">
                <div className="rtl-item-empty">暂无数据</div>
              </div>
            </div>
          )}
        </Carousel>
      </div>
    </div>
  );
}
