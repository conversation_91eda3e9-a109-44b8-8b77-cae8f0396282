/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:33:12
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconEnvInfo = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#FFA63F" />
      <g clipPath="url(#clip0_90_635)">
        <path
          d="M9.125 16.9797L12 12.0001M14.5 7.66998C12.3434 6.42487 9.64482 6.96891 8.123 8.84157C7.97446 9.02435 7.9002 9.11574 7.87859 9.24668C7.8614 9.35086 7.88687 9.48893 7.94009 9.58012C8.00699 9.69474 8.12556 9.76319 8.36269 9.9001L15.6373 14.1001C15.8744 14.237 15.993 14.3055 16.1257 14.3061C16.2313 14.3066 16.3636 14.2596 16.4452 14.1926C16.5478 14.1085 16.5898 13.9984 16.6739 13.7784C17.5347 11.5242 16.6566 8.91508 14.5 7.66998ZM14.5 7.66998C13.5434 7.11769 11.6487 8.60864 10.2679 11.0001M14.5 7.66998C15.4566 8.22226 15.1128 10.6086 13.7321 13.0001M17 17.0001H7"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_90_635">
          <rect width="12" height="12" fill="white" transform="translate(6 6)" />
        </clipPath>
      </defs>
    </svg>
  );
};
