/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-05 18:07:12
 * @LastEditTime: 2024-12-05 18:07:20
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
// Echarts图表字体、间距自适应
export const fitEChartSize = (size: number, defalteWidth = 1920) => {
  let clientWidth =
    window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return size;
  let scale = clientWidth / defalteWidth;
  return Number((size * scale).toFixed(3));
};
