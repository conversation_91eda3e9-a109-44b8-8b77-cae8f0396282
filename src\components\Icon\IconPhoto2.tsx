/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 20:11:48
 * @LastEditTime: 2025-01-14 22:34:57
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export const IconPhoto2 = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="32"
      height="32"
      viewBox="0 0 24 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24 11.679V17.7C24 19.5225 22.5225 21 20.7 21H3.3C1.4775 21 2.8125e-08 19.5225 0 17.7V3.3C0 1.4775 1.4775 0 3.3 0H20.7C22.5225 0 24 1.4775 24 3.3V11.679ZM22.2 9.5802V3.3C22.2 3.10302 22.1612 2.90796 22.0858 2.72597C22.0104 2.54399 21.8999 2.37863 21.7607 2.23934C21.6214 2.10005 21.456 1.98956 21.274 1.91418C21.092 1.8388 20.897 1.8 20.7 1.8H3.3C3.10302 1.8 2.90796 1.8388 2.72597 1.91418C2.54399 1.98956 2.37863 2.10005 2.23934 2.23934C2.10005 2.37863 1.98956 2.54399 1.91418 2.72597C1.8388 2.90796 1.8 3.10302 1.8 3.3V12.9885C2.19728 12.9295 2.59837 12.8999 3 12.9C4.88178 12.8972 6.70533 13.5523 8.1552 14.7519C9.6102 10.9608 13.263 8.4 17.4 8.4C19.248 8.4 20.9718 8.8719 22.2 9.5802ZM22.2 11.8932C22.05 11.6592 21.6057 11.2842 20.9724 10.9674C20.0091 10.4856 18.7794 10.2 17.4 10.2C13.9752 10.2 10.9557 12.345 9.7941 15.507C9.7173 15.7164 9.5859 16.0785 9.4005 16.5921C9.1845 17.1912 8.4315 17.3826 7.9557 16.9593C7.4934 16.5477 7.1526 16.2543 6.9393 16.0833C5.82283 15.1856 4.43259 14.6974 3 14.7C2.5932 14.7 2.1921 14.7384 1.8 14.814V17.7C1.8 17.897 1.8388 18.092 1.91418 18.274C1.98956 18.456 2.10005 18.6214 2.23934 18.7607C2.37863 18.8999 2.54399 19.0104 2.72597 19.0858C2.90796 19.1612 3.10302 19.2 3.3 19.2H20.7C20.897 19.2 21.092 19.1612 21.274 19.0858C21.456 19.0104 21.6214 18.8999 21.7607 18.7607C21.8999 18.6214 22.0104 18.456 22.0858 18.274C22.1612 18.092 22.2 17.897 22.2 17.7V11.8929V11.8932ZM6.6 9.6C5.88391 9.6 5.19716 9.31554 4.69081 8.80919C4.18446 8.30284 3.9 7.61608 3.9 6.9C3.9 6.18392 4.18446 5.49716 4.69081 4.99081C5.19716 4.48446 5.88391 4.2 6.6 4.2C7.31608 4.2 8.00284 4.48446 8.50919 4.99081C9.01554 5.49716 9.3 6.18392 9.3 6.9C9.3 7.61608 9.01554 8.30284 8.50919 8.80919C8.00284 9.31554 7.31608 9.6 6.6 9.6ZM6.6 7.8C6.83869 7.8 7.06761 7.70518 7.2364 7.5364C7.40518 7.36761 7.5 7.13869 7.5 6.9C7.5 6.6613 7.40518 6.43239 7.2364 6.2636C7.06761 6.09482 6.83869 6 6.6 6C6.3613 6 6.13239 6.09482 5.9636 6.2636C5.79482 6.43239 5.7 6.6613 5.7 6.9C5.7 7.13869 5.79482 7.36761 5.9636 7.5364C6.13239 7.70518 6.3613 7.8 6.6 7.8Z"
        fill="#B4B4B4"
      />
    </svg>
  );
};
