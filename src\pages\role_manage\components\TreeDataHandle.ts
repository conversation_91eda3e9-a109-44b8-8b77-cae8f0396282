import { findAndAddParentIds, findNodeById } from '@/utils/tree.util';

// 定义树节点的类型
export interface TreeNode {
  id: number;
  checked?: boolean;
  children?: TreeNode[];
}
export function handleTree(tree: TreeNode, checked: boolean, id: number): TreeNode {
  // 递归函数，用于更新节点及其子节点的选中状态
  function updateNodeAndChildren(node: TreeNode, newChecked: boolean): void {
    node.checked = newChecked;
    if (node.children) {
      node.children.forEach((child) => {
        updateNodeAndChildren(child, newChecked);
      });
    }
  }
  // 查找父节点的函数
  function findParent(node: TreeNode): TreeNode | undefined {
    function search(parentTree: TreeNode): TreeNode | undefined {
      if (parentTree.children) {
        for (let i = 0; i < parentTree.children.length; i++) {
          const child = parentTree.children[i];
          if (child === node) {
            return parentTree;
          }
          const found = search(child);
          if (found) {
            return found;
          }
        }
      }
      return undefined;
    }
    return search(tree);
  }
  // 递归函数，用于更新父节点的选中状态
  function updateParentNode(parent?: TreeNode): void {
    if (parent) {
      const someChildrenChecked = parent.children?.some((child) => child.checked) || false;
      const allChildrenUnchecked = parent.children?.every((child) => !child.checked) || true;

      if (someChildrenChecked) {
        parent.checked = true;
      } else if (allChildrenUnchecked) {
        parent.checked = false;
      }
      updateParentNode(findParent(parent));
    }
  }

  // 递归查找要更新的节点
  function findAndUpdate(node: TreeNode): boolean {
    if (node.id === id) {
      updateNodeAndChildren(node, checked);
      updateParentNode(findParent(node));
      return true;
    }
    if (node.children) {
      for (let i = 0; i < node.children.length; i++) {
        if (findAndUpdate(node.children[i])) {
          return true;
        }
      }
    }
    return false;
  }

  findAndUpdate(tree);
  return tree;
}

// 新增函数：获取所有 checked 为 true 的节点的 id
export function getAllCheckedIds(tree: TreeNode): number[] {
  const checkedIds: number[] = [];
  function traverse(node: TreeNode) {
    if (node.checked && node.id > 0) {
      checkedIds.push(node.id);
    }
    if (node.children) {
      node.children.forEach((child) => {
        traverse(child);
      });
    }
  }
  traverse(tree);
  return checkedIds;
}

// 新增方法：将给定 id 数组中的所有节点设置为选中
export function checkedNodesByIds(tree: TreeNode, ids: number[]): TreeNode {
  let newCheckedIds: number[] = [...ids];
  ids.forEach((id) => {
    const parentIds = findAndAddParentIds([tree], id, []);
    if (parentIds) {
      newCheckedIds = Array.from(new Set([...newCheckedIds, ...parentIds]));
    }
  });
  newCheckedIds.forEach((id) => {
    const node = findNodeById(tree, id);
    if (node) {
      node.checked = true;
    }
  });
  return tree;
}
