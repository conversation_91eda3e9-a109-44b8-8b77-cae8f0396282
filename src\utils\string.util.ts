/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 18:27:52
 * @LastEditTime: 2025-01-23 12:11:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const isBlank = (str: string): boolean => {
  if (typeof str === 'string') {
    return str === '' || str?.trim() === '';
  }

  return str === undefined || str === null;
};

export function concatString(...args: string[]) {
  return args
    .map((str) => {
      if (str === undefined || str === null) {
        return '';
      }
      return str;
    })
    .join('');
}

export function getUrlParameters(url: string) {
  const params: any = {};
  // 查找URL中的查询字符串部分（?之后的内容）
  const queryString = url.includes('?') ? url.split('?')[1] : '';
  if (!queryString) return params;

  // 解析查询字符串中的每个参数
  const paramPairs = queryString.split('&');
  for (const pair of paramPairs) {
    const [key, value] = pair.split('=').map(decodeURIComponent);
    if (key) {
      // 处理重复参数名的情况，存入数组
      if (params[key] !== undefined) {
        params[key] = Array.isArray(params[key]) ? [...params[key], value] : [params[key], value];
      } else {
        params[key] = value;
      }
    }
  }

  return params;
}

export function isAiLayout() {
  // const params = getUrlParameters(global?.location?.href || '');
  return true;
}
