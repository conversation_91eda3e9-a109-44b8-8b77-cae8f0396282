/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-19 01:04:09
 * @LastEditTime: 2025-01-19 01:04:22
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgDeviceEdit = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.4226 6.29222L17.5036 10.3733M15.492 4.22292C15.492 4.22292 17.3655 3.3696 18.998 5.00203C20.6304 6.63446 19.7771 8.50805 19.7771 8.50805L8.86947 19.4157L4 20L4.58434 15.1305L15.492 4.22292Z"
        stroke="#667085"
        strokeWidth="1.5"
      />
    </svg>
  );
};
