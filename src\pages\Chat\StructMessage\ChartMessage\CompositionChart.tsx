import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import { useModel, useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { useEffect, useMemo } from 'react';
import { MOS_NAME_LIST_EN } from '@/constants/mosquitor';
import { Spin } from 'antd';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import ChartMessage from './ChartMessage';

export default function CompositionChart(props: any) {
  const { startTime, endTime, range } = props;
  const {
    run: getSpeciesCompose,
    data: speciesComposeRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getSpeciesCompose,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );
  const { regionLoad } = useModel('useDevice', (m) => ({
    regionLoad: m.regionLoad,
  }));

  useEffect(() => {
    if (!regionLoad) return;
    getSpeciesCompose({
      start_time: startTime,
      end_time: endTime,
      time_unit: 'day',
      ...range,
    });
  }, [regionLoad]);

  const speciesCompose = useMemo(() => {
    const trendPoints = speciesComposeRes?.trend_points || [];
    const names = Object.values(MOS_NAME_LIST_EN);
    const result = names.map((name: any, index) => {
      const list: any = trendPoints.map((point: any) => {
        const species = point.species || [];
        const mos = species.find((item: any) => item.monitor_type?.toLowerCase() === name);
        return {
          count: mos?.count || 0,
        };
      });
      return {
        name,
        data: list,
      };
    });
    return result;
  }, [speciesComposeRes]);

  const option = useMemo(() => {
    const xLabels = speciesComposeRes?.trend_points.map((s: any) => {
      return s.time_point;
    });
    const optData = {
      tooltip: { trigger: 'axis' },
      grid: { left: 60, right: 30, top: 30, bottom: 60 },
      legend: {
        show: false,
        bottom: 10,
        left: 'center',
      },
      xAxis: [
        {
          type: 'category',
          data: xLabels,
          axisLabel: {
            color: '#797A85',
            overflow: 'truncate',
            ellipsis: '...',
            rotate: 0,
            margin: 20,
          },
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: {
        type: 'value',
        axisLabel: { color: '#797A85', rotate: 0, margin: 20 },
        splitLine: { lineStyle: { color: '#E4E6EB', type: 'dashed' } },
      },
      series: speciesCompose.map((s: any) => {
        return {
          name: getMosName(s.name),
          type: 'bar',
          stack: 'total',
          barGap: '10%',
          barWidth: '70%',
          data: s.data.map((item: { count: number }) => item.count),
          label: {
            color: '#333',
            position: 'top',
          },
          itemStyle: {
            color: getMosColor(s.name),
          },
        };
      }),
    };
    return optData;
  }, [speciesCompose]);

  return (
    <ChartMessage title="蚊种构成比">
      {loading ? (
        <div className="size-full flex-center">
          <Spin />
        </div>
      ) : (
        <EChartsCommon option={option} />
      )}
    </ChartMessage>
  );
}
