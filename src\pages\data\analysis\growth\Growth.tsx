import React, { useMemo, useRef } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { ReactComponent as IconDensity } from '../assets/density.svg';
import { <PERSON><PERSON>, Spin } from 'antd';
import { statisticsAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { EChartsOption } from 'echarts';
import dayjs from 'dayjs';
import { DATE_FMT, formXLabelByTimeUnit } from '@/utils/time.util';
import DataSearchForm from '../../components/DataSearchForm';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';

export default function Growth(props: any) {
  const { menu } = props;
  const queryRef = useRef<any>({});
  const {
    run: getGrowthRate,
    data: growthRateRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      queryRef.current = { ...data };
      return {
        url: statisticsAPI.getGrowthRate,
        method: 'get',
        params: {
          ...queryRef.current,
          monitor_type: queryRef.current?.monitor_type || '',
          tenant_id: 1,
        },
        headers,
      };
    },
    {
      manual: true,
    },
  );
  const growthRateData = useMemo(() => {
    const list = growthRateRes?.type_groups || [];
    const result = list.slice(0);
    return result;
  }, [growthRateRes]);

  const option = useMemo((): EChartsOption => {
    let result = growthRateData || [];
    result = result.map((item: any) => {
      const name =
        queryRef.current.growth_type === 'ytoy' ? '总蚊虫同比增长率' : '总蚊虫环比增长率';
      return {
        ...item,
        color: getMosColor(item.monitor_type),
        name: item.monitor_type === 'all' ? name : getMosName(item.monitor_type) || '其他',
      };
    });
    let xLabels = [];
    if (result?.[0]?.trend_points) {
      xLabels = result?.[0]?.trend_points.map((item: any) => {
        return formXLabelByTimeUnit(item.time_point, queryRef.current.time_unit);
      });
    }
    return {
      tooltip: {
        trigger: 'axis',
        // formatter: function (params: any) {
        //   const time = params[0].axisValue;
        //   return `
        //     <div style="text-align: left;">
        //       <h4>${time}</h4>
        //       ${params
        //         .map((item: any) => {
        //           return `<p>${item.name}:${item?.data?.current_value || 0}</p>`;
        //         })
        //         .join('')}
        //     </div>
        //   `;
        // },
      },
      legend: {
        show: true,
        icon: 'roundRect',
        data: result.map((item: any) => item),
        left: 'center',
        bottom: 0,
      },
      grid: [
        {
          left: 30,
          right: 30,
          top: 20,
          bottom: 40,
          containLabel: true,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: xLabels,
          axisLabel: { rotate: 0, margin: 20 },
          axisTick: {
            alignWithLabel: true,
          },
        },
      ],
      yAxis: [
        {
          type: 'value' as const,
        },
      ],
      series: result.map((item: any) => {
        return {
          name: item.name,
          type: 'line',
          areaStyle: {
            color: item.color,
          },
          itemStyle: {
            color: item.color,
          },
          label: {
            show: true,
            position: 'center',
          },
          symbolSize: 6,
          symbol: 'emptyCircle',
          data: item.trend_points.map((s: any) => {
            return {
              ...s,
              name: item.name,
              color: item.color,
              value: s.growth_rate,
            };
          }),
        };
      }),
    };
  }, [growthRateData]);

  return (
    <div className="py-[20px] flex-1 h-full box-border xc-scrollbar-y">
      <Panel
        title={
          <span className="flex items-center gap-2">
            <IconDensity className="text-[#FC91AD]"></IconDensity>
            <span>蚊虫增长率分析</span>
            <span className="text-[12px] text-[#999]">
              （所选蚊虫与{queryRef.current.growth_type === 'ytoy' ? '上一年同期' : '上一阶段'}
              增长率分析）
            </span>
          </span>
        }
        actions={
          <Button type="primary" className="rounded-[6px] px-4 h-8 text-[14px] font-medium !hidden">
            下载
          </Button>
        }
      >
        <DataSearchForm
          menu={menu}
          onRequest={getGrowthRate}
          allowFields={['region', 'monitor_type', 'time_unit', 'dateRange', 'growth_type']}
        ></DataSearchForm>
        <div className="w-full h-[500px] flex items-center justify-center">
          <div className="w-full h-full">
            {growthRateData?.length > 0 ? (
              <EChartsCommon option={option} />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                {loading ? <Spin /> : <span className="text-txt-sub">暂无数据</span>}
              </div>
            )}
          </div>
        </div>
      </Panel>
    </div>
  );
}
