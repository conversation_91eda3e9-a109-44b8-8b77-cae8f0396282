/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-24 01:28:47
 * @LastEditTime: 2025-01-14 22:33:17
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconFeature = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#2953FF" />
      <g clipPath="url(#clip0_118_735)">
        <path
          d="M8.25 17V14.5M8.25 9.5V7M7 8.25H9.5M7 15.75H9.5M12.5 7.5L11.6329 9.75443C11.4919 10.121 11.4214 10.3044 11.3118 10.4585C11.2146 10.5952 11.0952 10.7146 10.9585 10.8118C10.8044 10.9214 10.621 10.9919 10.2544 11.1329L8 12L10.2544 12.8671C10.621 13.0081 10.8044 13.0786 10.9585 13.1882C11.0952 13.2854 11.2146 13.4048 11.3118 13.5415C11.4214 13.6956 11.4919 13.879 11.6329 14.2456L12.5 16.5L13.3671 14.2456C13.5081 13.879 13.5786 13.6956 13.6882 13.5415C13.7854 13.4048 13.9048 13.2854 14.0415 13.1882C14.1956 13.0786 14.379 13.0081 14.7456 12.8671L17 12L14.7456 11.1329C14.379 10.9919 14.1956 10.9214 14.0415 10.8118C13.9048 10.7146 13.7854 10.5952 13.6882 10.4585C13.5786 10.3044 13.5081 10.121 13.3671 9.75443L12.5 7.5Z"
          stroke="white"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};
