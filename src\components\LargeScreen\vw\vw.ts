/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-05 18:02:44
 * @LastEditTime: 2025-01-24 19:56:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
// 定义设计稿的宽高
const designWidth = 1920;
const designHeight = 1080;

// px转vw
export const px2vw = (_px: number) => {
  return (_px * 100.0) / designWidth + 'vw';
};

export const px2vh = (_px: number) => {
  return (_px * 100.0) / designHeight + 'vh';
};

export const px2font = (_px: number) => {
  return (_px * 100.0) / designWidth + 'vw';
};

// tailwind
export const tpx2vw = (clz: string, _px: number) => {
  return `${clz}-[${(_px * 100.0) / designWidth + 'vw'}]`;
};

export const tpx2vh = (clz: string, _px: number) => {
  return `${clz}-[${(_px * 100.0) / designHeight + 'vh'}]`;
};

export const tpx2font = (clz: string, _px: number) => {
  return `${clz}-[${(_px * 100.0) / designWidth + 'vw'}]`;
};
