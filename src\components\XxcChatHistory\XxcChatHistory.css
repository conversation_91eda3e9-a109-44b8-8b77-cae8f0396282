.XxcChatHistory-new {
  position: relative;
  width: 168px;
  height: 36px;
  display: flex;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.8);
  box-sizing: border-box;
  border: 1px solid rgba(111, 93, 255, 0.17);
  border-radius: 6px;
  cursor: pointer;
  margin: 0 auto;
}

.XxcChatHistory-newIcon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
  color: #313747;
  font-size: 14px;
}

.XxcChatHistory-newPlus {
  position: absolute;
  right: 16px;
  top: 11px;
  width: 14px;
  height: 14px;
  color: #6D7380;
}

.XxcChatHistory-list {
  width: 168px;
  margin: 12px auto;
}

.XxcChatHistory-input,
.XxcChatHistory-item {
  display: flex;
  align-items: center;
  height: 36px;
  font-size: 14px;
  padding: 8px;
  border-radius: 6px;
  box-sizing: border-box;
  margin-bottom: 2px;
}

.XxcChatHistory-item {
  cursor: pointer;
  color: #84909F;
  justify-content: space-between;

  &:hover,
  &.is-selected {
    color: #313747;
    background-color: #fff;

    .XxcChatHistory-itemMenu {
      display: block;
    }
  }
}

.XxcChatHistory-itemText {
  flex: 1;
  min-width: 0;
}

.XxcChatHistory-itemMenu {
  flex-shrink: 0;
  display: none;
  width: 16px;
  height: 16px;
  background: url(@/assets/svg/More.svg) no-repeat center center;
}

.XxcChatHistory-input {
  background: #DCE9F6;
  color: #313747;
  font-size: 14px;
  width: 100%;
  outline: none;
}
