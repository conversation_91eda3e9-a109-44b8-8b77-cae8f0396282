import './Chat.css';
import MessageList from './MessageList/MessageList';
import _ from 'lodash';
import ChatTaskView from './ChatTaskView/ChatTaskView';
import ChatFooter from './ChatFooter/ChatFooter';
import { useEffect, useMemo, useRef, useState } from 'react';
import { CHAT_EVENT_NAME_MAP } from '@/services/chatService';
import { useChatStore } from '@/store/chatStore';
import { DRAWER_THEME_TYPE } from '@/components/XcDrawer';
import { AsyncComponent } from '@/components/AsyncComponent/AsyncComponent';
import { GLOBAL_MODAL_TYPE } from '@/constants/common';
import { useMemoizedFn } from 'ahooks';
import { EmptyFunction, EmptyObject } from '@/utils/common.util';

function AsideRender(props: any) {
  const [asideTheme, setAsideTheme] = useState('');
  const [asideContent, setAsideContent] = useState<string>('');
  const instructInfo = useChatStore((state) => state.instructInfo);
  const [modalType, setModalType] = useState<GLOBAL_MODAL_TYPE>(GLOBAL_MODAL_TYPE.none);
  const [animateStyle, setAnimateStyle] = useState({});

  const onCloseComponent = useMemoizedFn(() => {
    setAsideContent('');
    setModalType(GLOBAL_MODAL_TYPE.none);
    useChatStore.setState({
      instructInfo: null,
    });
  });

  useEffect(() => {
    console.log('🚀 ~ AsideRender ~ instructInfo:', instructInfo);
    const instructType = instructInfo?.instructType;
    switch (instructType) {
      case CHAT_EVENT_NAME_MAP.device_add:
        setAsideContent(CHAT_EVENT_NAME_MAP.device_add);
        setModalType(GLOBAL_MODAL_TYPE.add);
        break;
      case CHAT_EVENT_NAME_MAP.device_edit:
        setAsideContent(CHAT_EVENT_NAME_MAP.device_edit);
        setModalType(GLOBAL_MODAL_TYPE.edit);
        break;
      case CHAT_EVENT_NAME_MAP.device_list:
        setAsideContent(CHAT_EVENT_NAME_MAP.device_list);
        break;
      case CHAT_EVENT_NAME_MAP.device_delete:
        setAsideContent(CHAT_EVENT_NAME_MAP.device_delete);
        break;
      //--- 用户
      case CHAT_EVENT_NAME_MAP.user_add:
        setAsideContent(CHAT_EVENT_NAME_MAP.user_add);
        setModalType(GLOBAL_MODAL_TYPE.add);
        break;
      case CHAT_EVENT_NAME_MAP.user_edit:
        setAsideContent(CHAT_EVENT_NAME_MAP.user_edit);
        setModalType(GLOBAL_MODAL_TYPE.edit);
        break;
      case CHAT_EVENT_NAME_MAP.user_list:
        setAsideContent(CHAT_EVENT_NAME_MAP.user_list);
        break;
      case CHAT_EVENT_NAME_MAP.user_delete:
        setAsideContent(CHAT_EVENT_NAME_MAP.user_delete);
        break;
      //--- 部门
      case CHAT_EVENT_NAME_MAP.org_add:
        setAsideContent(CHAT_EVENT_NAME_MAP.org_add);
        setModalType(GLOBAL_MODAL_TYPE.add);
        break;
      case CHAT_EVENT_NAME_MAP.org_edit:
        setAsideContent(CHAT_EVENT_NAME_MAP.org_edit);
        setModalType(GLOBAL_MODAL_TYPE.edit);
        break;
      case CHAT_EVENT_NAME_MAP.org_list:
        setAsideContent(CHAT_EVENT_NAME_MAP.org_list);
        break;
      case CHAT_EVENT_NAME_MAP.org_delete:
        setAsideContent(CHAT_EVENT_NAME_MAP.org_delete);
        break;
      //-- 角色
      case CHAT_EVENT_NAME_MAP.role_add:
        setAsideContent(CHAT_EVENT_NAME_MAP.role_add);
        setModalType(GLOBAL_MODAL_TYPE.add);
        break;
      case CHAT_EVENT_NAME_MAP.role_edit:
        setAsideContent(CHAT_EVENT_NAME_MAP.role_edit);
        setModalType(GLOBAL_MODAL_TYPE.edit);
        break;
      case CHAT_EVENT_NAME_MAP.role_list:
        setAsideContent(CHAT_EVENT_NAME_MAP.role_list);
        break;
      case CHAT_EVENT_NAME_MAP.role_delete:
        setAsideContent(CHAT_EVENT_NAME_MAP.role_delete);
        break;
      default:
        break;
    }
    return () => {
      setAsideContent('');
      setModalType(GLOBAL_MODAL_TYPE.none);
    };
  }, [instructInfo]);

  const updateAsideWidth = useMemoizedFn(() => {
    const chat: HTMLElement | null = document.querySelector('.Chat');
    const contentWidth = 844;

    if (!chat) {
      return;
    }

    let spaceWidth = chat.offsetWidth - contentWidth;
    console.log('spaceWidth', spaceWidth);
    if (spaceWidth < 660) {
      setAsideTheme(DRAWER_THEME_TYPE.normal);
    } else {
      setAsideTheme(DRAWER_THEME_TYPE.float);
    }
  });

  useEffect(() => {
    window.addEventListener('resize', updateAsideWidth);
    return () => {
      window.removeEventListener('resize', updateAsideWidth);
    };
  }, []);

  useEffect(() => {}, [asideContent]);
  return (
    <div className={'ChatAside '} style={animateStyle}>
      {asideContent && (
        <div className={'ChatAside-content is-' + asideTheme}>
          <AsyncComponent
            xcTheme={asideTheme}
            componentName={asideContent}
            modalType={modalType}
            onClose={onCloseComponent}
            visible={true}
            onChange={EmptyFunction}
            currentItem={EmptyObject}
          />
        </div>
      )}
    </div>
  );
}

export default function Chat() {
  const listRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = useMemoizedFn(() => {
    requestAnimationFrame(() => {
      listRef.current?.scrollTo({
        top: listRef.current?.scrollHeight,
        behavior: 'smooth',
      });
    });
  });

  useEffect(() => {
    scrollToBottom();
  }, []);
  return (
    <div className="Chat">
      <div className="ChatCenterBox">
        <div className="ChatCenterBox-content">
          <div className="ChatCenterBox-messageList sc-scrollbar-y" ref={listRef}>
            <MessageList />
          </div>
          <ChatTaskView />
          <ChatFooter scrollToBottom={scrollToBottom} />
        </div>
      </div>
      <AsideRender />
    </div>
  );
}
