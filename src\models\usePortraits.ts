import { useState } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-09 13:51:03
 * @LastEditTime: 2025-02-10 16:48:08
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const usePortrait = () => {
  const [sickFirstClass, setSickFirstClass] = useState<string>(); // 类
  const [sickSecondClass, setSickSecondClass] = useState<string>(); // 属
  const [sickThirdClass, setSickThirdClass] = useState<string>(); //种

  return {
    sickFirstClass,
    sickSecondClass,
    sickThirdClass,
    setSickFirstClass,
    setSickSecondClass,
    setSickThirdClass,
  };
};

export default usePortrait;
