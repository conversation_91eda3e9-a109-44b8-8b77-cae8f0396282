import { IconLogo } from '@/components/Icon/IconLogo';
import { px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { Drawer } from 'antd';
import { memo, useCallback, useState } from 'react';
import { ChatBox } from './ChatBox';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-30 12:52:14
 * @LastEditTime: 2025-02-11 17:35:24
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}

const XxcContainer = styled.div`
  border-radius: ${() => px2vw(30)};
  gap: ${() => px2vw(10)};
  padding: ${() => `${px2vh(5)} ${px2vw(20)}`};
`;
const XxcLogo = styled(IconLogo)`
  width: ${() => px2vw(25)};
`;
const XxcText = styled.p`
  font-size: ${() => px2vw(14)};
  font-family: 'PingFang SC';
  color: #8fb7f2;
  font-weight: 500;
`;

export const Xxc = memo((props: IProps) => {
  const { className } = props;
  const [modelVisible, setModalVisible] = useState(false);

  const onClick = useCallback(() => {
    setModalVisible(true);
  }, []);

  const onClose = useCallback(() => {
    setModalVisible(false);
  }, []);

  return (
    <>
      <XxcContainer
        className={`absolute bg-gradient-to-l from-[#2C366A] to-[#3D4D92] flex items-center justify-center text-[#ffffff] ${className}`}
        onClick={onClick}
      >
        <XxcLogo />
        <XxcText>星小尘</XxcText>
      </XxcContainer>

      <Drawer
        width="auto"
        onClose={onClose}
        closable={false}
        autoFocus={true}
        footer={null}
        destroyOnClose={true}
        open={modelVisible}
        styles={{
          body: {
            padding: 0,
          },
          content: {
            backgroundColor: '#000000',
            borderRadius: 0,
            padding: 0,
            boxShadow: '50px',
          },
        }}
      >
        <div className="xl:w-[800px] md:w-[600px] sm:w-[540px] h-[100%]">
          <ChatBox />
        </div>
      </Drawer>
    </>
  );
});
