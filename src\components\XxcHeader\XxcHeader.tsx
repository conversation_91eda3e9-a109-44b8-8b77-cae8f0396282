import { ReactComponent as LogoBlue } from '@/assets/svg/logo-blue.svg';
import mail from './assets/mail.png';
import userAvatar from '@/assets/user/img_avatar.jpg';
import XxcUserPopover from '../XxcUserPopover';
import { useState } from 'react';
import { useMemoizedFn } from 'ahooks';

import './XxcHeader.css';

export default function XxcHeader() {
  const [visible, setVisible] = useState(false);
  const showUserPopover = () => {
    setVisible(true);
  };
  const hideUserPopover = useMemoizedFn(() => {
    setVisible(false);
  });
  return (
    <div className="XxcHeader ">
      <div className="XxcHeader-logo">
        <LogoBlue />
      </div>
      <div className="XxcHeader-menu flex-center">
        <div className="XxcHeader-mail">
          <img src={mail} className="XxcHeader-menuIcon" />
          <span>消息中心</span>
        </div>
        <div className="XxcHeader-menuDivider" />
        <div className="XxcHeader-user" onClick={showUserPopover}>
          <img src={userAvatar} className="XxcHeader-menuIcon" />
          <span>个人中心</span>
        </div>
        <XxcUserPopover visible={visible} onClose={hideUserPopover} />
      </div>
    </div>
  );
}
