import { history } from '@umijs/max';
import { errorConfig } from './requestErrorConfig';

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  user?: any;
  loading?: boolean;
  fetchUserInfo?: () => Promise<any | undefined>;
}> {
  const user = localStorage.getItem('user') || '';

  try {
    const ju = JSON.parse(user);
    if (ju) {
      return {
        user: {
          ...ju,
        },
      };
    } else {
      history.push('/user/login');
    }
  } catch (error) {
    history.push('/user/login');
  }

  return {};
}

export const request = {
  ...errorConfig,
};
