/*
 * Desc:
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Copyright (c) 2025 aiaas.com.cn
 * 权限管理
 * ! 此文件构建也在用, 请勿添加window document 等浏览器变量
 */

import _, { get } from 'lodash';
import { getTreeProperty } from './tree.util';

export interface IPermission {
  id?: number | string;
  name?: string;
  parent_id?: number;
  code?: string | number;
  type?: string;
  description?: string;
  status?: number;
  children?: IPermission[];
  checked?: boolean;
}

export enum STATIC_PERMISSION_CODE {
  数据权限 = -10000,
  菜单权限 = -20000,
  企业管理 = -30000, // 企业管理
  日志管理 = -40000,
  报表中心 = -50000,
  规则管理 = -60000,
  监测记录 = -70000,
  设备管理 = 'menu:device',
  创建设备 = 'device:create',
  更新设备 = 'device:update',
  删除设备 = 'device:delete',
  更新设备状态 = 'device:update:status',
  数字监测中心 = 'menu:digital',
  监测总览 = 'menu:monitor_overview',
  蚊媒画像中心 = 'menu:mosquito',
  查询监测记录 = 'menu:mosquito_audit',
  专家验证 = 'mosquito_audit:submit',
  删除监测记录 = 'mosquito_audit:delete',
  用户管理 = 'menu:user',
  创建用户 = 'user:create',
  更新用户 = 'user:update',
  删除用户 = 'user:delete',
  重置密码 = 'user:batch:password',
  修改状态 = 'user:batch:status',
  分配角色 = 'user:batch:roles',
  批量分组 = 'user:batch:orgs',
  部门管理 = 'menu:organization',
  创建部门 = 'organization:create',
  更新部门 = 'organization:update',
  删除部门 = 'organization:delete',
  解绑部门用户 = 'organization:unbind:users',
  角色管理 = 'menu:role',
  创建角色 = 'role:create',
  更新角色 = 'role:update',
  删除角色 = 'role:delete',
  解绑角色用户 = 'role:unbind:users',
  操作日志 = 'menu:operation_log',
  设备运行日志 = 'menu:device_log',
  设备绑定日志 = 'menu:device_bind_log',
  监测数据报表 = 'menu:report_monitor',
  蚊虫成蚊监测表 = 'menu:report_adult',
  下载监测数据报表 = 'report_monitor:download',
  下载蚊虫成蚊监测表 = 'report_adult:download',
  新增监测数据报表 = 'report_monitor:create',
  数据分析 = 'menu:statistics',
  地域与构成分布 = 'statistics:region',
  密度与活跃分布 = 'statistics:density',
  环境分布 = 'statistics:envir',
  增长率 = 'statistics:growth',
  生境分布 = 'statistics:habitat',
  预警规则 = 'menu:ews_rule',
  创建预警规则 = 'ews_rule:create',
  修改预警规则 = 'ews_rule:update',
  删除预警规则 = 'ews_rule:delete',
  预警记录 = 'menu:ews_record',
  MQTT日志 = 'menu:mqtt_log',
  蚊媒验证日志 = 'menu:audit_record',
  AI验证日志 = 'menu:ai_verification',
  设备健康日志 = 'menu:device_health_report',
  创建监测网 = 'device_group:create',
  删除监测网 = 'device_group:delete',
}
export const STATIC_PERMISSION_NAME = _.invert(STATIC_PERMISSION_CODE);

export const PERMISSION_TREE: IPermission[] = [
  {
    code: 'menu:monitor_overview',
    name: '监测总览',
  },
  {
    code: 'menu:digital',
    name: '监测大屏',
  },
  {
    name: '监测记录',
    code: STATIC_PERMISSION_CODE.监测记录,
    id: STATIC_PERMISSION_CODE.监测记录,
    children: [
      {
        code: 'menu:mosquito_audit',
        name: '查询监测记录',
      },
      {
        code: 'mosquito_audit:submit',
        name: '专家验证',
      },
      {
        code: 'mosquito_audit:delete',
        name: '删除数据',
      },
    ],
  },
  {
    name: '报表中心',
    code: STATIC_PERMISSION_CODE.报表中心,
    id: STATIC_PERMISSION_CODE.报表中心,
    children: [
      {
        code: 'menu:report_adult',
        name: '蚊虫成蚊监测表',
        children: [
          {
            code: 'report_adult:download',
            name: '下载蚊虫成蚊监测表',
          },
        ],
      },
      {
        code: 'menu:report_monitor',
        name: '监测数据报表',
        children: [
          {
            code: 'report_monitor:create',
            name: '新增数据表',
          },
          {
            code: 'report_monitor:download',
            name: '下载报监测数据表',
          },
        ],
      },
    ],
  },
  {
    code: 'menu:statistics',
    name: '数据分析',
    children: [
      {
        code: 'statistics:region',
        name: '地域与构成分布',
      },
      {
        code: 'statistics:density',
        name: '密度与活跃分布',
      },
      {
        code: 'statistics:growth',
        name: '增长率',
      },
      {
        code: 'statistics:habitat',
        name: '生境分布',
      },
      {
        code: 'statistics:envir',
        name: '环境分布',
      },
    ],
  },
  {
    code: 'menu:ews_record',
    name: '预警中心',
  },
  {
    code: 'menu:device',
    name: '设备管理',
    children: [
      {
        code: 'device:create',
        name: '创建设备',
      },
      {
        code: 'device:update',
        name: '更新设备',
      },
      {
        code: 'device:delete',
        name: '删除设备',
      },
      {
        code: 'device:update:status',
        name: '更新设备状态',
      },
    ],
  },
  {
    code: STATIC_PERMISSION_CODE.规则管理,
    name: '规则管理',
    id: STATIC_PERMISSION_CODE.规则管理,
    children: [
      {
        code: 'menu:ews_rule',
        name: '预警规则',
        children: [
          {
            code: 'ews_rule:create',
            name: '创建预警规则',
          },
          {
            code: 'ews_rule:update',
            name: '修改预警规则',
          },
          {
            code: 'ews_rule:delete',
            name: '删除预警规则',
          },
        ],
      },
    ],
  },
  {
    code: STATIC_PERMISSION_CODE.企业管理,
    name: '企业管理',
    id: STATIC_PERMISSION_CODE.企业管理,
    children: [
      {
        code: 'menu:user',
        name: '用户管理',
        children: [
          {
            code: 'user:create',
            name: '创建用户',
          },
          {
            code: 'user:update',
            name: '修改用户',
          },
          {
            code: 'user:delete',
            name: '删除用户',
          },
          {
            code: 'user:batch:password',
            name: '重置密码',
          },
          {
            code: 'user:batch:status',
            name: '修改状态',
          },
          {
            code: 'user:batch:roles',
            name: '分配角色',
          },
          {
            code: 'user:batch:orgs',
            name: '批量分组',
          },
        ],
      },
      {
        code: 'menu:organization',
        name: '部门管理',
        children: [
          {
            code: 'organization:create',
            name: '创建部门',
          },
          {
            code: 'organization:update',
            name: '修改部门',
          },
          {
            code: 'organization:delete',
            name: '删除部门',
          },
          {
            code: 'organization:unbind:users',
            name: '解绑部门用户',
          },
        ],
      },
      {
        code: 'menu:role',
        name: '角色管理',
        children: [
          {
            code: 'role:create',
            name: '创建角色',
          },
          {
            code: 'role:update',
            name: '修改角色',
          },
          {
            code: 'role:delete',
            name: '删除角色',
          },
          {
            code: 'role:unbind:users',
            name: '解绑角色用户',
          },
        ],
      },
    ],
  },
  {
    name: '日志管理',
    code: STATIC_PERMISSION_CODE.日志管理,
    id: STATIC_PERMISSION_CODE.日志管理,
    children: [
      {
        code: 'menu:operation_log',
        name: '操作日志',
      },
      {
        code: 'menu:device_log',
        name: '设备运行日志',
      },
      {
        code: 'menu:mqtt_log',
        name: 'MQTT日志',
      },
      {
        code: 'menu:audit_record',
        name: '蚊媒验证日志',
      },
      {
        code: 'menu:ai_verification',
        name: 'AI验证日志',
      },
      {
        code: 'menu:device_health_report',
        name: '设备健康日志',
      },
      {
        code: 'menu:device_bind_log',
        name: '设备绑定日志',
      },
    ],
  },
];
export const ALL_PERMISSIONS_CODE = getTreeProperty(PERMISSION_TREE, 'code');
export const SELF_PERMISSION: { current: string[] | null } = {
  current: null,
};
if (global.localStorage && localStorage.getItem('debug') === '1') {
  // SELF_PERMISSION.current = Object.values(STATIC_PERMISSION_CODE) as string[];
  SELF_PERMISSION.current = ALL_PERMISSIONS_CODE;
}
export function getUserPermission(force: boolean = false) {
  // console.log('SELF_PERMISSION.current ', SELF_PERMISSION.current);
  if (!SELF_PERMISSION.current || force) {
    try {
      const userString = localStorage.getItem('user');
      const userInfo = JSON.parse(userString || '');
      SELF_PERMISSION.current = userInfo?.permissions || [];
    } catch (e) {
      return [];
    }
  }
  return SELF_PERMISSION.current || [];
}

export function checkFunPermission(code: string) {
  return getUserPermission().includes(code);
}

export function checkMenuPermission(name: any) {
  const selfPermission = getUserPermission();
  const professionPermission =
    PERMISSION_TREE.find((item) => item.code === STATIC_PERMISSION_CODE.企业管理) ||
    ({} as IPermission);
  if (name === STATIC_PERMISSION_CODE.企业管理) {
    const plist = getTreeProperty(professionPermission, 'code');
    return selfPermission.some((item: string) => plist.includes(item));
  }
  if (name === STATIC_PERMISSION_CODE.日志管理) {
    return selfPermission.some((item: string) =>
      //@ts-ignore
      [
        STATIC_PERMISSION_CODE.操作日志,
        STATIC_PERMISSION_CODE.设备运行日志,
        STATIC_PERMISSION_CODE.AI验证日志,
        STATIC_PERMISSION_CODE.蚊媒验证日志,
        STATIC_PERMISSION_CODE.MQTT日志,
        STATIC_PERMISSION_CODE.设备绑定日志,
      ].includes(item as STATIC_PERMISSION_CODE),
    );
  }
  if (name === STATIC_PERMISSION_CODE.设备管理) {
    const plist = getTreeProperty(
      PERMISSION_TREE.find((item) => item.code === STATIC_PERMISSION_CODE.设备管理) ||
        ({} as IPermission),
      'code',
    );
    return selfPermission.some((item: string) => plist.includes(item));
  }

  if (name === STATIC_PERMISSION_CODE.监测记录) {
    return selfPermission.some((item: string) =>
      //@ts-ignore
      [
        STATIC_PERMISSION_CODE.查询监测记录,
        STATIC_PERMISSION_CODE.专家验证,
        STATIC_PERMISSION_CODE.删除监测记录,
      ].includes(item as STATIC_PERMISSION_CODE),
    );
  }
  if (name === STATIC_PERMISSION_CODE.数据权限) {
    return selfPermission.some((item: string) =>
      //@ts-ignore
      [
        STATIC_PERMISSION_CODE.数字监测中心,
        STATIC_PERMISSION_CODE.监测总览,
        STATIC_PERMISSION_CODE.监测记录,
        STATIC_PERMISSION_CODE.监测数据报表,
        STATIC_PERMISSION_CODE.数据分析,
      ].includes(item as STATIC_PERMISSION_CODE),
    );
  }
  return checkFunPermission(name);
}

export function filterRoutesWithPermission(routes: any): any[] {
  if (Array.isArray(routes)) {
    const result: any = [];
    routes.forEach((route) => {
      let hasAuth = false;
      if (route.permission) {
        hasAuth = route.permission.some((name: string) => {
          const res = checkMenuPermission(name);
          // console.log('routes permission', name, STATIC_PERMISSION_NAME[name], res);
          return res;
        });
      } else {
        hasAuth = true;
      }
      // console.log('filterRoutesWithPermission', route.name, hasAuth);
      if (hasAuth) {
        route.routes = filterRoutesWithPermission(route.routes);
        result.push(route);
      }
    });
    return result;
  }
  return [];
}
