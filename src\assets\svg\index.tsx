import { ReactComponent as Manage } from '@/assets/svg/Manage.svg';
import { ReactComponent as ManageActive } from '@/assets/svg/ManageActive.svg';

import { ReactComponent as Enterprise } from '@/assets/svg/Enterprise.svg';
import { ReactComponent as EnterpriseActive } from '@/assets/svg/EnterpriseActive.svg';

import { ReactComponent as Log } from '@/assets/svg/Log.svg';
import { ReactComponent as LogActive } from '@/assets/svg/LogActive.svg';

import { ReactComponent as Device } from '@/assets/svg/Device.svg';
import { ReactComponent as DeviceActive } from '@/assets/svg/DeviceActive.svg';

import { ReactComponent as DataMonitor } from '@/assets/svg/DataMonitor.svg';
import { ReactComponent as DataMonitorActive } from '@/assets/svg/DataMonitorActive.svg';

import { ReactComponent as DataPortrait } from '@/assets/svg/DataPortrait.svg';
import { ReactComponent as DataPortraitActive } from '@/assets/svg/DataPortraitActive.svg';

import { ReactComponent as Audit } from '@/assets/svg/Audit.svg';
import { ReactComponent as AuditActive } from '@/assets/svg/AuditActive.svg';

import { ReactComponent as Chart } from '@/assets/svg/Chart.svg';
import { ReactComponent as ChartActive } from '@/assets/svg/ChartActive.svg';
import { ReactComponent as Ews } from '@/assets/svg/Ews.svg';
import { ReactComponent as EwsActive } from '@/assets/svg/EwsActive.svg';
import { ReactComponent as Rule } from '@/assets/svg/Rule.svg';
import { ReactComponent as RuleActive } from '@/assets/svg/RuleActive.svg';
import { ReactComponent as DataOverview } from '@/assets/svg/DataOverview.svg';
import { ReactComponent as DataOverviewActive } from '@/assets/svg/DataOverviewActive.svg';
import { ReactComponent as DataAnalysis } from '@/assets/svg/DataAnalysis.svg';
import { ReactComponent as DataAnalysisActive } from '@/assets/svg/DataAnalysisActive.svg';

export { ReactComponent as IconBatchUpload } from '@/assets/svg/BatchUpload.svg';
export { ReactComponent as IconBatchDownload } from '@/assets/svg/BatchDownload.svg';
export { ReactComponent as IconEdit } from '@/assets/svg/Edit.svg';
export { ReactComponent as IconBatch } from '@/assets/svg/Batch.svg';
export { ReactComponent as IconRole } from '@/assets/svg/Role.svg';
export { ReactComponent as IconLock } from '@/assets/svg/Lock.svg';
export { ReactComponent as IconClose } from '@/assets/svg/Close.svg';
export { ReactComponent as IconAudit } from '@/assets/svg/Audit.svg';
export { ReactComponent as IconProcess } from '@/assets/svg/Process.svg';
export { ReactComponent as IconAdd } from '@/assets/svg/Add.svg';
export { ReactComponent as IconEwsStat } from '@/assets/svg/ews-stat.svg';
export { ReactComponent as IconScaleUp } from '@/assets/svg/ScaleUp.svg';
export { ReactComponent as IconScaleDown } from '@/assets/svg/ScaleDown.svg';
export { ReactComponent as IconRotate } from '@/assets/svg/Rotate.svg';
export { ReactComponent as AIAvatar } from '@/assets/svg/AIAvatar.svg';
export { ReactComponent as IconRecognition } from '@/assets/svg/Recognition.svg';
export { ReactComponent as IconEnv } from '@/assets/svg/Env.svg';
export { ReactComponent as IconEnvNew } from '@/assets/svg/EnvNew.svg';
export { ReactComponent as IconImage } from '@/assets/svg/Image.svg';
export { ReactComponent as IconArrowDown } from '@/assets/svg/ArrowDown.svg';
export { ReactComponent as IconCloseCircle } from '@/assets/svg/CloseCircle.svg';
export { ReactComponent as IconPlus } from '@/assets/svg/Plus.svg';
export { ReactComponent as LogoBlue } from '@/assets/svg/logo-blue.svg';
export { ReactComponent as IconAi } from '@/assets/svg/Ai.svg';

import { FunctionComponent, SVGProps } from 'react';

interface Icons {
  [key: string]: FunctionComponent<SVGProps<SVGSVGElement>>;
}

const MENU_ICONS: Icons = {
  Manage,
  ManageActive,

  Enterprise,
  EnterpriseActive,

  Log,
  LogActive,

  Device,
  DeviceActive,

  DataMonitor,
  DataMonitorActive,

  Chart,
  ChartActive,

  DataPortrait,
  DataPortraitActive,

  Audit,
  AuditActive,

  Ews,
  EwsActive,

  Rule,
  RuleActive,

  DataOverview,
  DataOverviewActive,

  DataAnalysis,
  DataAnalysisActive,
};

export default MENU_ICONS;
