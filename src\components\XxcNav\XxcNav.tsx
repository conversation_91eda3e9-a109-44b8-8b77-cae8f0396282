import { IconAi } from '@/assets/svg';
import { useLayoutStore } from '@/store';
import { LAYOUT_MODE } from '@/store';
import help from './assets/help.png';
import './XxcNav.css';
import { useMemoizedFn } from 'ahooks';
import XxcChatHistory from '../XxcChatHistory';
import XxcMenu from '../XxcMenu';
import { history } from '@umijs/max';
import { getFirstRoutePath } from '@/utils/common.util';

export default function XxcNav() {
  const { layoutMode, setLayoutMode } = useLayoutStore();

  const setAiMode = useMemoizedFn(() => {
    setLayoutMode(LAYOUT_MODE.ai);
    history.push('/chat');
  });

  const setNormalMode = useMemoizedFn(() => {
    setLayoutMode(LAYOUT_MODE.normal);
    const path = getFirstRoutePath();
    history.push(path);
  });

  return (
    <div className="XxcNav">
      <div className="XxcNav-mode flex-center !hidden">
        <div
          className={`XxcNav-modeItem flex-center ${layoutMode === LAYOUT_MODE.ai ? 'active' : ''}`}
          onClick={setAiMode}
        >
          <IconAi className="XxcNav-modeItemIcon" />
          <span>模式</span>
        </div>
        <div
          className={`XxcNav-modeItem flex-center ${layoutMode === LAYOUT_MODE.normal ? 'active' : ''}`}
          onClick={setNormalMode}
        >
          常规模式
        </div>
      </div>
      <div className="XxcNav-modeDivider !hidden" />
      <div className="XxcNav-content xc-scrollbar-y">
        {layoutMode === LAYOUT_MODE.ai ? <XxcChatHistory /> : <XxcMenu />}
      </div>
      <div className="XxcNav-footer">
        <div className="XxcNav-footerItem">
          <img src={help} className="XxcNav-footerItemIcon" />
          <span>帮助中心</span>
        </div>
      </div>
    </div>
  );
}
