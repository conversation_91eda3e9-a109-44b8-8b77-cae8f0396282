.XxcNav {
  width: 204px;
  padding: 10px 0;
  height: calc(100vh - 66px);
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.XxcNav-mode {
  width: 158px;
  height: 38px;
  border-radius: 19px;
  display: flex;
  background: rgba(255, 255, 255, 0.5);
  padding: 2px;
  box-sizing: border-box;
}

.XxcNav-modeDivider {
  background: #DEE9F3;
  height: 1px;
  width: 158px;
  margin: 16px auto;
}

.XxcNav-modeItem {
  flex: 1;
  border-radius: 17px;
  color: #84909F;
  font-size: 14px;
  width: 76px;
  height: 34px;


  &.active,
  &:hover {
    cursor: pointer;
    font-weight: 500;
    color: #fff;
    background: linear-gradient(to right, #44B1FF, #6F5DFF);

  }
}

.XxcNav-modeItemIcon {
  width: 18px;
  height: 12px;
  margin-right: 4px;
}

.XxcNav-content {
  min-height: 0;
  flex: 1;
  overflow-x: hidden;
  width: 100%;
}

.XxcNav-footer {
  flex-shrink: 0;
  height: 44px;
  border-radius: 8px;
  display: flex;
  width: 168px;
  background: #DCE9F6;
}

.XxcNav-footerItem {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #505663;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.XxcNav-footerItemIcon {
  width: 24px;
  height: 24px;
  margin-right: 6px;
}
