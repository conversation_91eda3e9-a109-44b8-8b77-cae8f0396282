import './XxcPasswordResetModal.css';
import ReactDom from 'react-dom';
import { useGlobalStore } from '@/store/globalStore';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { Form, message } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useMemoizedFn } from 'ahooks';

import { ReactComponent as KeyOutlined } from '@/assets/user/icon-key.svg';
import { ReactComponent as LockOutlined } from '@/assets/user/icon-lock.svg';
import { ReactComponent as MobileOutlined } from '@/assets/user/icon-mobile.svg';
import { ReactComponent as CheckOutlined } from '@/assets/user/icon-check.svg';
import { request, useModel } from '@umijs/max';
import { PHONE_REG_EXP } from '@/constants/regex';
// @ts-ignore
import CryptoJS from 'crypto-js';
import { STORAGE_KEY_INIT_PASSWD } from '@/constants/common';
import { CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

export default function XxcXxcPasswordResetModal() {
  const { passwordModalVisible, togglePasswordModalVisible } = useGlobalStore();
  const [form] = Form.useForm();
  const [isSubmitEnable, setIsSubmitEnable] = useState(false);
  const [sendCaptchaEnable, setSendCaptchaEnable] = useState(true);
  const [countDown, setCountDown] = useState(0);
  const [mobile, setMobile] = useState('');
  const { getPersist } = useModel('persist');
  useEffect(() => {
    const user = getPersist('user');
    if (user?.phone) {
      setMobile(user.phone);
      form.setFieldsValue({
        mobile: user.phone,
      });
    }
  }, []);

  const hideModal = useMemoizedFn(() => {
    // const init = localStorage.getItem(STORAGE_KEY_INIT_PASSWD);
    // if (!init) {
    togglePasswordModalVisible(false);
    // }
  });

  const onFinish = useMemoizedFn(() => {
    const pwd = CryptoJS.SHA256(form.getFieldValue('new_password')).toString();
    // const oldPwd = CryptoJS.SHA256(form.getFieldValue('old_password')).toString();
    request('/v1/auth/password/reset', {
      method: 'POST',
      data: {
        mobile: mobile,
        verify_code: form.getFieldValue('verify_code'),
        // old_password: oldPwd,
        new_password: pwd,
      },
    }).then((res) => {
      if (res.code === 200) {
        message.info('密码修改成功');
        localStorage.removeItem(STORAGE_KEY_INIT_PASSWD);
        hideModal();
      } else {
        message.error(res.message || '密码修改失败');
      }
    });
  });
  const onMobileChange = useMemoizedFn((value) => {
    if (!PHONE_REG_EXP.test(value)) {
      setSendCaptchaEnable(false);
      return;
    }
    setMobile(value);
    setSendCaptchaEnable(true);
  });
  const startCountDown = useMemoizedFn((count) => {
    let second = count || 60;
    setCountDown(second);
    const timer = setInterval(() => {
      second = second - 1;
      setCountDown(second);
      if (second === 0) {
        clearInterval(timer);
      }
    }, 1000);
  });
  const onValuesChange = useMemoizedFn(async () => {
    const result = await form.getFieldsValue();
    if (result.new_password && result.verify_code && result.mobile) {
      setIsSubmitEnable(true);
    } else {
      setIsSubmitEnable(false);
    }
    return true;
  });
  const requestCaptcha = useMemoizedFn(() => {
    request('/v1/auth/password/send-reset-code', {
      method: 'POST',
      data: {
        mobile: mobile,
      },
    }).then((res) => {
      // res.data.code;
      if (res?.code === 200) {
        // form.setFieldsValue({
        //   verify_code: res.data.code,
        // });
        console.log(res?.data?.code);
        onValuesChange();
        message.info('验证码已发送');
      } else {
        message.error(res?.message || '验证码发送失败');
      }
    });
  });

  const isCaptchaEnable = useMemo(() => {
    return countDown === 0 && sendCaptchaEnable;
  }, [countDown, sendCaptchaEnable, mobile]);

  const onSendCaptcha = useMemoizedFn(() => {
    if (!isCaptchaEnable) {
      return;
    }
    startCountDown(60);
    requestCaptcha();
  });

  if (!passwordModalVisible) {
    return null;
  }

  return ReactDom.createPortal(
    <div className="XxcPasswordResetModal">
      <div className="XxcPasswordResetModal-mask" onClick={hideModal}></div>
      <div className="XxcPasswordResetModal-body">
        <div className="XxcPasswordResetModal-close" onClick={hideModal}>
          <CloseOutlined className="text-[16px]" />
        </div>
        <div className="XxcPasswordResetModal-title">修改密码</div>
        <div className="XxcPasswordResetModal-desc">请填写一下信息完成密码修改</div>
        <div className="XxcPasswordResetModal-form">
          <ProForm
            onFinish={onFinish}
            isKeyPressSubmit
            className="XxcPasswordResetModalBox"
            form={form}
            autoComplete="new-password"
            onValuesChange={onValuesChange}
            submitter={{
              render: (props) => {
                return [
                  <div
                    className={
                      'XxcPasswordResetModal-submit ' + (isSubmitEnable ? 'is-active' : '')
                    }
                    tabIndex={6}
                    key="submit"
                    onClick={() => {
                      if (isSubmitEnable) {
                        props.form?.submit?.();
                      }
                    }}
                  >
                    确认修改
                  </div>,
                ];
              },
            }}
          >
            {/* <ProFormText.Password
              fieldProps={{
                autoComplete: 'new-password',
                tabIndex: 1,
                prefix: <KeyOutlined className="text-[#C4CDD8]" />,
                className: 'XxcPasswordResetModal-account',
                maxLength: 30,
                onKeyDown: (e: any) => {
                  if (e.keyCode === 32) {
                    e.preventDefault();
                  }
                },
              }}
              name="old_password"
              label=""
              required={false}
              validateTrigger="submit"
              rules={[
                { required: true, message: '旧密码不能为空' },
                { max: 30, message: '旧密码仅支持输入30个字' },
              ]}
              placeholder="请输入旧密码"
            /> */}
            <ProFormText.Password
              fieldProps={{
                autoComplete: 'new-password',
                tabIndex: 2,
                prefix: <CheckOutlined className="text-[#C4CDD8]" />,
                className: 'XxcPasswordResetModal-password',
                onKeyDown: (e: any) => {
                  if (e.keyCode === 32) {
                    e.preventDefault();
                  }
                },
              }}
              required={false}
              name="new_password"
              label=""
              validateTrigger="submit"
              rules={[
                { required: true, message: '新密码不能为空' },
                { max: 30, message: '新密码仅支持输入30个字' },
              ]}
              placeholder="请输入新密码"
            />
            <ProFormText
              fieldProps={{
                autoComplete: 'new-password',
                tabIndex: 3,
                prefix: <MobileOutlined className="text-[#C4CDD8]" />,
                className: 'XxcPasswordResetModal-mobile',
                maxLength: 20,
                onChange: (e: any) => {
                  onMobileChange(e.target.value);
                },
                onKeyDown: (e: any) => {
                  if (e.keyCode === 32) {
                    e.preventDefault();
                  }
                },
              }}
              name="mobile"
              label=""
              disabled
              required={false}
              validateTrigger="submit"
            />
            <ProFormText
              fieldProps={{
                autoComplete: 'new-password',
                tabIndex: 4,
                prefix: <LockOutlined className="text-[#C4CDD8]" />,
                className: 'XxcPasswordResetModal-captcha',
                suffix: (
                  <div
                    className={
                      'XxcPasswordResetModal-captchaBtn ' + (isCaptchaEnable ? 'is-active' : '')
                    }
                    tabIndex={5}
                    onClick={onSendCaptcha}
                  >
                    {countDown > 0 ? `重新获取${countDown}S` : '获取验证码'}
                  </div>
                ),
                maxLength: 6,
                onKeyDown: (e: any) => {
                  if (e.keyCode === 32) {
                    e.preventDefault();
                  }
                },
              }}
              name="verify_code"
              label=""
              required={false}
              validateTrigger="submit"
              rules={[
                { required: true, message: '验证码不能为空' },
                { pattern: /^\d{6}$/, message: '验证码格式不正确' },
              ]}
              placeholder="请输入验证码"
            />
          </ProForm>
        </div>
        <div className="XxcPasswordResetModal-footer"></div>
      </div>
    </div>,
    document.body,
  );
}
