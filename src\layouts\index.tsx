import Auth from '@/wrappers/Auth';
import { Outlet, request, useModel } from '@umijs/max';
import { ConfigProvider } from 'antd';
import customTheme from './theme';
import { useLastRoute } from '@/hooks/useLastRoute';
import XxcHeader from '@/components/XxcHeader';
import XxcNav from '@/components/XxcNav';
import { LAYOUT_MODE, useLayoutStore } from '@/store';
import PasswordResetModal from '@/components/XxcPasswordResetModal/XxcPasswordResetModal';
import { useGlobalStore } from '@/store/globalStore';
import { useEffect } from 'react';
import { history } from '@umijs/max';
import { deviceAPI } from '@/api';

export default function BasicLayout() {
  const { setRegionDeviceList } = useModel('useDevice', (m) => ({
    setRegionDeviceList: m.setRegionDeviceList,
  }));
  const layoutMode = useLayoutStore((state) => state.layoutMode);
  const { togglePasswordModalVisible } = useGlobalStore();
  useLastRoute();

  useEffect(() => {
    if (layoutMode === LAYOUT_MODE.ai) {
      const root = document.getElementById('root');
      root?.classList.add('is-ai-layout');
    }
  }, [layoutMode]);

  useEffect(() => {
    // const init = +(localStorage.getItem(STORAGE_KEY_INIT_PASSWD) || 0) || -10;
    // const now = new Date().getDate();
    // if (now - init > 7) {
    //   localStorage.setItem(STORAGE_KEY_INIT_PASSWD, now.toString());
    //   togglePasswordModalVisible(true);
    // }
  }, []);
  useEffect(() => {
    request(deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setRegionDeviceList(list);
      }
    });
  }, []);

  return (
    <ConfigProvider theme={customTheme}>
      <div className="w-screen">
        <XxcHeader />
        <div className="xxc-full-page xc-scrollbar-x xc-scrollbar-y">
          <XxcNav />
          <div className="flex-1 flex px-[16px] bg-[#f4f8fd] rounded-[24px] xc-scrollbar-x xc-scrollbar-y mb-[16px] mr-[16px]">
            <article className="flex-1 flex flex-col  min-w-[1000px]">
              <Auth>
                <Outlet />
              </Auth>
            </article>
          </div>
        </div>
        <PasswordResetModal />
      </div>
    </ConfigProvider>
  );
}
