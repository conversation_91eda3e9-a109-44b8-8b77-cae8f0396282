import { ReactComponent as IconDeepThink } from '../assets/icon-deep-think.svg';
import { ReactComponent as IconOnlineSearch } from '../assets/icon-online-search.svg';
import { ReactComponent as IconSend } from '../assets/icon-send.svg';
import { ReactComponent as IconPlus } from '../assets/icon-plus.svg';
import './ChatFooter.css';
import { ContextMenuType } from '@/components/XxcContextmenu/common';
import { ContextMenuData, useContextMenuStore } from '@/store/contextMenuStore';
import { useEffect, useRef, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import TextArea from 'antd/es/input/TextArea';
import ImageUploader from '../ImageUploader/ImageUploader';
import chatService from '@/services/chatService';
import { useChatStore } from '@/store';

const CONTEXT_MENU_KEY = 'chatInput';
const chatMenuList: ContextMenuData[] = [
  {
    name: '添加图片',
    type: ContextMenuType.image,
  },
  {
    name: '更多功能期待中...',
    type: '',
  },
];
export default function ChatFooter(props: any) {
  const { scrollToBottom } = props;
  const [content, setContent] = useState('');
  const registerContextMenu = useContextMenuStore((state) => state.register);
  const updateChatState = useChatStore((state) => state.updateChatState);
  const deepThink = useChatStore((state) => state.deepThink);
  const onlineSearch = useChatStore((state) => state.onlineSearch);
  const [sendEnable, setSendEnable] = useState(false);
  const uploaderRef = useRef<HTMLInputElement>(null);
  const onContentInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const content = e.target.value;
    setContent(content);
  };
  useEffect(() => {
    if (content.trim() === '') {
      setSendEnable(false);
    } else {
      setSendEnable(true);
    }
  }, [content]);
  const showImageUpload = () => {
    uploaderRef.current?.click();
  };

  const onContextMenuClick = useMemoizedFn((data: any, targetData: any) => {
    if (data.type === ContextMenuType.image) {
      showImageUpload();
    }
  });

  const onToggleContextMenu = useMemoizedFn((visible: boolean, data: any) => {
    if (visible) {
    } else {
    }
  });

  useEffect(() => {
    registerContextMenu(CONTEXT_MENU_KEY, chatMenuList, {
      onContextMenuClick,
      onToggleContextMenu,
    });
  }, []);

  const onSendClick = useMemoizedFn(() => {
    if (!sendEnable) {
      return;
    }
    const instructType = chatService.getInstructFromText(content) || '';
    console.log('🚀 ~ onSendClick ~ instructType:', instructType);
    updateChatState({ instructInfo: { instructType } });
    chatService.sendMessage(content);
    setContent('');
    scrollToBottom();
  });

  const onKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // console.log('KeyboardEvent', e);
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      e.stopPropagation();
      onSendClick();
    }
  };
  const onDeepThinkClick = useMemoizedFn(() => {
    updateChatState({ deepThink: !deepThink });
  });
  const onOnlineSearchClick = useMemoizedFn(() => {
    updateChatState({ onlineSearch: !onlineSearch });
  });
  return (
    <div className="ChatFooter">
      <div className="ChatFooterInput">
        <ImageUploader ref={uploaderRef} />
        <TextArea
          className="ChatFooterInputArea"
          onChange={onContentInput}
          value={content}
          onPressEnter={onKeyDown}
          maxLength={1000}
          placeholder="您想咨询什么..."
        ></TextArea>
      </div>
      <div className="ChatFooterControl">
        <div className="ChatFooter-ability">
          <div
            className={'ChatFooter-deepThink ' + (deepThink ? 'is-selected' : '')}
            onClick={onDeepThinkClick}
          >
            <IconDeepThink className="ChatFooter-icon" />
            深度思考
          </div>
          <div
            className={'ChatFooter-onlineSearch ' + (onlineSearch ? 'is-selected' : '')}
            onClick={onOnlineSearchClick}
          >
            <IconOnlineSearch className="ChatFooter-icon" />
            联网搜索
          </div>
          <div className="ChatFooter-plus" data-contextmenu-key={CONTEXT_MENU_KEY}>
            <IconPlus className="ChatFooter-icon" />
          </div>
        </div>
        <div
          className={'ChatFooter-send ' + (!sendEnable ? 'is-disable' : '')}
          onClick={onSendClick}
        >
          <IconSend className="ChatFooter-icon" />
        </div>
      </div>
    </div>
  );
}
