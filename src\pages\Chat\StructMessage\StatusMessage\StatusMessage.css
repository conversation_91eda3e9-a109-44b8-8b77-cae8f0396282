.StatusMessage {
  &.is-thinking {
    width: 76px;
    height: 28px;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    overflow: hidden;

    .StatusMessage-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: thinking 1s infinite;

      &:nth-child(1) {
        background-color: #E3E7EB;
      }

      &:nth-child(2) {
        background-color: #C4CDD8;
        animation-delay: 0.3s;
      }

      &:nth-child(3) {
        background-color: #9CA8B6;
        animation-delay: 0.6s;
      }
    }
  }
}
@keyframes thinking  {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
