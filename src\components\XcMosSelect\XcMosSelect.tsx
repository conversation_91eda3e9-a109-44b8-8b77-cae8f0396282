import { useEffect, useMemo, useState } from 'react';
import { useModel } from '@umijs/max';
import { Select } from 'antd';
import { IconArrowDown } from '@/assets/svg';
import { useMemoizedFn } from 'ahooks';

import './XcMosSelect.less';
import { MOS_NAME_LIST_EN } from '@/constants/mosquitor';

const ALL_KEY = 'all';
const XcMosSelect = (props: any) => {
  const { onSelect: propOnSelect, onChange: propOnChange, className, value: _, ...others } = props;
  const { mosquitoList } = useModel('useMosquito', (m) => {
    return {
      mosquitoList: m.mosquitoList,
    };
  });
  const [selectedValues, setSelectedValues] = useState<string[]>([ALL_KEY]);

  const allCode = useMemo(() => {
    return mosquitoList.map((item: any) => item.code);
  }, [mosquitoList]);

  const onChange = useMemoizedFn((value: any) => {
    let result = value || [];
    if (!value?.length || value?.includes(ALL_KEY)) {
      result = allCode.join(',');
    } else if (Array.isArray(value)) {
      result = value?.join(',');
    }
    console.log('onChange ', value, result);
    propOnChange?.(result);
  });

  const onSelectOption = useMemoizedFn((value: any) => {
    propOnSelect?.(value);
    if (value === ALL_KEY) {
      setSelectedValues([ALL_KEY]);
      onChange([ALL_KEY]);
    } else {
      setSelectedValues((prev: string[]) => {
        let result = [...prev, value];
        result = result.filter((item) => item !== ALL_KEY);
        onChange(result);
        return result;
      });
    }
  });

  const onDeselectOption = (value: any) => {
    setSelectedValues((prev: string[]) => {
      let result = prev.filter((item) => item !== value);
      onChange(result);
      return result;
    });
  };

  const renderOptions = useMemo(() => {
    const groups = [
      { name: '全部种类', code: ALL_KEY },
      {
        name: '伊蚊属',
        options: mosquitoList.filter((item: any) =>
          [MOS_NAME_LIST_EN.白纹伊蚊, MOS_NAME_LIST_EN.埃及伊蚊].includes(item.code.toLowerCase()),
        ),
      },
      {
        name: '库蚊属',
        options: mosquitoList.filter((item: any) =>
          [
            MOS_NAME_LIST_EN.三带喙库蚊,
            MOS_NAME_LIST_EN.致倦库蚊,
            MOS_NAME_LIST_EN.淡色库蚊,
          ].includes(item.code.toLowerCase()),
        ),
      },
      {
        name: '按蚊属',
        options: mosquitoList.filter((item: any) =>
          [MOS_NAME_LIST_EN.中华按蚊, MOS_NAME_LIST_EN.斯氏按蚊].includes(item.code.toLowerCase()),
        ),
      },
      {
        name: '阿蚊属',
        options: mosquitoList.filter((item: any) =>
          [MOS_NAME_LIST_EN.骚扰阿蚊].includes(item.code.toLowerCase()),
        ),
      },
    ];
    return groups;
  }, [mosquitoList]);

  return (
    <Select
      defaultValue={[ALL_KEY]}
      mode="multiple"
      showSearch={false}
      value={selectedValues}
      options={renderOptions}
      fieldNames={{ label: 'name', value: 'code' }}
      suffixIcon={<IconArrowDown className="text-[#999]" />}
      className={'XcMosSelect' + (className ? ` ${className}` : '')}
      onSelect={onSelectOption}
      onDeselect={onDeselectOption}
      maxTagCount={0}
      maxTagPlaceholder={(values: any) => {
        const text = values.map((item: any) => item.label).join(', ');
        return (
          <div className="XcMosSelect-placeholder" title={text}>
            <div className="XcMosSelect-placeholderText">{text}</div>
          </div>
        );
      }}
      {...others}
    ></Select>
  );
};
export default XcMosSelect;
