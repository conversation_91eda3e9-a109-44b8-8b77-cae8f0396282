import React, { useEffect } from 'react';
import Panel from '@/components/Panel';
import { useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { history } from '@umijs/max';
import { Spin } from 'antd';
const HabitatRankingPanel: React.FC<{
  startTime: string;
  endTime: string;
  range: any;
  mosquitoType: string;
}> = (props) => {
  const { startTime, endTime, range, mosquitoType } = props;
  const {
    run: getHabitatRatio,
    data: habitatRatioRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getHabitatRatio,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    if (typeof range?.province === 'undefined') return;
    getHabitatRatio({
      top_n: 5,
      start_time: startTime,
      end_time: endTime,
      ...range,
      monitor_type: mosquitoType,
    });
  }, [range, mosquitoType]);

  const habitatRatio = habitatRatioRes?.habitats || [];
  const gotoAnalysis = () => {
    history.push('/data/analysis/habitat');
  };
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <span>蚊虫监测数量最多生境Top5</span>
          <div className="text-[12px] text-[#999]">
            （该模块展示的是监测蚊虫总数最多的5个生境类型）
          </div>
        </span>
      }
      actions={
        <div onClick={gotoAnalysis} className=" cursor-pointer">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      }
      contentStyle={{
        overflow: 'visible',
      }}
    >
      <div className="w-full h-full">
        <div className="flex items-center h-[34px] gap-2  text-[#797A85] border-b border-[#F0F0F0] font-[500] text-[12px]">
          <div className="w-[32px] flex items-center justify-center">排名</div>
          <div className="flex-1">生境类型</div>
          <div className="text-right flex-shrink-0 w-[160px] ">蚊虫监测总数（只）</div>
        </div>
        {habitatRatio.length > 0 ? (
          habitatRatio.map((row: any, idx: any) => {
            let rankColor = '';
            if (row.rank === 1) {
              rankColor = '#ED8061';
            } else if (row.rank === 2) {
              rankColor = '#F0B261';
            } else if (row.rank === 3) {
              rankColor = '#3381F8';
            } else {
              rankColor = '#D7D9E5';
            }

            return (
              <div key={idx} className="flex items-center gap-2 h-[51px] mb-2 last:mb-0">
                <div className="w-[32px] flex items-center justify-center">
                  <span
                    className="flex-shrink-0 w-[20px] h-[20px] flex items-center justify-center rounded-full text-white text-[14px] font-bold"
                    style={{ background: rankColor }}
                  >
                    {idx + 1}
                  </span>
                </div>
                <div className="flex-1 flex-col items-start justify-center">
                  <div className="mb-2 text-[12px] text-[#1F2134] text-one-row min-w-0">
                    {row.area_type || '--'}
                  </div>
                  <div className="bg-[#EDEFF7] rounded h-[8px]  ">
                    <div
                      className="bg-[#68DBAD] h-full rounded"
                      style={{ width: `${row.ratio}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-right  text-[#1F2134] flex-shrink-0 w-[100px] self-end">
                  {row.count}
                </div>
              </div>
            );
          })
        ) : (
          <div className="w-full h-full flex items-center justify-center text-txt-sub">
            {loading ? <Spin /> : '暂无可用生境数据'}
          </div>
        )}
      </div>
    </Panel>
  );
};

export default HabitatRankingPanel;
