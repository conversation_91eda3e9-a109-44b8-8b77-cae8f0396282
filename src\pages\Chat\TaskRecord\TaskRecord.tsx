import { useEffect, useState } from 'react';
import SmartMessage from '../StructMessage/SmartMessage/SmartMessage';
import './TaskRecord.css';

export default function TaskRecord() {
  const [list, setList] = useState<any[]>([]);
  useEffect(() => {
    setList([
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
    ]);
  }, []);
  return (
    <div className="TaskRecord">
      <div className="TaskRecord-title">任务记录</div>
      <div className="TaskRecord-content xc-scrollbar-y">
        <div className="TaskRecord-list">
          {list.map((item, index) => {
            return (
              <div className="TaskRecord-item" key={index}>
                <SmartMessage tag="智能任务" content={item.text} />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
