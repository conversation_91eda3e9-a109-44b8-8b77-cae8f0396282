import React, { useEffect } from 'react';
import { useLocation, useNavigate } from '@umijs/max';
// import { checkFunPermission } from '@/utils/permission';
import StyledTabs from '@/components/XcAntdTabs';
import RuleList from './RuleList';

export default function EnterpriseManage() {
  const tabs = [
    {
      key: 'list',
      label: '预警规则',
      code: undefined,
      children: <RuleList key="list" />,
    },
  ];
  // .filter((item) => {
  //   return checkFunPermission(item.code as string);
  // });
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location?.pathname.split('/').pop();
  const activeKey = ['list'].includes(pathname || '') ? pathname : tabs?.[0]?.key;
  useEffect(() => {
    if (!activeKey) {
      navigate('/404');
    }
  }, [activeKey]);
  const onChange = (key: string) => {
    navigate(`/settings/rule/${key}`);
  };

  if (!activeKey) {
    return null;
  }
  return (
    <div className="flex flex-1 flex-col mt-[20px] mb-[16px]">
      {/* <StyledTabs
        defaultActiveKey={activeKey}
        activeKey={activeKey}
        onChange={onChange}
        className="text-txt-main flex-1"
        destroyInactiveTabPane
        items={tabs}
        tabBarStyle={{ marginBottom: 0 }}
      ></StyledTabs> */}
      {tabs?.find((item) => item.key === activeKey)?.children}
    </div>
  );
}
