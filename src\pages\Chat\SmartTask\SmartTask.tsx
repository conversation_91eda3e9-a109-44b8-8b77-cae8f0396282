import { useEffect, useState } from 'react';

import './SmartTask.css';

export default function SmartTask() {
  const [list, setList] = useState<any[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  useEffect(() => {
    setList([
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
      {
        title: '数据分析',
        text: '描述文字描述文字描述文字描述文字描述文字',
      },
    ]);
  }, []);
  const selectTask = (index: number) => {
    setSelectedIndex(index);
    console.log(index);
  };
  return (
    <div className="SmartTask">
      <div className="SmartTask-title">智能任务</div>
      <div className="SmartTask-content xc-scrollbar-y">
        <div className="SmartTask-list">
          {list.map((item, index) => {
            return (
              <div
                className={`SmartTask-item ${selectedIndex === index ? 'is-selected' : ''}`}
                key={index}
                onClick={() => selectTask(index)}
              >
                <div className="SmartTask-itemTitle">{item.title}</div>
                <div className="SmartTask-itemContent">{item.text}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
