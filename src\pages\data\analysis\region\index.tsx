import React from 'react';
import CompositionPanel from './components/CompositionPanel';
import MosAreaData from './components/MosAreaData';

const RegionPanel: React.FC = (props: any) => {
  const { menu } = props;

  return (
    <div className="py-[20px] flex-1 h-full box-border xc-scrollbar-y">
      <div className="">
        <MosAreaData menu={menu} />
      </div>
      <div className=" mt-[20px] ">
        <CompositionPanel menu={menu} />
      </div>
    </div>
  );
};

export default RegionPanel;
