/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-25 08:20:14
 * @LastEditTime: 2025-01-14 22:32:57
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export const IconEllipse = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle opacity="0.4" cx="5" cy="5" r="4" stroke="#667085" strokeWidth="2" />
    </svg>
  );
};
