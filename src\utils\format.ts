export function formatOrgTreeOptions(list: Array<any>): Array<any> {
  if (!Array.isArray(list)) {
    return [];
  }
  return list.map((item: any) => {
    return {
      label: item.name,
      value: item.id,
      title: item.name,
      key: item.id,
      ...item,
      children: formatOrgTreeOptions(item.children),
    };
  });
}

export function getQueryIdList(list: number[]) {
  if (Array.isArray(list)) {
    return list.join(',');
  }
  return '';
}

export function removeBlankProperty(obj: any) {
  for (const key in obj) {
    if (obj[key] === '' || obj[key] === null || obj[key] === undefined) {
      delete obj[key];
    }
  }
  return obj;
}
