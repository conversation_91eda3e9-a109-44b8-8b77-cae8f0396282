import React, { useEffect } from 'react';
import UserManage from '../user_manage';
import RoleManage from '../role_manage';
import OrgManage from '../org_manage';
import { useLocation, useNavigate } from '@umijs/max';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import StyledTabs from '@/components/XcAntdTabs';
import { message } from 'antd';

export default function EnterpriseManage() {
  const tabs = [
    {
      key: 'user',
      code: STATIC_PERMISSION_CODE.用户管理,
      label: '用户管理',
      children: <UserManage key="user_cont" />,
    },
    {
      key: 'org',
      label: '部门管理',
      code: STATIC_PERMISSION_CODE.部门管理,
      children: <OrgManage key="org_cont" />,
    },
    {
      key: 'role',
      label: '角色管理',
      code: STATIC_PERMISSION_CODE.角色管理,
      children: <RoleManage key="role_cont" />,
    },
  ].filter((item) => {
    return checkFunPermission(item.code as string);
  });
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location?.pathname.split('/').pop();
  const activeKey = ['org', 'user', 'role'].includes(pathname || '') ? pathname : tabs?.[0]?.key;
  useEffect(() => {
    if (!activeKey) {
      navigate('/404');
    }
  }, [activeKey]);

  const onChange = (key: string) => {
    navigate(`/settings/enterprise/${key}`);
  };

  if (!activeKey) {
    return null;
  }
  const pageContent = tabs?.find((item) => item.key === activeKey)?.children;
  if (!pageContent) {
    message.info('无权限访问');
  }
  return (
    <div className="flex flex-1 flex-col">
      {/* <StyledTabs
        defaultActiveKey={activeKey}
        activeKey={activeKey}
        onChange={onChange}
        className="text-txt-main flex-1"
        destroyInactiveTabPane
        items={tabs}
        tabBarStyle={{ marginBottom: 0 }}
      ></StyledTabs> */}
      {pageContent}
    </div>
  );
}
