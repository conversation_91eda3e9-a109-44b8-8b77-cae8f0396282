/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-25 15:53:28
 * @LastEditTime: 2025-01-23 23:06:33
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import xxcGif from '@/assets/xxc.gif';
import { commonStyles } from '@/constants/commonStyles';
import { IInitialState } from '@/interface/shared';
import { useModel } from '@umijs/max';
import { Button, Drawer, Dropdown, Upload } from 'antd';
import { memo, useEffect, useRef, useState } from 'react';
import { IconClose } from '../Icon/IconClose';
import { AIChatBox } from '../XcChatBox';
import { Logo } from '../XcLogo';
import { useGlobalStore } from '@/store/globalStore';

export const Nav = memo(() => {
  const { togglePasswordModalVisible } = useGlobalStore();
  const [visible, setVisible] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>();
  const { logout } = useModel('user');
  const { initialState } = useModel('@@initialState');
  const [iconSrc, setIconSrc] = useState('');
  const boxRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLDivElement>(null);
  const { user } = initialState as IInitialState;
  const goHome = () => {};
  const beforeUpload = async (file: File) => {
    await new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64Str = reader.result;
        setImageUrl(base64Str as any);
        resolve({ base64: base64Str, name: file.name });
      };
      reader.onerror = (error) => reject(error);
    });
    return false;
  };
  useEffect(() => {
    function loadIcon() {
      setIconSrc(xxcGif);
    }
    const timer = setTimeout(loadIcon, 1000);
    return () => {
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    const handle = (e: any) => {
      if (!boxRef?.current?.contains(e.target) && !inputRef?.current?.contains(e.target)) {
        setVisible(false);
      }
    };
    document.documentElement.addEventListener('click', handle);
    return () => {
      document.documentElement.removeEventListener('click', handle);
    };
  }, []);

  const showPasswordModal = () => {
    togglePasswordModalVisible(true);
  };

  return (
    <nav className="h-[70px] px-[20px] py-[23px] flex  items-center justify-between">
      {/* 左侧 */}
      <div className="flex items-center">
        <div className="w-[220px] ">
          <Logo goHome={goHome} />
        </div>
        <div
          onClick={() => setVisible(!visible)}
          className={`relative w-[226px] h-[38px] cursor-pointer flex items-center ${commonStyles.baseBgColor} ${commonStyles.rounded3} p-[3px_3px_3px_43px] box-border`}
          ref={inputRef}
        >
          {iconSrc ? (
            <img src={xxcGif} className="absolute left-[3px] size-[32px] rounded-full" />
          ) : null}
          <div
            className={`${commonStyles.normalText14} ${commonStyles.baseTextColor2} text-nowrap`}
          >
            好奇的事，找星小尘聊聊
          </div>
        </div>
      </div>

      {/* 右侧 */}
      <div className="flex items-center gap-[20px]">
        {/* <Circle className="bg-[#F5F7FA] size-[50px]">
          <IconSetting className="size-[25px]" />
        </Circle>
        <Circle className="bg-[#F5F7FA] size-[50px]">
          <IconNotification className="size-[25px]" />
        </Circle> */}
        <Dropdown
          menu={{ items: [] }}
          placement="bottom"
          trigger={['click']}
          dropdownRender={() => {
            return (
              <div className="bg-black w-[250px] flex flex-col p-[20px] rounded-[8px] gap-[20px]">
                <div className="flex items-center gap-[20px]">
                  <Upload
                    name="avatar"
                    className="avatar-uploader"
                    showUploadList={false}
                    maxCount={1}
                    // action="https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload"
                    beforeUpload={beforeUpload}
                  >
                    <img
                      src={imageUrl ?? user?.avatar_img}
                      className="size-[54px] rounded-full object-cover"
                      alt="avatar"
                    />
                  </Upload>

                  <div className="flex flex-col">
                    <p className="text-[16px] text-[#F5F7FA] font-semibold">{user?.name}</p>
                    <p className="text-[14px] text-[#eee] font-normal">
                      {user?.role_names?.join?.(',')}
                    </p>
                  </div>
                </div>
                <Button
                  ghost
                  block
                  onClick={() => {
                    logout();
                  }}
                >
                  退出登录
                </Button>
              </div>
            );
          }}
        >
          <img
            src={imageUrl ?? user?.avatar_img}
            className="size-[38px] rounded-full object-cover"
            alt="avatar"
          />
        </Dropdown>
      </div>
      <Drawer
        title={false}
        placement={'top'}
        closable={false}
        mask={false}
        destroyOnClose={true}
        styles={{
          wrapper: {
            height: '100%',
            boxShadow: 'none',
          },
          content: {
            marginTop: '100px',
            marginLeft: '240px',
            width: '800px',
            height: '100%',
            borderRadius: '44px',
            backgroundColor: 'rgba(255, 255, 255, 0.70)',
            backdropFilter: 'blur(60px)',
            boxShadow: '0px 0px 20px 0px #00000026',
          },
          body: {
            padding: 0,
            margin: 0,
            height: '100%',
          },
        }}
        onClose={() => setVisible(false)}
        open={visible}
      >
        <div className="relative w-[800px] h-[calc(100vh-100px)] flex flex-col" ref={boxRef}>
          {/* close */}
          <IconClose
            className={`absolute z-[1001] size-[24px] top-[24px] right-[25px] ${commonStyles.highlight}`}
            onClick={() => setVisible(false)}
          />
          {/* xxc */}
          <div className="relative flex flex-col justify-between w-[800px] h-[calc(100vh-100px)] pb-[20px]">
            <AIChatBox />
          </div>
        </div>
      </Drawer>
    </nav>
  );
});
