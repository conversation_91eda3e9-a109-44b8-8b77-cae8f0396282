import logo from '@/assets/logo.svg';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 21:26:52
 * @LastEditTime: 2024-12-28 09:06:12
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  goHome: () => void;
}

export const Logo = (props: IProps) => {
  const { goHome } = props;

  return (
    <div className="ml-[5px] cursor-pointer flex flex-row gap-[10px] items-center" onClick={goHome}>
      <img src={logo} alt="logo" className="w-[150px] h-[34.7px]" />
    </div>
  );
};
