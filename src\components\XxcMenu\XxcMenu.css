.XxcMenu {
  padding: 0 18px;
}

.XxcMenuItem {
  font-size: 14px;
  font-weight: 600;
  color: #313747;
  cursor: pointer;
  margin-bottom: 2px;

  &.is-hover>.XxcMenuItem-cont,
  &.is-selected>.XxcMenuItem-cont {
    background: rgba(98, 101, 255, 0.15);
    color: #3437FF;
  }
}

.XxcMenuItem-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.XxcMenuItem-children {
  height: 0;
  padding-left: 26px;
  transition: all 13s ease;
  overflow: hidden;
}

.XxcMenuItem-cont {
  border-radius: 8px;
  height: 44px;
  padding: 12px;
  display: flex;
  align-items: center;
  height: 100%;
}

.is-expand {
  .XxcMenuItem-children {
    height: auto;
  }

  .XxcMenuItem-arrow {

    transform: rotate(0deg);
    color: #4448FF;
  }
}

.XxcMenuItem-arrow {
  margin-left: 10px;
  color: #C4CDD8;
  transform: rotate(-90deg);
  transition-duration: .3s;
  transform-origin: center;
  width: 12px;
  height: 12px;
}

.XxcMenuItem-sub {
  color: #84909F;
  font-size: 12px;
  font-weight: 400;

  &.is-hover,
  &.is-selected {
    color: #4448FF;
  }
}

.XxcMenu-cateBox {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.XxcMenu-cateTitle {
  height: 28px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 30px;
  color: #84909F;
  font-size: 14px;
}

.XxcMenu-cateSplit {
  background: #DEE9F3;
  height: 1px;
  margin: 2px 0;
}
