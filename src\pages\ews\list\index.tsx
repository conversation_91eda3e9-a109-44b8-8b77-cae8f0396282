import { ewsAPI } from '@/api/ews';
import { IColumns } from '@/components/XcTable/interface/search.interface';
import { DATE_FMT } from '@/utils/time.util';
import { useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { StatData } from './components/StatData';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import RuleLevel from '@/pages/rule/components/RuleLevel';

const { Text } = Typography;

export default () => {
  const tableRef = useRef<ITableRef>(null);
  const [summaryData, setSummaryData] = useState<any>({ total: 0, high: 0, low: 0, medium: 0 });
  const statRef = useRef<any>(null);
  const stateInitRef = useRef(false);

  const ewsListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: ewsAPI.getEwsList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  // 刷新数据
  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
      statRef.current.refresh();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };

  const requestTable = async (params: any) => {
    const { range_time, main_type, level, code, current, pageSize } = params;
    const payload: Record<string, any> = {
      main_type,
      level,
      code,
      page: current,
      page_size: pageSize,
    };
    if (main_type === '99') {
      delete payload.main_type;
    }
    if (level === '99') {
      delete payload.level;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    }

    const result = await ewsListRequest.run({
      ...payload,
      tenant_id: 1,
    });

    if (result.code === 200) {
      if (!stateInitRef.current) {
        setSummaryData({
          total: result.data.total,
          high: result.data.level_count.high,
          medium: result.data.level_count.medium,
          low: result.data.level_count.low,
        });
      }
      // stateInitRef.current = true;
      return {
        total: result.data.total,
        data: result.data.list,
      };
    } else {
      // stateInitRef.current = true;
      message.error(result.message || '请求失败');
    }
    return {
      total: 0,
      data: [],
    };
  };
  const columns: IColumns[] = [
    {
      title: '预警类型',
      width: 150,
      key: 'main_type',
      hideInTable: true,
      initialValue: '99',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        { label: '全部预警类型', value: '99' },
        { label: '环境', value: 'environment' },
        { label: '成蚊', value: 'mosquito' },
        { label: '伊蚊成蚊', value: 'mosquito_ae' },
      ],
    },
    {
      title: '预警等级',
      width: 150,
      key: 'level',
      hideInTable: true,
      initialValue: '99',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        {
          label: '全部预警等级',
          value: '99',
        },
        {
          label: '高等',
          value: 'high',
        },
        {
          label: '中等',
          value: 'medium',
        },
        {
          label: '低等',
          value: 'low',
        },
      ],
    },
    {
      title: '入库时间',
      width: 235,
      key: 'range_time',
      realtime: true,
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '  ',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索预警编号/设备名称',
    },
    {
      title: '预警编号',
      dataIndex: 'id',
      key: 'id',
      link: true,
      fixed: 'left',
      render: (value) => {
        return (
          <Text className="text-txt-main  " ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    // {
    //   title: '设备编号 ',
    //   dataIndex: 'device_code',
    //   key: 'device_code',
    //   hideInSearch: true,
    //   render: (value) => {
    //     return (
    //       <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
    //         {value}
    //       </Text>
    //     );
    //   },
    // },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '监测点位置',
      dataIndex: 'monitor_point',
      key: 'monitor_point',
      width: 300,
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '触发时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (value) => {
        const txt = dayjs(value).format(DATE_FMT.DATE_TIME);
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: txt }}>
            {txt}
          </Text>
        );
      },
    },
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      key: 'rule_name',
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '预警类型',
      dataIndex: 'main_type',
      key: 'main_type',
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '触发条件',
      dataIndex: 'condition',
      key: 'condition',
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '指标值',
      dataIndex: 'current_value',
      key: 'current_value',
      render: (value) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '预警等级',
      dataIndex: 'level',
      key: 'level',
      render: (value: string) => {
        return <RuleLevel value={value} />;
      },
    },
  ];

  return (
    <div className="flex-1 flex ">
      <div className="flex-1 mb-[16px]">
        <XcTableNew
          ref={tableRef}
          loading={ewsListRequest.loading}
          columns={columns}
          request={requestTable}
          extend={<StatData data={summaryData} />}
          rowSelection={null}
          operator={null}
          searchTitle="预警中心"
        />
      </div>
    </div>
  );
};
