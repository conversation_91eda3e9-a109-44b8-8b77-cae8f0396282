import { regxUtil } from '@/utils/regx.util';
import { forwardRef, memo, useEffect, useRef, useState } from 'react';
import { useModel } from '@umijs/max';
import { cloneDeep } from 'lodash';
import { useMemoizedFn } from 'ahooks';
import '@/css/vw/CircleRing.css';
import _ from 'lodash';
import '@/css/vw/BigScreen.css';
import { getBoundaryPoints, sortPolygonPoints } from './util';
import { createXcPoint } from './XcPoint';
interface IPoint {
  icon: any;
  longitude: number;
  latitude: number;
  status: number;
  width: number;
  height: number;
  [key: string]: any;
  onClick?: (data: any) => void;
}

interface IProps {
  use2d?: boolean;
  heading?: number;
  tilt?: number;
  // 名称
  name?: string;
  longitude?: number;
  latitude?: number;
  zoom?: number;
  mapType?: 'BMAP_NORMAL_MAP' | 'BMAP_EARTH_MAP' | 'BMAP_SATELLITE_MAP';
  points?: IPoint[];
  children?: React.ReactNode;
}

const BaiduMapNew = memo(
  forwardRef((props: IProps, ref: any) => {
    const { use2d } = props;
    const containerRef = useRef<HTMLDivElement>(null);
    const bmapRef = useRef<any>(null);
    const [points, setPoints] = useState<any[]>([]);
    const [polygonPoints, setPolygonPoints] = useState<any[]>([]);
    const [inited, setInited] = useState(false);
    const timerRef = useRef<any>(0);
    const { region } = useModel('useMenu', (m) => ({
      region: m.region,
    }));
    const { streetDeviceList, districtDeviceList, cityDeviceList, provinceDeviceList } = useModel(
      'useDevice',
      (m) => ({
        streetDeviceList: m.streetDeviceList,
        districtDeviceList: m.districtDeviceList,
        cityDeviceList: m.cityDeviceList,
        provinceDeviceList: m.provinceDeviceList,
      }),
    );
    const mapReady = useMemoizedFn(() => {
      if (containerRef.current?.style) {
        containerRef.current.style.opacity = '1';
      }
    });
    // 初始化地图
    const initMap = useMemoizedFn(() => {
      if (bmapRef.current) {
        return;
      }
      if (containerRef.current && window.BMapGL) {
        // GL版命名空间为BMapGL
        // 按住鼠标右键，修改倾斜角和角度
        bmapRef.current = new window.BMapGL.Map(containerRef.current, {
          minZoom: 3,
          maxZoom: 21,
        });
        bmapRef.current.addEventListener('tilesloaded', mapReady);

        //开启鼠标滚轮缩放
        bmapRef.current.enableScrollWheelZoom();
        bmapRef.current.setDisplayOptions({
          // poiText: false,
          poiIcon: false,
          // overlay: false,   // 是否显示覆盖物
          layer: true, // 是否显示叠加图层，地球模式暂不支持
          building: true, // 是否显示3D建筑物（仅支持WebGL方式渲染的地图）
          street: true, // 是否显示路网
        });

        setInited(true);
      }
    });
    // 初始化
    useEffect(() => {
      initMap();
      return () => {
        if (bmapRef.current) {
          bmapRef.current.removeEventListener('tilesloaded', mapReady);
          bmapRef.current.clearOverlays();
          bmapRef.current.destroy();
          bmapRef.current = null;
        }
      };
    }, []);
    const showPolygon = useMemoizedFn((list?: any) => {
      if (!bmapRef.current) return;
      const sortList = sortPolygonPoints(list || polygonPoints);

      if (sortList.length >= 3) {
        let polygon = new window.BMapGL.Polygon(sortList, {
          strokeColor: 'blue',
          strokeWeight: 2,
          strokeOpacity: 1,
          fillColor: '#f70',
          fillOpacity: 1,
          zIndex: 100, // 使用 zIndex 控制层级
        });
        bmapRef.current.addOverlay(polygon);
      }
    });

    const showBoundary = useMemoizedFn(
      _.debounce(async (city) => {
        if (!bmapRef.current) return;
        try {
          const simplified = await getBoundaryPoints(city);
          // 创建一个半透明的 Polygon 来表示行政区划
          const boundaryPolygon = new window.BMapGL.Polygon(simplified, {
            strokeColor: '#2661BF',
            strokeWeight: 5,
            strokeOpacity: 0.85,
            fillColor: '#2894DC',
            fillOpacity: 0.95,
            zIndex: 1, // 较低的 zIndex
          });
          bmapRef.current.addOverlay(boundaryPolygon);
        } catch (error) {
          console.error('获取边界数据失败', error);
        }
      }, 500),
    );

    const getDevice = useMemoizedFn(async (region: any) => {
      // 1. 获取固定区域设备, 轮询每个设备状态，展示在地图上
      let dataSource: any[] = [];
      const devices: any[] = [];
      if (region.device_id) {
        const streetKey = `${region.province}.${region.city}.${region.district}.${region.street}`;
        const streeDevices = streetDeviceList[streetKey]?.filter(
          (item) => item.key === streetKey + `.${region.device_id}`,
        );
        dataSource = streeDevices ?? [];
      } else if (region.street) {
        const streetKey = `${region.province}.${region.city}.${region.district}.${region.street}`;
        dataSource = streetDeviceList[streetKey] ?? [];
      } else if (region.district) {
        const districtKey = `${region.province}.${region.city}.${region.district}`;
        dataSource = districtDeviceList[districtKey] ?? [];
      } else if (region.city) {
        const cityKey = `${region.province}.${region.city}`;
        dataSource = cityDeviceList[cityKey] ?? [];
      } else if (region.province) {
        const provinceKey = `${region.province}`;
        dataSource = provinceDeviceList[provinceKey] ?? [];
      }
      if (dataSource && dataSource.length > 0) {
        const m = cloneDeep(dataSource);
        m.forEach((it) => {
          devices.push({ ...it });
        });
      }
      return devices;
    });
    const addPoints = useMemoizedFn((point: any) => {
      if (!point) {
        return;
      }

      const devicePoints = point.filter((item: any) => item.gps);
      const positionList: any = [];
      for (let i = 0; i < devicePoints?.length; i++) {
        const { ...rest } = devicePoints[i];
        const [lng, lat] = rest.gps.split(',');
        if (rest.gps && lng && lat && regxUtil.poi.test(lng) && regxUtil.poi.test(lat)) {
          const p = new BMapGL.Point(lng, lat);
          positionList.push(p);
          let customOverlay = new (BMapGL as any).CustomOverlay(createXcPoint, {
            point: p,
            properties: {
              ...rest,
            },
          });
          bmapRef.current.addOverlay(customOverlay);
        }
      }
      setPolygonPoints(positionList);
      //showPolygon(positionList);
    });

    //设置中心点
    const chargeCenterByName = useMemoizedFn((name: string, options: any) => {
      if (bmapRef.current) {
        const { zoom } = options;
        requestAnimationFrame(() => {
          bmapRef.current.centerAndZoom(name, zoom);
          console.log('chargeCenterByName ');
        });
      }
    });

    // 设置中心点
    const chargeCenterByPoi = useMemoizedFn((poi: any, options: any) => {
      const { lng, lat } = poi;
      if (
        bmapRef.current &&
        lng &&
        lat &&
        regxUtil.poi.test(lng.toString()) &&
        regxUtil.poi.test(lat.toString())
      ) {
        const { zoom = 19 } = options;
        const center = new BMapGL.Point(lng, lat);
        console.log('chargeCenterByPoi ', center);
        // bmapRef.current.flyTo(center, zoom);
        bmapRef.current.centerAndZoom(center, zoom);
      }
    });

    const replacePoints = useMemoizedFn((points: any) => {
      bmapRef.current.clearOverlays();
      timerRef.current = setTimeout(() => {
        addPoints(points);
      }, 2000);
    });

    // 更新点位
    useEffect(() => {
      if (bmapRef.current && points) {
        replacePoints(points);
      }
      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }, [points, inited]);

    useEffect(() => {
      if (!inited) {
        return;
      }
      async function updateRegion() {
        const devices = await getDevice(region);
        setPoints(devices);
        if (region.device_id) {
          const first = devices[0];
          const [lng, lat] = first?.gps?.split(',') ?? [];
          setTimeout(() => {
            if (lng && lat) {
              chargeCenterByPoi({ lng: Number(lng), lat: Number(lat) }, { zoom: 15 });
            } else {
              chargeCenterByName(
                `${region.province}${region.city}${region.district}${region.street}`,
                {
                  zoom: 15,
                },
              );
            }
          }, 500);
        } else if (region.street) {
          const streetStr = `${region.province}${region.city}${region.district}${region.street}`;
          chargeCenterByName(streetStr, {
            zoom: 15,
          });
        } else if (region.district) {
          const districtStr = `${region.province}${region.city}${region.district}`;
          chargeCenterByName(districtStr, { zoom: 13 });
        } else if (region.city) {
          const cityStr = `${region.province}${region.city}`;
          chargeCenterByName(cityStr, { zoom: 12 });
        } else if (region.province) {
          chargeCenterByName(`${region.province}`, { zoom: 10 });
        }

        if (region.city) {
          const cityStr = `${region.province}${region.city}`; // 城市
          showBoundary(cityStr);
        }
      }
      updateRegion();
    }, [region, inited]);

    const deviceClickHandler = useMemoizedFn((e) => {
      if (e.data.type === 'deviceClick') {
        const gps = e.data?.device?.gps || '';
        if (gps) {
          const [lng, lat] = gps.split(',');
          chargeCenterByPoi({ lng: Number(lng), lat: Number(lat) }, { zoom: 21 });
        }
      }
    });

    useEffect(() => {
      window.addEventListener('message', deviceClickHandler);
      return () => {
        window.removeEventListener('message', deviceClickHandler);
      };
    }, []);

    useEffect(() => {
      if (!inited) {
        return;
      }
      if (use2d) {
        bmapRef.current.setMapStyleV2({ styleId: '' });
        bmapRef.current?.setTilt(0);
      } else {
        bmapRef.current.setMapStyleV2({ styleId: 'f775090aa3e983de0ddfb12bda62dc8c' });
        bmapRef.current?.setTilt(23);
      }
    }, [inited, use2d]);

    return (
      <div ref={containerRef} className="map-container w-full h-full" style={{ opacity: 0 }}></div>
    );
  }),
);

export default BaiduMapNew;
