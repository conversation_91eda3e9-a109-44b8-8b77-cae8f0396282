import { useMemoizedFn } from 'ahooks';
import { forwardRef, useState } from 'react';
import './ImageUploader.css';

export interface ImageUploaderProps {
  onChange?: (imageList: any[]) => void;
}
export default forwardRef((props: ImageUploaderProps, ref: any) => {
  const { onChange } = props;
  const [imageList, setImageList] = useState<any[]>([]);
  const onChangeImageList = useMemoizedFn((imageList: any[]) => {
    onChange?.(imageList);
  });
  const handleChange = useMemoizedFn((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result;
        if (result) {
          setImageList((prev) => [...prev, { url: result }]);
          onChangeImageList([...imageList, { url: result }]);
        }
      };
      reader.readAsDataURL(file);
    }
  });
  const removeImage = useMemoizedFn((index: number) => {
    const newImageList = [...imageList];
    newImageList.splice(index, 1);
    setImageList(newImageList);
    onChangeImageList(newImageList);
  });

  if (!imageList.length) {
    return null;
  }

  return (
    <div className="ImageUploader">
      <input
        ref={ref}
        type="file"
        onChange={handleChange}
        accept="image/*"
        className="ImageUploader-imageInput"
      />
      <div className="ImageUploader-list">
        {imageList.map((item, index) => {
          return (
            <div className="ImageUploader-item" key={index}>
              <img src={item.url} className="ImageUploader-itemImg" />
              <div className="ImageUploader-itemClose" onClick={() => removeImage(index)}></div>
            </div>
          );
        })}
      </div>
    </div>
  );
});
