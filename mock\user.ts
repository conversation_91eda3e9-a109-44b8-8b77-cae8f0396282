/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-25 14:01:23
 * @LastEditTime: 2024-12-25 14:08:23
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { Request, Response } from 'express';

const getUser = (req: Request, res: Response) => {
  // TODO 权限验证
  res.send({
    success: true,
    data: {
      name: '超级管理员',
      avatar:
        'https://s3-alpha-sig.figma.com/img/57d3/d250/790e98129931897251abd3915a931233?Expires=1736121600&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=JC2kgdYIgG4X1yZgP0YlIl3OKZ~bMwEVHHNT9SHwi5yUHBI~PdRecojG02O3RLSl6zeXmnAF~I8-3tlsyZ5NgFOSxlfbAdVyPqrC55cIL~EHUtKRwCfig0FBTrp6WbyoXghk1LY7er3fsLIIvBnOaf77rRMM649aBisewjHecPRehELdH-ogoCnYLHEmGd5835bxc1Fv20yBDSu0QkQYTpdHHWYfiK~PV~DAOYkHB2PU6gqIXgcGlossxlshuZfDmR2SwmZLhG2d59ueyEupsvSvxjlOUkBIvU9yv8c8eYlf~HaoeyCjIMwUAQ7t6CyyADXcmZf9kisfPtIDz~1TYg__',
      role: '超级管理员',
    },
  });
};

export default {
  'GET /api/currentUser': getUser,
};
