import ReactDOM from 'react-dom';
import { history, styled, useModel } from '@umijs/max';
import { ReactComponent as HeaderIcon } from './assets/header-icon.svg';
import { ReactComponent as HomeIcon } from './assets/home.svg';
import { getFirstRoutePath } from '@/utils/common.util';
import '@/css/vw/BigScreen.css';
import BigScreenTree from './components/BigScreenTree';
import headImg from './assets/bg.png';
import title from './assets/title1.png';
import SpeciesCompositionDonutChart from './components/SpeciesCompositionDonutChart';
import DailyActivityLineChart from './components/DailyActivityLineChart';
import GroupProgressList from './components/GroupProgressList';
import WarningList from './components/WarningList';
import DeviceDrawer from './components/DeviceDrawer';
import { useEffect, useMemo, useState } from 'react';
import BaiduMapNew from '@/components/BaiduMap/BaiduMapNew';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import { useMemoizedFn } from 'ahooks';
import useMosquitoModel from '@/models/useMosquito';

const MODAL_TYPE = {
  none: 'none',
  device: 'device',
};

function BigScreen() {
  const [modalType, setModalType] = useState(MODAL_TYPE.none);
  const [device, setDevice] = useState<any>(null);
  const { region } = useModel('useMenu', (m) => ({
    region: m.region,
  }));
  const { mosquitoList } = useMosquitoModel();
  const allMosCode = mosquitoList.map((item: any) => item.code).join(',');
  const goHome = () => {
    const path = getFirstRoutePath();
    history.push(path || '/welcome');
  };
  const closeModal = () => {
    setModalType(MODAL_TYPE.none);
  };

  const showDeviceDrawer = () => {
    setModalType(MODAL_TYPE.device);
  };
  const handler = useMemoizedFn((e: any) => {
    if (e.data.type === 'deviceClick') {
      setDevice(e.data.device);
      showDeviceDrawer();
    }
  });
  useEffect(() => {
    window.addEventListener('message', handler);
    return () => {
      window.removeEventListener('message', handler);
    };
  }, []);

  const [startTime, endTime] = useMemo(() => {
    const defaultRange = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
    return defaultRange;
  }, []);

  const screenRegion = region;
  const is2d = localStorage.getItem('use2d') === '1';
  const [use2d, setUse2d] = useState(is2d);
  const toggle2d = useMemoizedFn(() => {
    setUse2d(!use2d);
    localStorage.setItem('use2d', use2d ? '0' : '1');
  });

  return ReactDOM.createPortal(
    <div className={'BigScreen' + (use2d ? ' ' : ' is-2dMap')}>
      <div className={'header'}>
        <div className={'headImg'} style={{ backgroundImage: `url(${headImg})` }}>
          <img src={title} className={'headTitle'}></img>
        </div>
        <div className={'headerLeft'}>
          <HomeIcon onClick={goHome} className={'homeIcon'}></HomeIcon>
          <div className={'headerSettings'}>
            <HeaderIcon className={'headerIcon'}></HeaderIcon>面板设置
          </div>
        </div>
      </div>
      <div className={'mainContent'}>
        <div className={'xc-scrollbar-none leftContainer'}>
          <div className="BigScreen-2dBtn" onClick={toggle2d}>
            {use2d ? '2D' : '3D'}
          </div>
          <BigScreenTree></BigScreenTree>
          <div className={'leftContainerCont'}>
            <div className={'todayDataTitle'}>近7天监测数据</div>
            <SpeciesCompositionDonutChart
              region={screenRegion}
              startTime={startTime}
              endTime={endTime}
              monitorType={allMosCode}
            />
            <DailyActivityLineChart
              region={screenRegion}
              startTime={startTime}
              endTime={endTime}
              monitorType={allMosCode}
            />
          </div>
        </div>
        <div className={'baiduMapContainer'}>
          <BaiduMapNew use2d={use2d} />
        </div>
        <div className={'xc-scrollbar-none rightContainer'}>
          <GroupProgressList startTime={startTime} endTime={endTime} monitorType={allMosCode} />
          <WarningList startTime={startTime} endTime={endTime} monitorType={allMosCode} />
        </div>
      </div>
      {modalType === MODAL_TYPE.device ? (
        <DeviceDrawer onClose={closeModal} device={device} monitorType={allMosCode} />
      ) : null}
    </div>,
    document.body,
  );
}

export default BigScreen;
