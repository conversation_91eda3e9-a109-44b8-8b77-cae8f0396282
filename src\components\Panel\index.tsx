import React from 'react';
import './index.css';
interface PanelProps {
  title: React.ReactNode;
  actions?: React.ReactNode;
  children: React.ReactNode;
  contentStyle?: React.CSSProperties;
  boxStyle?: React.CSSProperties;
}

const Panel: React.FC<PanelProps> = ({ title, actions, children, contentStyle, boxStyle }) => {
  return (
    <div className="Panel" style={boxStyle}>
      <div className="PanelHead">
        <div className="PanelTitle">{title}</div>
        {actions}
      </div>
      <div className="PanelContent" style={contentStyle}>
        {children}
      </div>
    </div>
  );
};

export default Panel;
