import '@/css/vw/WarningList.css';
import { ewsAPI } from '@/api/ews';
import { useRequest } from '@umijs/max';
import { useEffect, useState } from 'react';
import _ from 'lodash';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import { ReactComponent as WarningIcon } from '../assets/waring-icon.svg';
import { Carousel } from 'antd';
import dayjs from 'dayjs';

const ewsLevel = [
  {
    name: '高能预警',
    key: 'high',
    color: '#F98581',
  },
  {
    name: '中等预警',
    key: 'medium',
    color: '#E8FD85',
  },
  {
    name: '低等预警',
    key: 'low',
    color: '#97FCA0',
  },
];

const LEVEL_MAP = {
  高: 'high',
  中: 'medium',
  低: 'low',
};

export default function WarningList({ startTime, endTime }: any) {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [levelCount, setLevelCount] = useState({ high: 0, medium: 0, low: 0 });
  const ewsListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: ewsAPI.getEwsList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  useEffect(() => {
    const [startTime1, endTime1] = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
    ewsListRequest
      .run({
        page: 1,
        page_size: 20,
        start_time: startTime || startTime1,
        end_time: endTime || endTime1,
        tenant_id: 1,
      })
      .then((res) => {
        const lc = res?.data?.level_count || { high: 0, medium: 0, low: 0 };
        const total = _.sum(Object.values(lc));
        setTotal(total);
        setLevelCount(lc);
        setDataSource(res?.data?.list || []);
      });
  }, []);
  return (
    <div className="wl-card-container">
      <div className="wl-header">
        <div className="wl-header-title">
          <WarningIcon />
          近7天预警数据
        </div>
        <div className="wl-header-count">
          <span>当前预警数</span>
          <div className="wl-header-value" style={{ color: '#68DBAD' }}>
            {total}
          </div>
        </div>
      </div>
      <div className="wl-split-line"></div>
      <div className="wl-level-list">
        {ewsLevel.map((level, lidx) => {
          return (
            <div className="wl-level" key={lidx}>
              <div className="wl-level-title">{level.name}</div>
              <div className="wl-level-circle" style={{ color: level.color }}>
                {levelCount[level.key as keyof typeof levelCount]}
              </div>
            </div>
          );
        })}
      </div>
      <div className="wl-list">
        {dataSource.length > 0 ? (
          <Carousel
            dots={false}
            vertical
            autoplay
            slidesToShow={dataSource.length > 5 ? 5 : dataSource.length}
            arrows={false}
            autoplaySpeed={3000}
            infinite={true}
          >
            {dataSource.map((item, idx) => {
              const text = item.condition?.split(/\s+/)?.[0] + ' ' + item.current_value;
              return (
                <div className="wl-list-row" key={idx}>
                  <div
                    className={`wl-list-right is-${LEVEL_MAP[item.level as keyof typeof LEVEL_MAP] || 'low'}`}
                    title={text}
                  >
                    {text}
                  </div>
                  <div className="wl-list-left">
                    <div className="wl-device text-one-row">{item.monitor_point || ''}</div>
                    <div className="wl-time">{dayjs(item.created_at).format('MM-DD HH:mm:ss')}</div>
                  </div>
                </div>
              );
            })}
          </Carousel>
        ) : (
          <div className="wl-list-empty">暂无数据</div>
        )}
      </div>
    </div>
  );
}
