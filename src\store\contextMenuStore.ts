import { ContextMenuType } from '@/components/XxcContextmenu/common';
import { create } from 'zustand';
import { EmptyFunction } from '@/utils/common.util';

export interface ContextMenuData {
  name: string;
  type: ContextMenuType | string;
}

export interface ContextMenuState {
  data: Record<string, ContextMenuData[]>;
  current: {
    menus: ContextMenuData[];
    data: any;
    position: { x: number; y: number };
  };
  menuKey: string;
  visible: boolean;
  showContextMenu: (config: {
    menuKey: string;
    position?: { x: number; y: number };
    data: any;
  }) => void;
  hideContextMenu: () => void;
  register: (
    key: string,
    menus: ContextMenuData[],
    callbacks: {
      onContextMenuClick: (data: any, targetData: any) => void;
      onToggleContextMenu: (visible: boolean, data: any) => void;
    },
  ) => void;
  getToggleCallback: (key: string) => (visible: boolean, data: any) => void;
  getClickCallback: (key: string) => (data: any, targetData: any) => void;
}

export const contextMenuCallbackMap = new Map<string, (data: any, targetData: any) => void>();
export const contextMenuToggleMap = new Map<string, (visible: boolean, data: any) => void>();

const defaultMenuData = {
  menus: [],
  data: {},
  position: { x: 0, y: 0 },
};

export const useContextMenuStore = create<ContextMenuState>((set) => ({
  data: {},
  current: defaultMenuData,
  menuKey: '',
  visible: false,
  showContextMenu: (config: {
    menuKey: string;
    position?: { x: number; y: number };
    data: any;
  }) => {
    const { menuKey, position, data } = config;
    set((prevState: any) => {
      const menus = prevState.data[menuKey];
      if (!menus) {
        return { visible: false };
      }
      return {
        current: { menus, data, position: position || defaultMenuData.position },
        menuKey,
        visible: true,
      };
    });
    contextMenuToggleMap.get(menuKey)?.(true, data);
  },
  hideContextMenu: () => {
    set((state) => {
      contextMenuToggleMap.get(state.menuKey)?.(false, state.current.data);
      return { visible: false, current: defaultMenuData };
    });
  },
  register: (
    menuKey: string,
    menus: ContextMenuData[],
    callbacks: {
      onContextMenuClick: (data: any, targetData: any) => void;
      onToggleContextMenu: (visible: boolean, data: any) => void;
    },
  ) => {
    const { onContextMenuClick, onToggleContextMenu } = callbacks;
    contextMenuCallbackMap.set(menuKey, onContextMenuClick);
    contextMenuToggleMap.set(menuKey, onToggleContextMenu);
    set((prevState: any) => {
      return { data: { ...prevState.data, [menuKey]: menus } };
    });
  },
  getToggleCallback: (menuKey: string) => {
    const callback = contextMenuToggleMap.get(menuKey);
    return callback || EmptyFunction;
  },
  getClickCallback: (menuKey: string) => {
    const callback = contextMenuCallbackMap.get(menuKey);
    return callback || EmptyFunction;
  },
}));
