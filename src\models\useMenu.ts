import { IRegion } from '@/interface/regionDevice';
import { useModel } from '@umijs/max';
import { useEffect, useState } from 'react';
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-10 16:42:11
 * @LastEditTime: 2025-02-13 01:27:39
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const useMenu = () => {
  const { regionDeviceList } = useModel('useDevice', (m) => ({
    regionDeviceList: m.regionDeviceList,
  }));
  const { setEnvs } = useModel('useEnv', (m) => ({
    setEnvs: m.setEnvs,
  }));
  // 菜单
  const [menu, setMenu] = useState<any>([]);
  const [defaultOpenKeys, setDefaultOpenKeys] = useState<any>([]);
  // 当前区域
  const [region, setRegion] = useState<IRegion>({} as any);
  const handleReginDeviceList = (data: any) => {
    // 增加key，label 处理children
    function handleData(ds: any, parent_name = '') {
      if (!ds?.length) {
        return;
      }
      for (let i = 0; i < ds.length; i++) {
        let item = ds[i];
        item.key = (parent_name ? `${parent_name}.` : '') + item.name;
        item.value = item.key;
        item.label = item.name;
        let deviceList: any = [];
        let key = item.name;
        if (item.devices) {
          if (item.level === 'province') {
            // key = `province.city.district.street`
            key = `${item.key}....`;
          } else if (item.level === 'city') {
            key = `${item.key}...`;
          } else if (item.level === 'district') {
            key = `${item.key}..`;
          } else {
            key = `${item.key}.`;
          }
          deviceList = item.devices.map((it: any) => {
            return {
              key: key + it.id,
              value: key + it.id,
              label: it.name,
              ...it,
            };
          });
        }
        item.children = item.children || [];
        if (item.children && item.children.length > 0) {
          handleData(item.children, item.key);
        }
        item.children = [...deviceList, ...item.children];
      }
    }
    handleData(data);
    // 默认选中最高节点第一个节点
    setMenu(data);
    const [first] = data;
    if (first) {
      setDefaultOpenKeys([first.key]);
      // 环境信息
      setEnvs((s) => ({
        ...s,
        device_id: first.id,
        device_status: first.status,
        co2: first.co2,
        temp: first.temp,
        hum: first.hum,
        speed: first.speed,
        area_type: first.area_type,
      }));
    }
  };

  // 监听regionDeviceList变化，组合菜单
  useEffect(() => {
    if (regionDeviceList.length === 0) {
      setMenu(regionDeviceList);
      return;
    }
    // 处理菜单
    handleReginDeviceList(regionDeviceList);
  }, [regionDeviceList]);

  return { region, setRegion, menu, setMenu, defaultOpenKeys, setDefaultOpenKeys };
};

export default useMenu;
