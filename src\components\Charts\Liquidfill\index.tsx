/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-12 14:57:21
 * @LastEditTime: 2025-02-12 09:08:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import 'echarts-liquidfill';
import { memo } from 'react';
import EChartsCommon from '../Charts/EChartsCommon';

export const LiquidFill = memo((props: any) => {
  const { percent } = props;

  // https://github.com/ecomfe/echarts-liquidfill?tab=readme-ov-file
  const option = {
    series: [
      {
        type: 'liquidFill',
        data: [percent],
        center: ['50%', '50%'],
        direction: 'right',
        radius: '80%',
        shape: 'circle',
        label: {
          show: true,
          align: 'center',
          normal: {
            formatter: `{top| ${(percent * 100).toFixed(2)}%} \n {bottom|监测设备率 }`,
            fontWeight: 400,
            color: '#999',
            fontSize: 14,
            rich: {
              top: {
                color: '#ffffff',
                fontSize: 16,
                fontWeight: 'bold',
                lineHeight: 1.5,
              },
              bottom: {
                color: '#ffffff',
                fontSize: 10,
                lineHeight: 2,
              },
            },
          },
        },
        color: [
          {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#1792FE', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#7D40FF', // 100% 处的颜色
              },
            ],
            global: false,
          },
        ],
        outline: {
          show: true,
          borderDistance: 0,
          itemStyle: {
            borderWidth: 0,
            color: 'none',
            borderColor: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#1792FE', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#7D40FF', // 100% 处的颜色
                },
              ],
              global: false,
            },
          },
        },
        backgroundStyle: {
          borderWidth: 0,
          color: 'rgba(20, 20, 44, 0.8)',
        },
        // 禁止波纹
        amplitude: Number(percent) === 1 ? 0 : '8%',
        waveAnimation: Number(percent) === 1 ? false : true,
        itemStyle: {
          opacity: Number(percent) === 0 ? 0 : 1,
        },
        grid: {
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
        },
      },
      {
        name: '',
        type: 'pie',
        radius: ['90%', '100%'],
        center: ['50%', '50%'],
        startAngle: 0,
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: percent,
            name: '监测率',
            label: {
              show: false,
            },
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: '#1792FE', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#7D40FF', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            value: 1 - percent,
            name: '未监测率',
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            itemStyle: {
              color: '#343561',
            },
          },
        ],
      },
    ],
  };

  return <EChartsCommon option={option} />;
});
