import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import hljs from 'highlight.js';
import 'highlight.js/styles/monokai.min.css'; // 代码高亮主题
import { useState } from 'react';
import { DEFAULT_IMAGE } from '@/constants/common';
import { Image } from 'antd';
interface CodeBlockProps {
  language: string;
  value: string;
}

interface MarkdownMessageProps {
  message: any;
}
// 自定义代码块渲染器
const CodeBlock = (props: CodeBlockProps) => {
  const { language, value } = props;
  const highlightedCode = hljs.highlight(value || '', {
    language: language || 'plaintext',
  }).value;

  return (
    <pre className="p-4">
      <code
        className={`hljs language-${language}`}
        dangerouslySetInnerHTML={{ __html: highlightedCode }}
      />
    </pre>
  );
};

const MsgImage = (props: IMsgImage) => {
  const { src } = props;
  const [preview, setPreview] = useState(true);
  return (
    <Image
      width={120}
      height={120}
      src={src}
      preview={preview}
      style={{ borderRadius: 16, objectFit: 'cover' }}
      onError={() => {
        setPreview(false);
      }}
      fallback={DEFAULT_IMAGE}
    />
  );
};
const genImg = ({ src }: any) => {
  return <MsgImage src={src} />;
};

function MarkdownMessage(props: MarkdownMessageProps) {
  const { message } = props;
  const { content } = message || {};
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        p: 'div',
        img: genImg,
        // @ts-ignore
        code({ node, inline, className, children, ...props }) {
          const match = /language-(\w+)/.exec(className || '');
          // @ts-ignore
          const txt = node?.children?.[0]?.value || '';
          return !inline && match && txt ? (
            <CodeBlock language={match[1]} value={txt || ''} />
          ) : (
            <code className={className} {...props}>
              {children}
            </code>
          );
        },
      }}
    >
      {content}
    </ReactMarkdown>
  );
}

export default MarkdownMessage;
