/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:30:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDeviceGuidlineUnMonitor = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="36"
      height="37"
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M35.5 18.5002C35.5 28.1562 27.6654 35.9847 18 35.9847C8.33461 35.9847 0.5 28.1562 0.5 18.5002C0.5 8.84413 8.33461 1.01562 18 1.01562C27.6654 1.01562 35.5 8.84413 35.5 18.5002Z"
        stroke="#5A5A89"
        strokeOpacity="0.4"
      />
      <path
        d="M12.0112 23.9887H24.8445C25.317 23.9887 25.7 24.3717 25.7 24.8442C25.7 25.3168 25.317 25.6998 24.8445 25.6998H11.1556C10.6831 25.6998 10.3 25.3168 10.3 24.8442V11.1554C10.3 10.6828 10.6831 10.2998 11.1556 10.2998C11.6281 10.2998 12.0112 10.6828 12.0112 11.1554V23.9887Z"
        fill="#5A5A89"
      />
      <path
        d="M15.2019 20.2963C14.8787 20.641 14.3373 20.6585 13.9926 20.3353C13.6479 20.0121 13.6304 19.4707 13.9536 19.126L17.1619 15.7038C17.4744 15.3704 17.9938 15.3415 18.3414 15.6381L20.8736 17.7989L24.1729 13.6199C24.4657 13.249 25.0037 13.1857 25.3745 13.4785C25.7454 13.7713 25.8087 14.3093 25.5159 14.6802L21.6659 19.5568C21.3652 19.9377 20.8082 19.9925 20.439 19.6775L17.8518 17.4697L15.2019 20.2963Z"
        fill="#5A5A89"
      />
    </svg>
  );
};
