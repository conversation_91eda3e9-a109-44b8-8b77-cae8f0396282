/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-16 23:36:18
 * @LastEditTime: 2024-12-28 13:30:20
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { memo } from 'react';

interface IProps {
  className?: string;
  icon?: React.ReactNode;
  title?: string;
  tips?: string;
}

export const CardTips = memo((props: IProps) => {
  const { icon, tips, title } = props;
  return (
    <section className="flex flex-row bg-[#F5F7FA]/70 w-[290px] h-[94px] rounded-[16px] px-[20px] py-[16px] gap-[20px]">
      <div className="flex flex-col justify-center items-center">{icon}</div>
      <div className="flex flex-col justify-center gap-[10px]">
        <p className="text-txt-main text-[16px] font-medium">{title}</p>
        <p className="text-txt-sub text-[12px] font-normal">{tips}</p>
      </div>
    </section>
  );
});
