import { loadBaiduMapApi } from '@/utils/loader.util';
import { useEffect, useState } from 'react';

declare global {
  interface Window {
    BMapGL: any;
  }
}

export default function Map() {
  const [inited, setInited] = useState(false);

  useEffect(() => {
    loadBaiduMapApi().then(() => {
      setInited(true);
    });
  }, []);

  useEffect(() => {
    if (inited) {
      const map = new window.BMapGL.Map('map');
      // const point = new window.BMapGL.Point(114.129302,22.555665);
      const point = new window.BMapGL.Point(114.177882, 22.567814);
      map.centerAndZoom(point, 12);
      map.setTilt(23);
      map.enableScrollWheelZoom();
      const bd = new window.BMapGL.Boundary();
      if (process.env.NODE_ENV === 'development') {
        map.setMapStyleV2({
          styleId: 'f775090aa3e983de0ddfb12bda62dc8c',
        });
      } else {
        map.setMapStyleV2({
          styleId: 'fcf2e7317e830d312d95708964e4d6c0',
        });
      }
      map.setDisplayOptions({
        // poiText: false,
        poiIcon: false,
        // overlay: false,   // 是否显示覆盖物
        layer: false, // 是否显示叠加图层，地球模式暂不支持
        building: false, // 是否显示3D建筑物（仅支持WebGL方式渲染的地图）
        street: false, // 是否显示路网
      });
      bd.get('深圳市', (rs: any) => {
        const count = rs.boundaries.length; //行政区域的点有多少个
        for (let i = 0; i < count; i++) {
          const path = [];
          const str = rs.boundaries[i].replace(' ', '');
          const points = str.split(';');
          for (let j = 0; j < points.length; j++) {
            const lng = points[j].split(',')[0];
            const lat = points[j].split(',')[1];
            path.push(new window.BMapGL.Point(lng, lat));
          }
          // const prism = new window.BMapGL.Prism(path, 2000, {
          //   topFillColor: '#2661BF',
          //   topFillOpacity: 0.95,
          //   sideFillColor: '#2694DB',
          //   sideFillOpacity: 0.95,
          // });

          const mapmask = new BMapGL.MapMask(path, {
            isBuildingMask: true,
            isPoiMask: true,
            isMapMask: true,
            showRegion: 'inside',
            topFillColor: '#eee',
            topFillOpacity: 0.5,
            sideFillColor: '#eee',
            sideFillOpacity: 0.9,
          });

          map.addOverlay(mapmask);
          // map.addOverlay(prism);
        }
      });

      map.addEventListener('click', function (e) {
        console.log('点击位置经纬度：' + e.latlng.lng + ',' + e.latlng.lat);
      });
    }
  }, [inited]);

  if (!inited) {
    return null;
  }
  return (
    <div id="map" className=" h-full mx-[-30px]">
      Map
    </div>
  );
}
