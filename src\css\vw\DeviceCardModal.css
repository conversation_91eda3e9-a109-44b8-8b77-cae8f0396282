.DeviceCardModal {
  .container {
    width: 320px;
    height: 180px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-radius: 8px;
    padding: 20px 20px;
  }

  .container.control {
    height: 190px;
  }

  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #222;
    font-family: 'PingFang SC';
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    justify-content: space-between;
  }

  .title-left {
    gap: 8px;
    display: flex;
    align-items: center;
  }

  .title-close {
    color: var(--text-txt-main);
    width: 24px;
    height: 24px;
  }

  .content {
    display: flex;
    flex-direction: row;
    height: 100%;
    gap: 20px;

    .left {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;

      .left-img {
        border-radius: 8px;
        width: 100px;
        height: 100px;
      }

      .left-btn {
        width: 60px;
        height: 24px;
        font-size: 10px;
        font-weight: 500;
        border-width: 0.5px;
        border-radius: 4px;
      }
    }

    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      color: #222222;
      font-size: 12px;
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      gap: 4px;
    }
  }
}
