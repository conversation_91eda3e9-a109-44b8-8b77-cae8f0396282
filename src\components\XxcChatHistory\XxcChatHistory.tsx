import { IconAdd } from '@/assets/svg';
import { ChatItem, useChatStore } from '@/store';
import ai from './assets/ai.png';

import './XxcChatHistory.css';
import { forwardRef, useEffect, useRef, useState } from 'react';
import { ContextMenuData, useContextMenuStore } from '@/store/contextMenuStore';
import { useMemoizedFn } from 'ahooks';
import { ContextMenuType } from '../XxcContextmenu/common';
import XxcContextMenuWrap from '../XxcContextmenu/XxcContextMenuWrap';

const chatMenuList: ContextMenuData[] = [
  {
    name: '编辑',
    type: ContextMenuType.edit,
  },
  {
    name: '删除',
    type: ContextMenuType.delete,
  },
];

const CONTEXT_MENU_KEY = 'chat_history';
interface XxcChatHistoryItemProps {
  data: ChatItem;
  selected: boolean;
}
const XxcChatHistoryItem = (props: XxcChatHistoryItemProps) => {
  const { data, selected } = props;
  const { title, id } = data || {};

  return (
    <XxcContextMenuWrap data={data} menuKey={CONTEXT_MENU_KEY}>
      <div
        className={`XxcChatHistory-item  ${selected ? 'is-selected' : ''}`}
        onClick={() => {
          useChatStore.setState({ selectedId: id });
        }}
      >
        <div className="XxcChatHistory-itemText text-one-row">{title}</div>
        <div
          className="XxcChatHistory-itemMenu"
          data-contextmenu-key={CONTEXT_MENU_KEY}
          data-contextmenu-data={JSON.stringify({ id: data.id })}
        ></div>
      </div>
    </XxcContextMenuWrap>
  );
};

const XxcChatHistoryEdit = forwardRef((props: { data: ChatItem; updateChatMsg: any }, ref) => {
  const { data, updateChatMsg } = props;
  const { title } = data || {};

  const onKeydown = useMemoizedFn((e: any) => {
    if (e.key === 'Enter') {
      updateChatMsg({
        ...data,
        title: e.currentTarget?.value || '',
      } as ChatItem);
    }
  });

  return (
    <input
      type="text"
      className="XxcChatHistory-input"
      defaultValue={title}
      onKeyDown={onKeydown}
      autoFocus
      ref={ref as any}
    />
  );
});

export default function XxcChatHistory() {
  const { chatList, selectedId } = useChatStore();

  const hideContextMenu = useContextMenuStore((state) => state.hideContextMenu);
  const registerContextMenu = useContextMenuStore((state) => state.register);
  const [activeContext, setActiveContext] = useState<ChatItem | null>(null);
  const updateChatMsg = useChatStore((state) => state.updateChatMsg);
  const deleteChatMsg = useChatStore((state) => state.deleteChatMsg);
  const setMessageList = useChatStore((state) => state.setMessageList);
  const setSelectedId = useChatStore((state) => state.setSelectedId);
  const addChatMsg = useChatStore((state) => state.addChatMsg);
  const [editMode, setEditMode] = useState(false);
  const editRef = useRef<HTMLInputElement>(null);

  const onContextMenuClick = useMemoizedFn((data: any, targetData: any) => {
    if (data.type === 'edit') {
      setActiveContext(targetData);
      setEditMode(true);
    } else if (data.type === 'delete' && targetData?.id) {
      deleteChatMsg(targetData?.id);
    }
  });

  const onToggleContextMenu = useMemoizedFn((visible: boolean, data: any) => {
    if (visible) {
      setActiveContext(data);
    } else {
      // setActiveContext(null);
    }
  });

  useEffect(() => {
    registerContextMenu(CONTEXT_MENU_KEY, chatMenuList, {
      onContextMenuClick,
      onToggleContextMenu,
    });
  }, []);

  const updateChatMsgItem = useMemoizedFn((item: ChatItem) => {
    updateChatMsg(item);
    hideContextMenu();
    setActiveContext(null);
    setEditMode(false);
  });

  const hide = useMemoizedFn((e: any) => {
    if (editRef.current && editRef.current.contains(e.target)) {
      return;
    }
    const value = editRef.current?.value;
    if (value) {
      updateChatMsgItem({
        ...activeContext,
        title: value,
      } as ChatItem);
    }
    setEditMode(false);
  });

  useEffect(() => {
    document.body.addEventListener('click', hide);
    return () => {
      document.body.removeEventListener('click', hide);
    };
  }, []);

  const openNewChat = useMemoizedFn(() => {
    setMessageList([]);
    setSelectedId(0);
    addChatMsg({
      id: Date.now(),
      title: '新对话',
      content: '',
    });
    console.log('openNewChat');
  });

  return (
    <>
      <div className="XxcChatHistory-new" onClick={openNewChat}>
        <img src={ai} className="XxcChatHistory-newIcon" />
        新对话
        <IconAdd className="XxcChatHistory-newPlus" />
      </div>
      <div className="XxcChatHistory-list">
        {chatList.map((item, index) => {
          return activeContext?.id === item.id && editMode ? (
            <XxcChatHistoryEdit
              data={item}
              updateChatMsg={updateChatMsgItem}
              ref={editRef}
              key={'edit_' + index}
            />
          ) : (
            <XxcChatHistoryItem data={item} key={index} selected={item.id === selectedId} />
          );
        })}
      </div>
    </>
  );
}
