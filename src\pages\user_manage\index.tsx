import { userApi } from '@/api/user';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { IColumns } from '@/components/XcTable/interface/search.interface';
import { DATE_FMT } from '@/utils/time.util';
import {
  ProFormItem,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { request, styled, useRequest } from '@umijs/max';
import { Button, ConfigProvider, message, Space, Switch, TreeSelect, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { UserAddOrEdit } from './components/AddOrEdit';
import { roleApi } from '@/api/role';
import { orgApi } from '@/api/org';
import { isBlank } from '@/utils/string.util';
import { IconAdd, IconBatch, IconEdit, IconRole, IconClose } from '@/assets/svg';
import { sleep } from '@/utils/common.util';
import { ERROR_CODE } from '@/constants/request';
import PermissionWrap from '@/components/PermissionWrap';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import { XcAntdTree } from '@/components/XcAntdTree';
import _ from 'lodash';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';

const { Text } = Typography;
const StyledTreeSelect = styled(TreeSelect)`
  .ant-select-selection-wrap {
    height: 100%;
  }
  .ant-select-selection-item {
    display: inline-block;
    white-space: nowrap;
  }
  .ant-select-selection-overflow {
    flex-wrap: nowrap;
    overflow-x: auto;
    scrollbar-width: none;
  }
`;

enum MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  status = 4, // 更改状态
  batchStatus = 5,
  pwd = 6,
  role = 7,
  org = 8,
}

export default () => {
  const tableRef = useRef<ITableRef>(null);
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [allRoleList, setAllRoleList] = useState<Array<any>>([]);
  const [allOrgList, setAllOrgList] = useState<Array<any>>([]);

  // 获取角色列表
  useRequest(
    (data?: any, headers?: any) => ({
      url: roleApi.getList,
      params: { tenant_id: 1, ...data, page_size: 100 },
      headers,
    }),
    {
      onSuccess: (res) => {
        const list = res?.list || [];
        setAllRoleList(list);
      },
    },
  );
  useRequest(
    (data?: any, headers?: any) => ({
      url: orgApi.getList,
      params: { tenant_id: 1, ...data },
      headers,
    }),
    {
      onSuccess: (res) => {
        const list = res?.list || [];
        setAllOrgList(list);
      },
    },
  );

  /**
   * 用户列表请求接口
   */
  const userListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: userApi.getList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  // 刷新数据
  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };

  /**
   * 更新用户请求接口
   */
  const userEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: userApi.putUser(id),
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, org_ids, role_ids, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.status === '0') {
      others.status = 99;
    }
    if (Array.isArray(org_ids)) {
      payload.org_ids = org_ids.filter((id: number) => +id > 0);
    }
    if (+role_ids > 0) {
      payload.role_ids = role_ids;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await userListRequest.run(queryPayload);

    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: result.data.list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };
  const showModal = async (item: any, type: MODAL_TYPE) => {
    setCurrentItem(item);
    setModalType(type);
  };
  const closeModal = () => {
    setModalType(MODAL_TYPE.none);
  };
  const toggleModalVisible = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };
  // 用户状态 --start
  const onStatusChange = async (data: any) => {
    const status = data.status === 1 ? -2 : 1;
    return userEditRequest
      .run(data.id, {
        ...data,
        status,
      })
      .then((res) => {
        if (res.code === 200) {
          const text = status === 1 ? '用户已启用。' : '用户已停用。';
          message.info(res.message || text);
          closeModal();
          queryTableData();
          return true;
        } else {
          message.error(res.message || '修改失败，请重试。');
          return false;
        }
      });
  };
  const showStatusModal = (item: any) => {
    setCurrentItem(item);
    // if (item.status === 1) {
    showModal(item, MODAL_TYPE.status);
    // } else {
    //   onStatusChange(item);
    // }
  };
  const onBatchStatusChange = async (values: any) => {
    let ids = tableRef.current?.checkedListRef.current.map((item: any) => {
      return item.id;
    });
    ids = _.uniq(ids);
    // const status = values.status;
    return request(userApi.postUpdateStatus, {
      method: 'POST',
      data: {
        tenant_id: 1,
        user_ids: ids,
        type: 'status',
        ...values,
      },
    }).then(async (res) => {
      if (res.code === 200) {
        message.info(res.message || '批量操作成功，所有用户已更新。');
        queryTableData();
        return true;
      } else {
        message.error(res.message || '批量操作失败，部分用户未更新。');
        return true;
      }
    });
  };
  // 用户状态 --end

  // 修改--start
  const showEditModal = (item: any) => {
    request(userApi.getUser(item.id), {
      params: { tenant_id: 1 },
    }).then(async (res) => {
      setCurrentItem(res.data);
      await sleep(100);
      showModal(res.data, MODAL_TYPE.edit);
    });
  };

  // 修改--end

  const onRoleChange = async (values: any) => {
    let ids = tableRef.current?.checkedListRef.current.map((item: any) => {
      return item.id;
    });
    ids = _.uniq(ids);
    const result = await request(userApi.postUsersRoles, {
      method: 'POST',
      data: {
        tenant_id: 1,
        user_ids: ids,
        ...values,
      },
    }).then(async (res) => {
      if (res.code === 200) {
        message.info(res.message || '角色分配成功。');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.info(res.message || '角色分配失败。');
        return false;
      }
    });
    return result;
  };
  const orgFormRef = useRef<any>();

  const onOrgCheck = (checkedKeys: any, e: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys];
    orgFormRef.current?.setFieldValue('org_ids', keys);
  };
  const onOrgChange = async (values: any) => {
    let ids = tableRef.current?.checkedListRef.current.map((item: any) => {
      return item.id;
    });
    ids = _.uniq(ids);
    const result = await request(userApi.postUsersOrgs, {
      method: 'POST',
      data: {
        tenant_id: 1,
        user_ids: ids,
        ...values,
      },
    }).then(async (res) => {
      if (res.code === 200) {
        message.info(res.message || '批量操作成功，所有用户已更新。');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.info(res.message || '批量操作失败，部分用户未更新。');
        return false;
      }
    });
    return result;
  };
  // 修改密码--start
  const onPasswordChange = async (ids: number[]) => {
    return request(userApi.postResetPassword, {
      method: 'POST',
      data: {
        tenant_id: 1,
        user_ids: ids,
        type: 'pwd',
      },
    }).then(async (res) => {
      if (res.code === 200 || res.code === ERROR_CODE.NONE_UPDATE) {
        message.info(res.message || '密码已重置。');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.error(res.message || '密码重置失败。');
        return false;
      }
    });
  };
  // 修改密码--end
  const batchRender = () => {
    return (
      <div className="flex">
        <PermissionWrap accessCode={STATIC_PERMISSION_CODE.分配角色}>
          <Button
            type="link"
            className="text-txt-main  px-0 mr-[24px]"
            icon={<IconRole className="size-[20px] !align-middle" />}
            key={'分配角色'}
            onClick={() => {
              showModal({}, MODAL_TYPE.role);
            }}
          >
            分配角色
          </Button>
        </PermissionWrap>
        <PermissionWrap accessCode={STATIC_PERMISSION_CODE.修改状态}>
          <Button
            type="link"
            className="text-txt-main  px-0 mr-[24px]"
            icon={<IconEdit className="size-[20px] !align-middle" />}
            key={'修改状态'}
            onClick={() => {
              showModal({}, MODAL_TYPE.batchStatus);
            }}
          >
            修改状态
          </Button>
        </PermissionWrap>
        <PermissionWrap accessCode={STATIC_PERMISSION_CODE.批量分组}>
          <Button
            type="link"
            className="text-txt-main  px-0 mr-[24px]"
            icon={<IconBatch className="size-[15px] !align-middle" />}
            key={'批量分组'}
            onClick={() => {
              showModal({}, MODAL_TYPE.org);
            }}
          >
            批量分组
          </Button>
        </PermissionWrap>
      </div>
    );
  };
  const columns: IColumns[] = [
    {
      title: '全部部门',
      width: 120,
      key: 'org_ids',
      hideInTable: true,
      initialValue: [],
      valueType: 'drop',
      realtime: true,
      valueEnum: [{ label: '全部部门', value: 'all' }],
      render: (_, record, index: number, action: { onChange: any }) => {
        return (
          <StyledTreeSelect
            multiple
            onSelect={() => null}
            style={{ width: 240 }}
            onChange={(data: any) => {
              let value = data;
              if (data.indexOf('all') !== -1) {
                value = [];
              }
              action.onChange(value.join(','));
            }}
            maxCount={5}
            allowClear={{
              clearIcon: (
                <IconClose
                  className="size-[12px] items-center"
                  onClick={() => {
                    setTimeout(() => {
                      queryTableData();
                    }, 0);
                  }}
                />
              ),
            }}
            placeholder={<span className="text-txt-main">全部部门</span>}
            fieldNames={{
              // @ts-ignore
              title: 'name',
              key: 'id',
              label: 'name',
              value: 'id',
              children: 'children',
            }}
            treeData={[{ id: 'all', name: '全部部门', children: allOrgList }]}
            defaultValue={'all'}
            treeDefaultExpandAll
          />
        );
      },
    },
    {
      title: '全部状态',
      width: 120,
      key: 'status',
      hideInTable: true,
      initialValue: 99,
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        { label: '全部状态', value: 99 },
        { label: '启用', value: '1' },
        { label: '停用', value: '-2' },
      ],
    },
    {
      title: '所属角色',
      width: 120,
      key: 'role_ids',
      hideInTable: true,
      initialValue: 'all',
      valueType: 'drop',
      realtime: true,
      valueEnum: [{ id: 'all', name: '全部角色' }, ...allRoleList],
    },
    {
      title: '创建时间',
      key: 'range_time',
      realtime: true,
      // initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '用户名称',
      width: 320,
      key: 'name',
      hideInTable: true,
      placeholder: '搜索用户名称',
    },
    {
      title: '用户编号',
      dataIndex: 'code',
      key: 'code',
      link: true,
      render: (_, item: any) => {
        return (
          <Text
            className="text-txt-main font-normal hover:text-txt-blue  cursor-pointer"
            ellipsis={{ tooltip: item.code }}
            onClick={() => {
              if (checkFunPermission(STATIC_PERMISSION_CODE.更新用户)) {
                showEditModal(item);
              }
            }}
          >
            {item.code}
          </Text>
        );
      },
    },
    {
      title: '用户名称',
      dataIndex: 'name',
      key: 'name',
      render: (_, item: { name: string }) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: item.name }}>
            {item.name}
          </Text>
        );
      },
    },
    {
      title: '所属部门',
      dataIndex: 'orgs',
      key: 'orgs',
      render: (_, item: { orgs: string }) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: item?.orgs }}>
            {item.orgs || '-'}
          </Text>
        );
      },
    },
    {
      title: '用户角色',
      dataIndex: 'roles',
      key: 'roles',
      render: (_, item: { roles: number }) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: item?.roles }}>
            {item.roles || '-'}
          </Text>
        );
      },
    },
    {
      title: '用户状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, item: { status: number }) => {
        return (
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.修改状态} useDisabled>
            <Switch onClick={() => showStatusModal(item)} checked={item.status === 1} />
          </PermissionWrap>
        );
      },
    },
    {
      title: '手机号码',
      dataIndex: 'phone',
      key: 'phone',
      render: (_, item: any) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: item?.phone }}>
            {item?.phone}
          </Text>
        );
      },
    },
    {
      title: '用户邮箱',
      dataIndex: 'email',
      key: 'email',
      render: (_, item: any) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.email }}>
            {isBlank(item.email) ? '-' : item.email}
          </Text>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (_, item: { created_at: any }) => {
        const txt = dayjs(item.created_at).format('YYYY.MM.DD');
        // return <span className="text-txt-main">{dayjs(item.created_at).format('YYYY.MM.DD')}</span>;
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: txt }}>
            {txt}
          </Text>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 136,
      className: 'flex items-center justify-center',
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px]'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新用户}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showEditModal(item)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.重置密码}>
              <Button
                type="link"
                className="text-txt-main px-[0px] hover:text-txt-blue"
                onClick={() => showModal(item, MODAL_TYPE.pwd)}
              >
                重置密码
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex">
      <XcTableNew
        ref={tableRef}
        loading={userListRequest.loading}
        columns={columns}
        request={requestTable}
        extend={null}
        batchRender={batchRender}
        searchTitle="用户管理"
        operator={
          <>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建用户}>
              <Button
                type="primary"
                className="h-[32px] rounded-[4px] bg-btn-blue text-txt-white text-[14px]"
                icon={<IconAdd className="size-[16px]" />}
                onClick={() => {
                  showModal({}, MODAL_TYPE.add);
                }}
              >
                <span className={'!self-center'}>新增用户</span>
              </Button>
            </PermissionWrap>
          </>
        }
      />
      {modalType === MODAL_TYPE.edit || modalType === MODAL_TYPE.add ? (
        <UserAddOrEdit
          modalType={modalType}
          onClose={closeModal}
          visible={true}
          onChange={queryTableData}
          currentItem={currentItem}
          allOrgList={allOrgList}
          allRoleList={allRoleList}
          showResetPassword={() => {
            showModal(currentItem, MODAL_TYPE.pwd);
          }}
        ></UserAddOrEdit>
      ) : null}
      <XcModalForm
        title="确认"
        key={'重置密码'}
        width={500}
        open={modalType === MODAL_TYPE.pwd}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={() => onPasswordChange([currentItem.id])}
      >
        <div className="my-[40px]">重置密码后将使用系统密码，确定重置？</div>
      </XcModalForm>
      <XcModalForm
        key="确认"
        title={currentItem?.status === 1 ? '禁用' : '启用'}
        width={500}
        open={modalType === MODAL_TYPE.status}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={() => onStatusChange(currentItem)}
      >
        {currentItem?.status === 1 ? (
          <div className="my-[40px]">停用后，该用户将无法登录系统，确定停用？</div>
        ) : (
          <div className="my-[40px]">启用后，该用户将继续登录系统，确定启用？</div>
        )}
      </XcModalForm>

      <XcModalForm
        title="分配角色"
        key={'分配角色'}
        width={500}
        open={modalType === MODAL_TYPE.role}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={onRoleChange}
      >
        <ProFormTextArea
          name="name"
          label={'用户名称'}
          disabled
          fieldProps={{
            value: (tableRef?.current?.checkedListRef.current || []).map((item: any) => item.name),
            className: 'xc-scrollbar-y',
            autoSize: {
              minRows: 1,
              maxRows: 6,
            },
          }}
        />
        <ProFormTreeSelect
          label="用户角色"
          name="role_ids"
          rules={[
            {
              required: true,
              message: '请选择用户角色',
            },
          ]}
          fieldProps={{
            fieldNames: {
              label: 'name',
              value: 'id',
              children: 'children',
            },
            multiple: true,
            treeData: allRoleList,
            treeCheckable: true,
            showCheckedStrategy: TreeSelect.SHOW_PARENT,
            placeholder: '请选择用户角色',
          }}
        />
      </XcModalForm>
      <XcModalForm
        title="批量分组"
        width={500}
        ref={orgFormRef}
        key={'批量分组'}
        open={modalType === MODAL_TYPE.org}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={onOrgChange}
      >
        <ProFormTextArea
          name="name"
          label={'用户名称'}
          disabled
          fieldProps={{
            value: (tableRef?.current?.checkedListRef.current || []).map((item: any) => item.name),
            className: 'xc-scrollbar-y',
            autoSize: {
              minRows: 1,
              maxRows: 6,
            },
          }}
        />
        <ProFormText hidden name="org_ids"></ProFormText>
        <ProFormItem label="所属部门">
          <div className="xc-scrollbar-y xc-from-tree-box h-[430px] ">
            <XcAntdTree
              checkable
              onCheck={onOrgCheck}
              treeData={allOrgList}
              fieldNames={{
                title: 'name',
                key: 'id',
                children: 'children',
              }}
            />
          </div>
        </ProFormItem>
      </XcModalForm>
      <XcModalForm
        title="修改状态"
        width={500}
        ref={orgFormRef}
        key={'修改状态'}
        open={modalType === MODAL_TYPE.batchStatus}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={onBatchStatusChange}
      >
        <ProFormTextArea
          name="name"
          label={'用户名称'}
          disabled
          fieldProps={{
            value: (tableRef?.current?.checkedListRef.current || []).map((item: any) => item.name),
            className: 'xc-scrollbar-y',
            autoSize: {
              minRows: 1,
              maxRows: 6,
            },
          }}
        />
        <ProFormSelect
          options={[
            {
              value: 1,
              label: '启用',
            },
            {
              value: -2,
              label: '禁用',
            },
          ]}
          rules={[
            {
              required: true,
              message: '用户状态未选择',
            },
          ]}
          name="status"
          label={'用户状态'}
        />
      </XcModalForm>
    </div>
  );
};
