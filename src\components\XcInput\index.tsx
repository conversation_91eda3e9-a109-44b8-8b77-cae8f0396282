/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-30 00:19:50
 * @LastEditTime: 2025-02-02 22:54:28
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { styled } from '@umijs/max';
import { ConfigProvider, Input, InputProps } from 'antd';

const StyledInput = styled(Input)`
  padding: 0px;
  > .ant-input-prefix {
    margin-inline-end: 0;
  }
  > .ant-input-suffix {
    margin-inline-end: 16px;
    > .ant-input-clear-icon {
      font-size: 14px;
    }
  }
  input {
    color: #949baa;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    &::placeholder {
      color: #949baa;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
`;

export const XcInput = (props: InputProps) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Input: {
            colorBgContainer: '#F4F4F6',
            colorBorder: '#F4F4F6',
            activeBorderColor: '#E7E9ED',
            hoverBorderColor: '#E7E9ED',
          },
        },
      }}
    >
      <StyledInput {...props} />
    </ConfigProvider>
  );
};
