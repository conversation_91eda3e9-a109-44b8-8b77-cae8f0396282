/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-21 21:46:03
 * @LastEditTime: 2025-01-24 17:58:53
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
// 随机经纬度，目标经纬度基准参考，随机附近100 米的经纬度
export function getRandomLatLng(baseLatLng: any, radius: number) {
  const lat = baseLatLng.lat;
  const lng = baseLatLng.lng;
  const rad = radius / 1000 / 6371;
  const u = Math.random();
  const v = Math.random();
  const w = rad * Math.sqrt(u);
  const t = 2 * Math.PI * v;
  const x = w * Math.cos(t);
  const y = w * Math.sin(t);
  const newLat = lat + (x * 180) / Math.PI;
  const newLng = lng + (y * 180) / Math.PI;

  return { lat: newLat, lng: newLng };
}
