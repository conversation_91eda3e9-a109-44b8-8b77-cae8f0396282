/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:30:41
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDeviceGuidlineMonitor = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="36"
      height="37"
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M35.5 18.5002C35.5 28.1562 27.6654 35.9847 18 35.9847C8.33461 35.9847 0.5 28.1562 0.5 18.5002C0.5 8.84413 8.33461 1.01562 18 1.01562C27.6654 1.01562 35.5 8.84413 35.5 18.5002Z"
        stroke="#5A5A89"
        strokeOpacity="0.4"
      />
      <g clipPath="url(#clip0_6105_27232)">
        <circle cx="18" cy="18" r="8.25" stroke="#5A5A89" strokeWidth="1.5" />
        <path
          d="M10.2002 20.4498H13.5857L14.7926 17.15C14.9147 16.8163 15.3788 16.7956 15.5302 17.1171L17.4916 21.2833C17.6462 21.6116 18.1228 21.5811 18.2342 21.2358L20.2471 14.9983C20.367 14.627 20.8932 14.6294 21.0096 15.0018L22.7122 20.4498L25.2002 20.4498"
          stroke="#5A5A89"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6105_27232">
          <rect width="22" height="22" fill="white" transform="translate(7 7)" />
        </clipPath>
      </defs>
    </svg>
  );
};
