.XxcHeader {
  display: flex;
  height: 66px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  width: 100vw;
  /* background: linear-gradient(to right, #E9F2FB 0%, #dbe2fb 80%, #E9F2FB 100%); */
}

.XxcHeader-logo {
  margin-left: 14px;

}

.XxcHeader-menu {
  background: rgba(255, 255, 255, 0.4);
  box-sizing: border-box;
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 8px;
  height: 44px;
  padding: 0 2px;
}

.XxcHeader-menuDivider {
  width: 1px;
  height: 20px;
  border-radius: 0.5px;
  background: #DEE9F3;
  margin: 0 2px;
}

.XxcHeader-user,
.XxcHeader-mail {
  display: flex;
  width: 136px;
  height: 40px;
  border-radius: 4px;
  align-items: center;
  padding: 6px 24px;
  cursor: pointer;

  &:hover {
    background-color: #fff;
  }
}

.XxcHeader-menuIcon {
  width: 28px;
  height: 28px;
  margin-right: 4px;
}

.XxcHeader-user .XxcHeader-menuIcon {
  border-radius: 14px;
  border: 1px solid #D9EAFF;
}
