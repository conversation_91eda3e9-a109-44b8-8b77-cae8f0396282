/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-23 11:12:46
 * @LastEditTime: 2025-01-14 22:28:36
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export const IconChevronLeft = (props: any) => {
  const { className, onClick } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      onClick={onClick}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20 24L12 16L20 8"
        stroke="#9292C1"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
