import IconEdit from './assets/Edit.svg';
import IconDelete from './assets/Trash.svg';
import IconImage from './assets/Image.svg';

export enum ContextMenuType {
  edit = 'edit',
  delete = 'delete',
  image = 'image',
}

export const CONTEXT_MENU_ICONS: Record<ContextMenuType, string> = {
  [ContextMenuType.edit]: IconEdit,
  [ContextMenuType.delete]: IconDelete,
  [ContextMenuType.image]: IconImage,
};

export const getMenuListKey = (key: string) => {
  return 'contextMenuList_' + key;
};
