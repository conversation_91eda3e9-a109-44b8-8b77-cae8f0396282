import { useMemo } from 'react';
import './ImageMessage.css';
export default function ImageMessage(props: any) {
  const { list } = props;
  const renderList = useMemo(() => {
    return Array.isArray(list) ? list : [];
  }, [list]);
  return (
    <div className="ImageMessage">
      <div className="ImageMessage-list">
        {renderList.map((item: any, index: number) => {
          return (
            <div className="ImageMessage-item" key={index}>
              <img src={item.url} alt="" />
            </div>
          );
        })}
      </div>
    </div>
  );
}
