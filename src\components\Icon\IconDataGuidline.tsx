/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:29:49
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDataGuidline = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#3455FF" />
      <g clipPath="url(#clip0_6105_27257)">
        <rect width="12" height="12" transform="translate(6 6)" fill="#3455FF" />
        <path
          d="M16.5 16.5H9.1C8.53995 16.5 8.25992 16.5 8.04601 16.391C7.85785 16.2951 7.70487 16.1422 7.60899 15.954C7.5 15.7401 7.5 15.4601 7.5 14.9V7.5M9.5 13.25V14.75M11.75 11.75V14.75M14 10.25V14.75M16.25 8.75V14.75"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_6105_27257">
          <rect width="12" height="12" fill="white" transform="translate(6 6)" />
        </clipPath>
      </defs>
    </svg>
  );
};
