/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-11 08:56:28
 * @LastEditTime: 2025-01-14 22:31:11
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDeviceOnline = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="17"
      height="7"
      viewBox="0 0 17 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.74512 3.5H13.7451"
        stroke="url(#paint0_linear_90_782)"
        strokeWidth="6"
        strokeLinecap="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_90_782"
          x1="2.14512"
          y1="4"
          x2="16.5451"
          y2="4"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#1792FE" />
          <stop offset="1" stopColor="#7D40FF" />
        </linearGradient>
      </defs>
    </svg>
  );
};
