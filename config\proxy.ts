/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 16:15:02
 * @LastEditTime: 2025-02-11 15:45:57
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  // 如果需要自定义本地开发服务器  请取消注释按需调整
  dev: {
    '/api/': {
      // target: 'https://community-admin.hlpays.cn',
      // target: 'http://127.0.0.1:4523/m1/5673246-5354003-default', // mock 接口
      // target: 'http://************:30004', // test接口
      // target: 'https://vsp.aixxc.com', // test接口
      target: 'https://dev.aiaas.com.cn', // test接口
      // target: 'http://**************:80', // 新接口
      // target: 'http://************:8001', // 旧接口
      // target: 'https://test.mitoch.cn/gw',
      changeOrigin: true,
      secure: false,
      pathRewrite: { '^': '' },
    },
  },

  /**
   * @name 详细的代理配置
   * @doc https://github.com/chimurai/http-proxy-middleware
   */
  test: {
    // localhost:8000/api/** -> https://preview.pro.ant.design/api/**
    '/api/': {
      target: 'https://proapi.azurewebsites.net',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
