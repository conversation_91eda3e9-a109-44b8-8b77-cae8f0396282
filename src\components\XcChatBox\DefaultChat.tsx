import { memo } from 'react';
import { CardTips } from './CardTips';
import { IconBarChart } from './icons/IconBarChart';
import { IconBellRinging } from './icons/IconBellRinging';
import { IconLightbulb } from './icons/IconLightbulb';
import { IconMultipleModal } from './icons/IconMultipleModal';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 17:55:23
 * @LastEditTime: 2025-01-02 16:11:42
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const DefaultChat = memo(() => {
  return (
    <div className="w-[800px] flex flex-col items-center">
      <section className="flex flex-col items-center text-[28px] text-[#ffffff] text-center md:mt-[0.7rem] xl:mt-[1.4rem] md:mb-[.4rem] xl:mb-[0.8rem]">
        <div className="mt-[16px] text-[26px] font-medium text-txt-main">
          你好，我是星小尘，你的超级助手
        </div>
        <div className="rounded-[16px] py-[10px] px-[20px] text-[14px] text-[#C8C8C8] mt-[10px] font-normal">
          上传资料，体验精准快捷的监测服务
        </div>
      </section>

      <section className="flex justify-center gap-[20px] mb-[.2rem]">
        <CardTips
          icon={<IconMultipleModal className="size-[24px]" />}
          title="多模态输入"
          // tips="实现图片、音频、文件上传识别病媒种类"
          tips="实现图片上传识别病媒种类"
        />
        <CardTips
          icon={<IconBarChart className="size-[24px]" />}
          title="数据分析"
          tips="对上传的数据进行分析，提供相关分布、密度等信息"
        />
      </section>

      <section className="flex justify-center gap-[20px]">
        <CardTips
          icon={<IconLightbulb className="size-[24px]" />}
          title="智能问答"
          tips="通过问题咨询，调用知识库回答病媒生物相关情况"
        />
        <CardTips
          icon={<IconBellRinging className="size-[24px]" />}
          title="智能预警"
          tips="通过上传相关检测数据，预测可能爆发病害的目的地"
        />
      </section>
    </div>
  );
});
