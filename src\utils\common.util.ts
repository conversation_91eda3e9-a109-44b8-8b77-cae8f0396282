import { request } from '@umijs/max';
import { ALL_ROUTES } from '../../config/routes';
import { checkMenuPermission } from './permission';
import { getTreeNodeWithProperty } from './tree.util';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-30 18:57:10
 * @LastEditTime: 2024-12-30 19:32:22
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const sleep = (ms: number) => {
  return new Promise((resolve) => {
    setTimeout(resolve, ms);
  });
};

export const compareArrayIdList = (newValue: number[], preValue: number[]) => {
  if ((!newValue && !preValue) || (newValue?.length === 0 && !preValue)) {
    return false;
  }
  const result = newValue?.sort?.().join(',') !== preValue?.sort?.().join(',');
  return result;
};

export function getFirstRoutePath() {
  const ignoreRoutes = ['/user/login', '/403', '/404', '/500', '/user/resetPwd'];
  const list = getTreeNodeWithProperty<any>(ALL_ROUTES, 'component', { children: 'routes' });
  const firstRoute = list.find((item) => {
    const isIgnore = ignoreRoutes.some((route) => {
      const index = item.path.indexOf(route);
      return index !== -1;
    });
    if (isIgnore) {
      return false;
    }
    if (item.permission) {
      return item.permission.some((code: string) => {
        const res = checkMenuPermission(code);
        return res;
      });
    } else {
      return true;
    }
  });
  return firstRoute?.path || '/welcome';
}

export async function downloadWithToken(url: string, filename: string = 'data.html') {
  try {
    const response = await request(url);
    if (response.code !== 200) {
      return Promise.reject(response.message);
    }
    const { download_url, file_name } = response.data || {};
    const a = document.createElement('a');

    // const blob = await fetch(download_url).then((res) => res.blob())
    // const downloadUrl = window.URL.createObjectURL(blob);
    a.href = download_url;
    a.target = '_blank';
    a.download = file_name || filename;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      // window.URL.revokeObjectURL(downloadUrl);
    }, 0);
    return true;
  } catch (error) {
    console.log('下载失败:', error);
    return Promise.reject(error);
  }
}

export function closest(element: any, selector: string) {
  // 从当前元素开始向上遍历
  let current = element;

  while (current) {
    // 检查当前元素是否匹配选择器
    if (current.matches(selector)) {
      return current;
    }

    // 移动到父元素
    current = current.parentElement;

    // 如果到达文档根节点仍未找到则返回 null
    if (current === document.documentElement && !current.matches(selector)) {
      break;
    }
  }

  return null;
}

export const EmptyFunction = () => {};

export const EmptyObject = {};

export const EmptyArray = [];
