import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { request, useRequest } from '@umijs/max';
import { Button, message, Typography } from 'antd';
import React, { useRef, useState } from 'react';
import { orgApi } from '@/api/org';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import { IconAdd } from '@/assets/svg';
import { GLOBAL_MODAL_TYPE } from '@/constants/common';
import { OrgAddOrEdit } from './AddOrEdit';
import { XcTableNew } from '@/components/XcTable/XcTableNew';

const { Text } = Typography;
export default () => {
  const [modalType, setModalType] = useState<GLOBAL_MODAL_TYPE>(GLOBAL_MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const tableRef = useRef<any>(null);

  /**
   * 部门列表请求接口
   */
  const orgListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: orgApi.getList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { current, pageSize, ...others } = params;

    const queryPayload = {
      page: current,
      page_size: pageSize,
      ...others,
      tenant_id: 1,
    };
    const result = await orgListRequest.run(queryPayload);

    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: result.data.list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };
  // 刷新数据
  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };
  const closeModal = () => {
    setModalType(GLOBAL_MODAL_TYPE.none);
  };
  const onOpenModal = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };

  const showModal = (item: any, type: GLOBAL_MODAL_TYPE) => {
    if (item.parent_id === 0) {
      item.parent_id = null;
    }
    setCurrentItem(item);
    setModalType(type);
  };

  const onDeleteOrg = async () => {
    return request(orgApi.deleteOrg(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  const onUnbindOrg = async () => {
    return request(orgApi.deleteBindOrg(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('解绑成功');
        closeModal();
        queryTableData();
        return true;
      } else {
        message.info(res.message || '解绑失败');
        return false;
      }
    });
  };

  const columns = [
    {
      title: '用户名称',
      width: 320,
      key: 'name',
      hideInTable: true,
      placeholder: '搜索用户名称',
    },
    {
      title: '部门名称',
      dataIndex: 'id',
      key: 'id',
      render: (_, item: { name: string }) => {
        return (
          <Text className="text-txt-main bg-white" ellipsis={{ tooltip: item.name }}>
            {item.name}
          </Text>
        );
      },
    },
    {
      title: '部门编号',
      dataIndex: 'code',
      key: 'code',
      render: (_, item: any) => {
        return <div className="text-txt-main">{item.code}</div>;
      },
    },
    {
      title: '部门用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (_, item: any) => {
        return <div className="text-txt-main">{item.user_count}</div>;
      },
    },
    {
      title: '操作',
      width: 242,
      align: 'center',
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px] w-full'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建部门}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal({ parent_id: item.id }, GLOBAL_MODAL_TYPE.add)}
              >
                新增子部门
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.解绑部门用户}>
              <Button
                type="link"
                className={`text-txt-main px-[0px] ${item?.children?.length > 0 || item.user_count <= 0 ? 'hidden' : ''}`}
                onClick={() => showModal(item, GLOBAL_MODAL_TYPE.unbind)}
              >
                解绑
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新部门}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, GLOBAL_MODAL_TYPE.edit)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除部门}>
              <Button
                type="link"
                className={`text-txt-main px-[0px] ${item?.children?.length > 0 ? 'hidden' : ''}`}
                onClick={() => {
                  if (item.user_count > 0) {
                    showModal(item, GLOBAL_MODAL_TYPE.deleteRemind);
                  } else {
                    showModal(item, GLOBAL_MODAL_TYPE.delete);
                  }
                }}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];
  return (
    <div className="flex-1 flex ">
      <XcTableNew
        ref={tableRef}
        loading={orgListRequest.loading}
        columns={columns}
        request={requestTable}
        extend={null}
        rowSelection={null}
        batchRender={null}
        searchTitle="部门管理"
        operator={
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建部门}>
            <Button
              type="primary"
              className={'h-[32px] rounded-[4px] bg-btn-blue text-txt-white text-[14px]'}
              icon={<IconAdd className="!align-middle" />}
              onClick={() => {
                showModal({}, GLOBAL_MODAL_TYPE.add);
              }}
            >
              新增部门
            </Button>
          </PermissionWrap>
        }
      />

      {modalType === GLOBAL_MODAL_TYPE.add || modalType === GLOBAL_MODAL_TYPE.edit ? (
        <OrgAddOrEdit
          modalType={modalType}
          currentItem={currentItem}
          onChange={requestTable}
          visible={true}
          onClose={closeModal}
        />
      ) : null}
      <XcModalForm
        title="删除"
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.deleteRemind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={() => {
          setTimeout(() => {
            showModal(currentItem, GLOBAL_MODAL_TYPE.unbind);
          }, 200);
          return Promise.resolve(true);
        }}
      >
        <div className="my-[40px]">请先解绑“{currentItem.name}”部门下的客户，再删除。</div>
      </XcModalForm>
      <XcModalForm
        title="确认"
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onDeleteOrg}
      >
        <div className="my-[40px]">
          删除“{currentItem.name}”部门后，该部门下的用户将无法查看相关数据， 请谨慎操作！确定删除？
        </div>
      </XcModalForm>
      <XcModalForm
        title="解绑"
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.unbind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onUnbindOrg}
      >
        <div className="my-[40px]">
          解绑“{currentItem.name}”部门后，该部门下的用户（{currentItem.user_count}
          人）将无法查看相关数据，请谨慎操作！确定解绑？
        </div>
      </XcModalForm>
    </div>
  );
};
