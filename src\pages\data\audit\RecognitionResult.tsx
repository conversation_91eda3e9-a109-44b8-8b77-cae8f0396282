import React, { forwardRef, useEffect, useImperative<PERSON><PERSON><PERSON>, useMemo, useState } from 'react';
import { Select, Form, Input, message } from 'antd';
import { GLOBAL_MODAL_TYPE, SEX_OPTIONS } from '@/constants/common';
import { isEmpty, omit } from 'lodash';
import { useForm } from 'antd/es/form/Form';
import { FormInstance } from 'antd/lib';
import { useMemoizedFn } from 'ahooks';
import { NAME_REG_EXP } from '@/constants/regex';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import { AUDIT_VERIFY_DATA_STATUS, verifyFailImg, verifyWaitImg } from './constant';
import { IconCloseCircle, IconPlus, IconRecognition } from '@/assets/svg';
import { MOS_NAME_LIST_ZH } from '@/constants/mosquitor';

interface IAuditData {
  order: number;
  source_id: number;
  type: string;
  sex: number; //
  code: string;
  data_type: number; // 1. 人工审核 2.AI验证 3 其他
  custom_type: '';
}
enum VIEW_MODE {
  verify = 'verify',
  plainText = 'plainText',
}
enum MOS_TYPE_EXT {
  其他 = 'other',
  无蚊虫 = 'none',
}
enum fieldType {
  allList = 'allList',
}
interface MosquitoSelectProps {
  field: any;
  mosList: { code: string; name: string }[];
  form: FormInstance;
  updateValue: (
    idx: number,
    key: keyof IAuditData,
    value: string | number,
    listName: fieldType,
  ) => void;
  disabled?: boolean;
  listName: fieldType;
}

const MosquitoSelect: React.FC<MosquitoSelectProps> = ({
  field,
  mosList,
  form,
  updateValue,
  disabled = false,
  listName,
}) => {
  const [showCustomType, setShowCustomType] = useState(false);

  useEffect(() => {
    const code = form.getFieldValue([listName, field.name, 'code']);
    setShowCustomType(code === MOS_TYPE_EXT.其他);
  }, [field, form, listName]);

  return (
    <React.Fragment>
      <Form.Item
        className="flex-1 h-[38px] mb-0"
        name={[field.name, 'code']}
        rules={[
          {
            required: true,
            message: '请选择蚊种',
          },
        ]}
      >
        <Select
          className="h-[38px]"
          options={mosList}
          fieldNames={{ label: 'name', value: 'code' }}
          allowClear
          placeholder="选择蚊种"
          disabled={disabled}
          onChange={(v) => {
            updateValue(field.name, 'type', v, listName);
            setShowCustomType(v === MOS_TYPE_EXT.其他);
          }}
        />
      </Form.Item>
      <Form.Item
        className={`custom_type flex-1 h-[38px] mb-0 ${showCustomType ? '' : 'hidden'}`}
        name={[field.name, 'custom_type']}
        rules={
          showCustomType
            ? [
                {
                  required: true,
                  message: '请输入自定义类型',
                },
                {
                  pattern: NAME_REG_EXP,
                  message: '蚊种名称格式不正确',
                },
              ]
            : []
        }
      >
        <Input
          maxLength={20}
          disabled={disabled}
          className="h-[38px]  "
          allowClear
          placeholder="请输入自定义蚊种"
          onChange={(e) => updateValue(field.name, 'custom_type', e.target.value, listName)}
        />
      </Form.Item>
    </React.Fragment>
  );
};
interface VerifyItemProps {
  field: any;
  mosList: any[];
  form: any;
  updateListFields: (idx: number, key: string, value: string | number, type: fieldType) => void;
  canVerify: boolean;
  handleRemove: () => void;
  type: fieldType;
}

const VerifyItem = (props: VerifyItemProps) => {
  const { field, mosList, form, updateListFields, canVerify, handleRemove, type } = props;
  const isOther = useMemo(() => {
    return form.getFieldValue(type)[field.name]?.['code'] === MOS_TYPE_EXT.其他;
  }, [field]);
  return (
    <div className="flex gap-x-[10px] mt-[14px] items-center w-full">
      <Form.Item className="hidden" name={[field.name, 'order']}>
        <Input />
      </Form.Item>
      <Form.Item className="hidden" name={[field.name, 'data_type']}>
        <Input />
      </Form.Item>
      <Form.Item className="hidden" name={[field.name, 'source_id']}>
        <Input />
      </Form.Item>
      <MosquitoSelect
        field={field}
        mosList={mosList}
        form={form}
        updateValue={updateListFields}
        disabled={!canVerify}
        listName={type}
      />
      {isOther ? null : (
        <Form.Item
          className="flex-1 h-[38px] mb-0"
          name={[field.name, 'sex']}
          rules={[
            {
              required: true,
              message: '请选择性别',
            },
          ]}
        >
          <Select
            className="flex-1 h-[38px]"
            options={SEX_OPTIONS}
            placeholder="选择性别"
            onChange={(v) => updateListFields(field.name, 'sex', v, type)}
            disabled={!canVerify}
          />
        </Form.Item>
      )}
      {canVerify ? <IconCloseCircle onClick={handleRemove} className="VerifyItem-remove " /> : null}
    </div>
  );
};

export default forwardRef((props: any, ref) => {
  const { currentItem, mosqutioList, detail, onChange } = props;
  const list: IAuditData[] = detail?.audit_record_list;
  const [mosList, setMosList] = useState<any>([]);
  const [modalType, setModalType] = useState<GLOBAL_MODAL_TYPE>(GLOBAL_MODAL_TYPE.none);
  const [modalData, setModalData] = useState<any>({});
  const [form] = useForm();
  const [viewMode, setViewMode] = useState<VIEW_MODE>(VIEW_MODE.plainText);
  const [renderList, setRenderList] = useState<any>([]);

  useEffect(() => {
    const list = mosqutioList || [];
    const result = [
      ...list,
      {
        code: MOS_TYPE_EXT.其他,
        name: '其他',
      },
    ];
    setMosList(result);
  }, [mosqutioList]);

  useEffect(() => {
    const newList = Array.isArray(list)
      ? list.map((item) => {
          let mosInfo: any = {};
          if (item.data_type === 3) {
            mosInfo.name = item.custom_type;
            mosInfo.code = MOS_TYPE_EXT.其他;
          } else {
            mosInfo =
              mosList.find((mos: any) => {
                return item?.type?.toLocaleLowerCase() === mos?.code?.toLocaleLowerCase();
              }) || ({} as unknown as { code: string; name: string });
          }

          return { ...item, code: mosInfo.code, name: mosInfo.name };
        })
      : [];

    form.setFieldsValue({
      allList: newList,
    });
    setRenderList(newList);
  }, [list, form]);

  // AI验证区操作
  const updateListFields = (idx: number, key: string, value: string | number, type: fieldType) => {
    const currentValues = form.getFieldValue(type) || [];
    const next = [...currentValues];
    next[idx] = {
      ...next[idx],
      [key]: value,
    };
    form.setFieldsValue({
      allList: next,
    });
    setRenderList(next);
  };

  const onValueChange = useMemoizedFn(() => {
    const allList = form.getFieldValue('allList') || [];

    const list = [...allList]
      .map((item) => {
        if (item.type === MOS_TYPE_EXT.无蚊虫) {
          return {
            custom_type: MOS_TYPE_EXT.无蚊虫,
            data_type: 3,
            order: 0,
            sex: 0,
            type: MOS_TYPE_EXT.无蚊虫,
            source_id: 0,
          };
        }
        const baseItem = omit(item, ['name', 'code']);
        baseItem.type = item.code;
        // Check if type exists in mosqutioList
        const typeExists = mosqutioList?.some(
          (mos: any) => mos?.code?.toLowerCase() === item?.code?.toLowerCase(),
        );
        let result = {};
        if (!typeExists) {
          result = {
            ...baseItem,
            data_type: 3,
            type: MOS_TYPE_EXT.其他,
          };
        } else {
          result = {
            ...baseItem,
            custom_type: '',
          };
        }
        return result;
      })
      .filter((item) => item && !isEmpty(item))
      .map((item, index) => {
        return {
          ...item,
          // @ts-ignore
          sex: item.sex || 0,
          order: index + 1,
        };
      });
    onChange(list);
  });
  const onFinish = useMemoizedFn(() => {
    onValueChange();
    setViewMode(VIEW_MODE.plainText);
  });

  useImperativeHandle(ref, () => {
    return {
      check: () => {
        return form?.validateFields();
      },
    };
  });

  const closeModal = useMemoizedFn(() => {
    setModalData({});
    setModalType(GLOBAL_MODAL_TYPE.none);
  });

  const handleRemove = useMemoizedFn(
    (field: any, remove: (index: number) => void, type: fieldType) => {
      const allList = form.getFieldValue('allList') || [];
      const total = [...allList];
      if (total.length <= 1) {
        message.info('请在识别结果中选择其他类，并填写原因');
        return;
      }
      const values = form.getFieldValue(type) || [];
      const currentValue = values[field.name];
      const mosInfo = mosqutioList?.find(
        (mos: any) => mos.code.toLowerCase() === currentValue.type?.toLowerCase(),
      );
      let displayText = '-';
      if (mosInfo) {
        displayText = `${mosInfo.name}-${SEX_OPTIONS.find((opt) => opt.value === currentValue.sex)?.label || ''}`;
      } else {
        if (currentItem.type === MOS_TYPE_EXT.无蚊虫) {
          displayText = '无蚊虫';
        } else if (currentItem.type === MOS_TYPE_EXT.其他) {
          displayText = currentValue.custom_type || '其他';
        } else {
          displayText = '-';
        }
      }
      setModalType(GLOBAL_MODAL_TYPE.delete);
      setModalData({
        content: `请确认是否删除当前数据(${displayText})`,
        onOk: () => {
          remove(field.name);
        },
      });
    },
  );

  const canVerify = checkFunPermission(STATIC_PERMISSION_CODE.专家验证);
  const showResultVerify = useMemoizedFn(() => {
    setViewMode(VIEW_MODE.verify);
  });

  // if (AUDIT_VERIFY_DATA_STATUS.AI验证中 === currentItem.status) {
  //   return (
  //     <div className="flex-1">
  //       <div className="flex items-center h-[20px]">
  //         <IconRecognition className="mr-[8px] text-txt-blue" style={{ '--stoke-color': '#fff' }} />
  //         <span className="ml-[8px]">识别结果</span>
  //       </div>
  //       <span className="flex-1 flex items-center text-sys-yellow h-[80px] justify-center">
  //         <img src={verifyWaitImg} className="mr-[4px] size-[20px]" />
  //         星小尘正加急识别中，请稍等...
  //       </span>
  //     </div>
  //   );
  // }
  return (
    <div className="w-full">
      <div className="RecognitionResult-head flex items-center h-[20px]">
        <div className="flex items-center">
          <IconRecognition className="text-txt-blue" style={{ '--stoke-color': '#fff' }} />
          <span className="ml-[8px]">识别结果</span>
        </div>
        {canVerify ? (
          viewMode === VIEW_MODE.plainText ? (
            <div className="RecognitionResult-verifyBtn" onClick={showResultVerify}>
              专家识别
            </div>
          ) : (
            <div className="RecognitionResult-verifyBtn" onClick={onFinish}>
              完成
            </div>
          )
        ) : null}
      </div>
      {viewMode === VIEW_MODE.plainText ? (
        <div className="RecognitionResult-desc">
          {currentItem.status === AUDIT_VERIFY_DATA_STATUS.待专家验证 ? (
            <>
              <img src={verifyFailImg} className="mr-[4px] size-[20px] text-[#FF7818]" />
              <span className="text-[#FF7818]">星小尘已完成识别（有部分存疑，请专家识别）</span>
            </>
          ) : AUDIT_VERIFY_DATA_STATUS.AI验证中 === currentItem.status ? (
            <span className="flex-1 flex items-center text-sys-yellow h-[80px] justify-center">
              <img src={verifyWaitImg} className="mr-[4px] size-[20px]" />
              星小尘正加急识别中，请稍等...
            </span>
          ) : (
            '以下为星小尘识别结果：'
          )}
        </div>
      ) : null}
      <Form form={form} onValuesChange={onValueChange}>
        {viewMode === VIEW_MODE.plainText ? (
          renderList?.length > 0 ? (
            renderList.map((item: any, index: number) => {
              return (
                <div key={index} className="RecognitionResult-plainText">
                  {MOS_NAME_LIST_ZH[item.type.toLowerCase()] || item.custom_type}-1只(
                  {SEX_OPTIONS.find((opt) => opt.value === item.sex)?.label})
                </div>
              );
            })
          ) : null
        ) : (
          <div className="mb-[20px]">
            <Form.List name="allList">
              {(fields, { add, remove }) => (
                <>
                  {fields.map((field, idx) => (
                    <VerifyItem
                      key={idx}
                      field={field}
                      mosList={mosList}
                      form={form}
                      updateListFields={updateListFields}
                      canVerify={canVerify}
                      type={fieldType.allList}
                      handleRemove={() => handleRemove(field, remove, fieldType.allList)}
                    />
                  ))}
                  <div
                    className="RecognitionResult-addBtn"
                    onClick={() => {
                      if (fields.length < 10) {
                        add({
                          order: 0,
                          source_id: 0,
                          data_type: 1,
                          sex: 0,
                          type: undefined,
                        });
                      }
                    }}
                  >
                    <IconPlus className="plusIcon" />
                    添加蚊种
                  </div>
                </>
              )}
            </Form.List>
          </div>
        )}
      </Form>
      <XcModalForm
        title={'删除'}
        width={500}
        open={modalType === GLOBAL_MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={(visible) => {
          if (!visible) {
            closeModal();
          }
        }}
        onFinish={() => {
          modalData?.onOk?.();
          closeModal();
          return Promise.resolve(true);
        }}
      >
        <div className="my-[40px]">{modalData.content}</div>
      </XcModalForm>
    </div>
  );
});
