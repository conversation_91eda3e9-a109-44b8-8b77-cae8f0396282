import { Checkbox } from 'antd';
import { CheckboxChangeEvent } from 'antd/lib';
import _ from 'lodash';
import { useEffect, useState } from 'react';
import { checkedNodesByIds, getAllCheckedIds, handleTree, TreeNode } from './TreeDataHandle';
import { IPermission, PERMISSION_TREE, STATIC_PERMISSION_CODE } from '@/utils/permission';
import { getAllMosqutio } from '@/utils/mosqutio.util';
import { mergeTrees } from '@/utils/tree.util';

function PermissionContent(
  props: Record<string, any> & {
    menu: IPermission;
    onCheckBoxChange: (event: CheckboxChangeEvent) => void;
  },
) {
  const { menu, onCheckBoxChange } = props;
  return (
    <div className="xc-permission-content">
      {Array.isArray(menu.children)
        ? menu.children.map((item) => {
            return (
              <Checkbox
                className="xc-permission-checkbox"
                key={item.id}
                value={item.id}
                onChange={onCheckBoxChange}
                checked={item.checked}
              >
                {item.name}
              </Checkbox>
            );
          })
        : null}
    </div>
  );
}
export function MenuPermissionTable(
  props: Record<string, any> & {
    data: IPermission[];
    onChange: (ids: number[]) => any;
    ownList?: number[];
  },
) {
  const { data, ownList, onChange } = props;

  const [treeData, setTreeData] = useState<IPermission>({} as IPermission);
  const [currentChecked, setCurrentChecked] = useState<number[]>([]);

  useEffect(() => {
    onChange(currentChecked);
  }, [currentChecked]);

  useEffect(() => {
    const treeData = _.cloneDeep(PERMISSION_TREE);
    const tree: IPermission[] = {
      id: STATIC_PERMISSION_CODE.菜单权限,
      name: '菜单权限',
      checked: false,
      children: treeData,
    };

    let newTree = mergeTrees(tree, data, 'code');
    if (ownList) {
      // @ts-ignore
      newTree = checkedNodesByIds(tree, ownList || []);
    }
    setTreeData(newTree as IPermission);
    const ids = getAllCheckedIds(newTree as TreeNode);
    setCurrentChecked(ids);
  }, [ownList, data]);

  const onCheckBoxChange = (event: CheckboxChangeEvent) => {
    const newData = handleTree(
      treeData as TreeNode,
      event.target.checked,
      event.target.value,
    ) as IPermission;
    setTreeData({ ...newData });
    const ids = getAllCheckedIds(treeData as TreeNode);
    setCurrentChecked(ids);
  };

  return (
    <>
      <div className="xc-permission-header">
        <Checkbox onChange={onCheckBoxChange} checked={treeData.checked} value={treeData.id}>
          {treeData.name}
        </Checkbox>
      </div>
      {treeData.children?.map((menu: any, index) => {
        return (
          <div className="xc-permission-body" key={index}>
            <div className="xc-permission-menu ">
              <Checkbox
                value={menu.id}
                onChange={onCheckBoxChange}
                checked={menu.checked}
                key={menu.id}
              >
                {menu.name}
              </Checkbox>
            </div>
            {![
              STATIC_PERMISSION_CODE.企业管理,
              STATIC_PERMISSION_CODE.规则管理,
              STATIC_PERMISSION_CODE.报表中心,
            ].includes(menu.id) ? (
              <PermissionContent menu={menu} onCheckBoxChange={onCheckBoxChange} />
            ) : Array.isArray(menu.children) ? (
              <div className="flex-1">
                {menu.children.map((item, index) => {
                  return (
                    <div className="xc-permission-body" key={index}>
                      <div className="xc-permission-menu ">
                        <Checkbox
                          onChange={onCheckBoxChange}
                          checked={item.checked}
                          key={item.id}
                          value={item.id}
                        >
                          {item.name}
                        </Checkbox>
                      </div>
                      <PermissionContent menu={item} onCheckBoxChange={onCheckBoxChange} />
                    </div>
                  );
                })}
              </div>
            ) : null}
          </div>
        );
      })}
    </>
  );
}

export function DataPermissionTable(
  props: Record<string, any> & {
    data: IPermission;
    onChange: (ids: number[]) => any;
    ownList?: number[];
  },
) {
  const { data, ownList, onChange } = props;
  const [treeData, setTreeData] = useState<IPermission>({} as IPermission);
  const [currentChecked, setCurrentChecked] = useState<number[]>([]);

  useEffect(() => {
    let newData = _.cloneDeep(data?.children || []);
    newData = getAllMosqutio(newData);
    const tree = {
      id: STATIC_PERMISSION_CODE.数据权限,
      name: '数据权限',
      checked: false,
      children: [{ ...data, children: [...newData] }],
    };
    const newTree = checkedNodesByIds(tree as TreeNode, ownList || []);
    setTreeData(newTree as IPermission);
    setCurrentChecked(getAllCheckedIds(newTree as TreeNode));
  }, [ownList, data]);

  const onCheckBoxChange = (event: CheckboxChangeEvent) => {
    const newData = handleTree(
      treeData as TreeNode,
      event.target.checked,
      event.target.value,
    ) as IPermission;
    setTreeData({ ...newData });
    setCurrentChecked(getAllCheckedIds(treeData as TreeNode));
  };

  useEffect(() => {
    onChange(currentChecked);
  }, [currentChecked]);

  const menu = treeData.children?.[0] || ({ checked: false } as IPermission);
  return (
    <>
      <div className="xc-permission-header">
        <Checkbox
          onChange={onCheckBoxChange}
          checked={treeData.checked}
          value={treeData.id}
          key={treeData.id}
        >
          {treeData.name}
        </Checkbox>
      </div>
      <div className="xc-permission-body">
        <div className="xc-permission-menu">
          <Checkbox
            value={menu.id}
            onChange={onCheckBoxChange}
            checked={menu.checked}
            key={menu.id}
          >
            {menu.name}
          </Checkbox>
        </div>
        <PermissionContent menu={menu} onCheckBoxChange={onCheckBoxChange} />
      </div>
    </>
  );
}
