import { useEffect, useRef } from 'react';
import { previewFitScale } from './previewScale';

interface Scale {
  /**
   * Width ratio
   */
  widthRatio: number;
  /**
   * Height ratio
   */
  heightRatio: number;
}

interface IProps {
  width?: number;
  height?: number;
  // TODO 未实现
  mode?: 'fit' | 'scrollX' | 'scrollY' | 'full';
  onScaleChange?: (scale: Scale) => void;
}

export const useFitScreen = (props: IProps = {}) => {
  const previewRef = useRef<HTMLDivElement>(null);

  // const { width = 1920, height = 1080, mode, onScaleChange } = props;
  const { width = 1920, height = 1080 } = props;

  useEffect(() => {
    const { calcRate, windowResize, unWindowResize } = previewFitScale(
      width,
      height,
      previewRef.current,
    );
    calcRate();
    windowResize();
    return () => {
      unWindowResize();
    };
  }, []);

  return {
    previewRef,
  };
};
