import { useMemoizedFn } from 'ahooks';
import { useEffect, useMemo, useRef } from 'react';
import { useForm } from 'antd/es/form/Form';
import { getRangeByTimeUnit, TIME_UNITS } from '@/utils/time.util';
import { DATE_FMT } from '@/utils/time.util';
import { isBlank } from '@/utils/string.util';
import { Form, Select, TreeSelect } from 'antd';
import XcMosSelect from '@/components/XcMosSelect';
import XcTimeUnit from '@/components/XcTimeUnit';
import DateSelect from '@/components/DateSelect';
import _ from 'lodash';
import './DataSearchForm.css';
import { IconArrowDown } from '@/assets/svg';
import { useModel } from '@umijs/max';

export enum FormFields {
  region = 'region',
  monitor_type = 'monitor_type',
  time_unit = 'time_unit',
  dateRange = 'dateRange',
  growth_type = 'growth_type',
  sensor_type = 'sensor_type',
}

interface DataSearchFormProps {
  menu: any;
  onRequest: any;
  allowFields?: FormFields[] | string[];
  initialValue?: any;
}

const defaultFields = [
  FormFields.region,
  FormFields.monitor_type,
  FormFields.time_unit,
  FormFields.dateRange,
];

export default function DataSearchForm(props: DataSearchFormProps) {
  const { menu, onRequest, allowFields = defaultFields, initialValue } = props;
  const [form] = useForm();
  const queryRef = useRef<any>({});
  const defaultRange = getRangeByTimeUnit(TIME_UNITS.MONTH);
  const { mosquitoList } = useModel('useMosquito', (m) => {
    return {
      mosquitoList: m.mosquitoList,
    };
  });
  const { regionLoad } = useModel('useDevice', (m) => ({
    regionLoad: m.regionLoad,
  }));
  const allCode = useMemo(() => {
    return mosquitoList.map((item: any) => item.code);
  }, [mosquitoList]);
  const requestData = useMemoizedFn(
    _.debounce(() => {
      const { dateRange = defaultRange, region, monitor_type, ...others } = form.getFieldsValue();
      const [province, city, district, street, device_id] = (region || '').split('.');
      queryRef.current = {
        ...queryRef.current,
        province,
        city,
        district,
        street,
        dev_ids: device_id,
        ...others,
      };

      queryRef.current.start_time = dateRange[0].format(DATE_FMT.DATE_TIME);
      queryRef.current.end_time = dateRange[1].format(DATE_FMT.DATE_TIME);

      if (!monitor_type) {
        queryRef.current.monitor_type = allCode.join(',');
      }
      Object.keys(queryRef.current).forEach((item) => {
        if (isBlank(queryRef.current[item])) {
          delete queryRef.current[item];
        }
      });
      if (queryRef.current.monitor_type) {
        onRequest(queryRef.current);
      }
    }, 20),
  );

  useEffect(() => {
    if (regionLoad) {
      form.setFieldsValue(initialValue || {});
      requestData();
    }
  }, [regionLoad]);

  const onValuesChange = (keys: any) => {
    requestData();
  };
  const onDateSelect = (dateRange: any) => {
    form.setFieldsValue({
      dateRange,
    });
    requestData();
  };

  const handleClick = (_keys: any, _value: any) => {
    queryRef.current.device_code = _value?.code || '';
  };

  return (
    <div
      className="flex items-center mb-[16px] gap-x-[10px] data-search-form relative"
      style={{ paddingRight: allowFields.includes(FormFields.time_unit) ? '84px' : 0 }}
    >
      <Form
        layout="inline"
        initialValues={queryRef.current}
        onValuesChange={onValuesChange}
        form={form}
        className="flex items-center gap-y-[16px] flex-wrap"
      >
        {allowFields.includes(FormFields.region) && (
          <Form.Item name="region" style={{ marginRight: 10 }}>
            <TreeSelect
              style={{
                width: '300px',
              }}
              fieldNames={{
                label: 'name',
                value: 'key',
                children: 'children',
              }}
              className="DataSearchForm-TreeSelect"
              allowClear
              treeData={menu}
              onChange={handleClick}
              onSelect={handleClick}
              treeDefaultExpandAll
              multiple={false}
              placeholder="全部区域"
              suffixIcon={<IconArrowDown className="text-[#999]" />}
            />
          </Form.Item>
        )}
        {allowFields.includes(FormFields.monitor_type) && (
          <Form.Item name="monitor_type" style={{ marginRight: 10 }}>
            <XcMosSelect
              placeholder="全部蚊虫"
              style={{ width: 120 }}
              suffixIcon={<IconArrowDown className="text-[#999]" />}
            ></XcMosSelect>
          </Form.Item>
        )}
        {allowFields.includes(FormFields.sensor_type) && (
          <Form.Item name="sensor_type" style={{ marginRight: 10 }}>
            <Form.Item
              name="sensor_type"
              initialValue="temp"
              className="w-[140px]"
              style={{ marginRight: 10 }}
            >
              <Select
                placeholder="传感器类型"
                options={[
                  {
                    label: '温度',
                    value: 'temp',
                  },
                  {
                    label: '湿度',
                    value: 'hum',
                  },
                  {
                    label: '风速',
                    value: 'wind',
                  },
                  {
                    label: '二氧化碳',
                    value: 'co2',
                  },
                  {
                    label: '光照强度',
                    value: 'light',
                  },
                ]}
                suffixIcon={<IconArrowDown className="text-[#999]" />}
              ></Select>
            </Form.Item>
          </Form.Item>
        )}
        {allowFields.includes(FormFields.growth_type) && (
          <Form.Item
            name="growth_type"
            initialValue="mtom"
            className="w-[140px]"
            style={{ marginRight: 10 }}
          >
            <Select
              placeholder="全部蚊虫"
              options={[
                {
                  label: '环比增长率',
                  value: 'mtom',
                },
                {
                  label: '同比增长率',
                  value: 'ytoy',
                },
              ]}
              suffixIcon={<IconArrowDown className="text-[#999]" />}
            ></Select>
          </Form.Item>
        )}
        {allowFields.includes(FormFields.dateRange) && (
          <>
            <Form.Item name="dateRange" initialValue={defaultRange} className="hidden"></Form.Item>
            <DateSelect
              defaultRange={defaultRange}
              defaultValue={defaultRange}
              onChange={onDateSelect}
            />
          </>
        )}
        {allowFields.includes(FormFields.time_unit) && (
          <Form.Item
            name="time_unit"
            initialValue="day"
            style={{ marginRight: 10, position: 'absolute', right: 0, top: 0 }}
          >
            <XcTimeUnit
              suffixIcon={<IconArrowDown className="text-[#999]" />}
              style={{ width: '74px' }}
            ></XcTimeUnit>
          </Form.Item>
        )}
      </Form>
    </div>
  );
}
