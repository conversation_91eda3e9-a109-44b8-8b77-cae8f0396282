/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-19 18:31:50
 * @LastEditTime: 2025-01-14 22:30:14
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export const IconDevice = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#2953FF" />
      <rect
        width="10.6667"
        height="10.6667"
        transform="translate(6.66797 6.66626)"
        fill="#2953FF"
      />
      <path
        d="M12.0012 15.555H16.0012M8.00122 15.555H8.74546C8.96288 15.555 9.07158 15.555 9.17388 15.5305C9.26458 15.5087 9.35129 15.4728 9.43082 15.424C9.52052 15.3691 9.59739 15.2922 9.75113 15.1385L15.3346 9.55503C15.7028 9.18684 15.7028 8.58989 15.3346 8.2217C14.9664 7.85351 14.3694 7.85351 14.0012 8.2217L8.41778 13.8051C8.26405 13.9589 8.18718 14.0357 8.13221 14.1254C8.08347 14.205 8.04756 14.2917 8.02578 14.3824C8.00122 14.4847 8.00122 14.5934 8.00122 14.8108V15.555Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
