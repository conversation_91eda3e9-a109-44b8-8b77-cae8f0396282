import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { IPagination } from './interface/pagination.interface';
import { XcTableBatch } from './XcTableBatch';
import XcAntdTable from '../XcAntdTable/XcAntdTable';
import { removeBlankProperty } from '@/utils/format';
import { ColumnsType } from 'antd/lib/table';
import { XcTableSearchNew } from './XcTableSearchNew';
import { COLUMN_RENDER_TYPE, IColumns } from './interface/search.interface';
import XcAntdText from '../XcAntdText';

interface IProps {
  initSearch?: boolean;
  scroll?: { x?: number; y?: number };
  searchTitle?: React.ReactNode;
  loading?: boolean;
  delay?: number;
  rowId?: string;
  extend?: React.ReactNode;
  operator?: React.ReactNode;
  columns: IColumns[];
  pagination?: IPagination;
  bodyClassName?: string;
  batchRender?: () => React.ReactNode;
  request: (params?: any) => Promise<any>;
  /**
   * @deprecated
   * @param params
   * @returns
   */
  onSearch?: (params: any) => void;
  /**
   * @deprecated
   * @param params
   * @returns
   */
  onSearchChange?: (params: any) => void;
  rowKey?: string;
  [key: string]: any;
}

export interface ITableRef {
  onSubmit: () => Promise<void>;
  checkedListRef: any;
}

function renderPosition(value: string, item: any) {
  return (
    <XcAntdText>
      {item.province + '-' + item.city + '-' + item.district + '-' + item.street}
    </XcAntdText>
  );
}

function renderTextEllipsis(value: string) {
  return <XcAntdText ellipsis={{ tooltip: value }}>{value}</XcAntdText>;
}

export const XcTableNew = memo(
  forwardRef<unknown, IProps>((props, ref) => {
    const {
      request,
      columns,
      extend,
      operator,
      loading,
      batchRender,
      initSearch = true,
      pagination,
      searchTitle,
      ...tableProps
    } = props;
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [checkedList, setCheckedList] = useState<any[]>([]);
    const searchColumns = columns.filter((it) => it.hideInTable === true);
    const tableColumns = useMemo(() => {
      const list = columns.filter((it: any) => it.hideInTable === undefined);
      return list.map((item: any) => {
        if (item.renderType === COLUMN_RENDER_TYPE.text_ellipsis) {
          item.render = renderTextEllipsis;
        }
        if (item.renderType === COLUMN_RENDER_TYPE.text_position) {
          item.render = renderPosition;
        }
        return item;
      });
    }, [columns]);

    const checkedListRef = useRef<any[]>([]);
    const formRef = useRef<any>({});
    const paginationRef = useRef({
      current: 1,
      pageSize: 50,
      total: 0,
    });
    useEffect(() => {
      paginationRef.current = {
        ...paginationRef.current,
        pageSize: pagination?.pageSize || pagination?.defaultPageSize || 50,
      };
    }, [pagination]);
    // @ts-ignore
    const onSelectItem = useCallback(
      (selectedRowKeys: any, selectedRows: any[]) => {
        checkedListRef.current = selectedRowKeys.map((id: number) => {
          return dataSource.find((item) => item.id === id);
        });
        setCheckedList(selectedRowKeys || []);
      },
      [dataSource],
    );

    const onRest = () => {
      setCheckedList([]);
    };

    const requestTable = useCallback(async () => {
      let values = formRef.current?.getFieldsValue();
      values = removeBlankProperty(values);
      const { current, pageSize } = paginationRef.current;
      const queryPayload = {
        ...values,
        current: current,
        pageSize: pageSize,
        tenant_id: 1,
      };
      // console.log('queryPayload', queryPayload);
      const result = await request(queryPayload);
      // console.log('result ', result);
      paginationRef.current.total = result?.total || 0;
      setDataSource(result?.data || []);
    }, [request]);
    useImperativeHandle(ref, () => {
      return {
        onSubmit: () => {
          requestTable();
        },
        checkedListRef: checkedListRef,
      };
    });
    useEffect(() => {
      if (initSearch) {
        requestTable();
      }
    }, [initSearch]);
    return (
      <div className="relative w-full h-full flex flex-col">
        <div>
          <XcTableSearchNew
            columns={searchColumns}
            requestTable={() => {
              paginationRef.current.current = 1;
              requestTable();
            }}
            ref={formRef}
            className={checkedList.length > 0 ? 'hidden' : ''}
            searchTitle={searchTitle}
          >
            {operator}
          </XcTableSearchNew>
          {checkedList.length > 0 && (
            <XcTableBatch
              total={checkedList.length}
              onReset={onRest}
              onDelete={function (): void {
                throw new Error('Function not implemented.');
              }}
              batchRender={batchRender}
            />
          )}
          {extend}
        </div>
        <div className="flex-1">
          <XcAntdTable
            columns={tableColumns as ColumnsType}
            loading={loading}
            pagination={{
              ...(pagination || {}),
              ...paginationRef.current,
              onChange: (page, pageSize) => {
                paginationRef.current.current = page;
                paginationRef.current.pageSize = pageSize;
                requestTable();
              },
            }}
            dataSource={dataSource}
            rowSelection={
              tableProps.rowSelection !== null
                ? {
                    selectedRowKeys: checkedList,
                    onChange: onSelectItem,
                  }
                : undefined
            }
            {...tableProps}
          ></XcAntdTable>
        </div>
      </div>
    );
  }),
);
