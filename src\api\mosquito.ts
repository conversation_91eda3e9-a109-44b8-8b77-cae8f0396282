/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-24 23:49:19
 * @LastEditTime: 2024-12-01 12:53:35
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const MosquitoAPI = {
  status: '/v1/mosquito/status',
  mosquitoDeviceStatus: '/v1/mosquito/device/status',
  trendHour: '/v1/mosquito/trend/hourly',
  trendDaily: '/v1/mosquito/trend/daily',
  realtime: '/v1/mosquito/realtime',
  monitoring: '/v1/mosquito/monitoring',
  speciesMonthly: '/v1/mosquito/species/monthly',
  speciesImages: '/v1/mosquito/species/images',
  speciesTrend: '/v1/mosquito/species/trend',
  // 排名
  speciesStats: '/v1/mosquito/species/stats',
  //实时监测记录
  getAuditList: '/v1/audit/mosquito/list',
  // 实时监测记录详情
  getAuditDetail: (id: number) => `/v1/audit/mosquito/detail/${id}`,
  deleteAudit: '/v1/audit/mosquito/delete',
  // 提交蚊媒审核
  postSubmitMosquito: 'v1/audit/mosquito/submit',
};
