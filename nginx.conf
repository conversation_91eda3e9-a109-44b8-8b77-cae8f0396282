server {
  listen 80;
  server_name localhost;

  gzip on;
  gzip_min_length 1k;
  gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript image/jpeg image/gif image/png image/webp;
  gzip_vary on;
  gzip_buffers 2 4k;
  gzip_http_version 1.1;

  client_max_body_size 20M;
  keepalive_timeout 10;
  add_header X-Frame-Options SAMEORIGIN always;

  error_page 500 502 503 504 /50x.html;

  location = /50x.html {
    root /usr/share/nginx/html;
  }

  index index.html index.htm;

  location / {
    root /usr/share/nginx/html;
    try_files $uri $uri/ /index.html;
    expires 1h;
  }

  location ^~ /static/ {
    root /usr/share/nginx/html;
    gzip_static on;
    expires 1h;
    add_header Cache-Control public;
  }
}