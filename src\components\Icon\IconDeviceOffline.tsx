/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:31:05
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDeviceOffline = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="20"
      height="7"
      viewBox="0 0 20 7"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M3.74512 3.5H16.2551" stroke="#343561" strokeWidth="6" strokeLinecap="round" />
    </svg>
  );
};
