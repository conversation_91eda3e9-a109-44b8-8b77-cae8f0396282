import { uniqueId } from 'lodash';

export function loadJS(src: string) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.onerror = () => {
      const msg = 'loadJS error: ' + src;
      console.error('loadJS error', src);
      reject(msg);
    };
    script.onload = () => {
      resolve(true);
    };
    document.head.appendChild(script);
  });
}
export function loadBaiduMapApi() {
  const ak =
    location.hostname !== 'localhost'
      ? '31MalseHSdsbjfRllFIBdLLrAzZXTxUO'
      : 'bbXCQQR0JpnRkh8Lej7nBw1jvFKMENKr';
  return new Promise((resolve, reject) => {
    if (window.BMapGL) {
      resolve(true);
    } else {
      const baiduCallbackName = `baidu_${uniqueId()}`;
      // @ts-ignore
      window[baiduCallbackName] = () => {
        if (window.BMapGL) {
          resolve(true);
        } else {
          reject('百度地图加载失败');
        }
      };
      loadJS(`//api.map.baidu.com/api?type=webgl&v=3.0&ak=${ak}&callback=${baiduCallbackName}`);
    }
  });
}
