/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 12:18:14
 * @LastEditTime: 2025-02-06 19:50:10
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { ModalForm, ModalFormProps } from '@ant-design/pro-components';
import { forwardRef, memo } from 'react';

export const XcModalForm = memo(
  forwardRef((props: ModalFormProps, ref: any) => {
    const { children, submitter, ...rest } = props;
    return (
      <ModalForm
        formRef={ref}
        modalProps={{
          centered: true,
        }}
        {...rest}
        submitter={{
          resetButtonProps: {
            style: {
              backgroundColor: '#F4F4F6',
              border: '0',
              fontSize: '14px',
              fontWeight: 'normal',
              borderRadius: '8px',
              width: '108px',
              height: '36px',
            },
          },
          submitButtonProps: {
            style: {
              border: '0',
              fontSize: '14px',
              fontWeight: 'normal',
              borderRadius: '8px',
              width: '108px',
              height: '36px',
            },
          },
          ...(submitter || {}),
        }}
      >
        {children}
      </ModalForm>
    );
  }),
);
