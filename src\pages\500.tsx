/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-22 14:01:19
 * @LastEditTime: 2025-01-22 14:02:39
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { history } from '@umijs/max';
import { Button, Result } from 'antd';
import React from 'react';

const NoFoundPage: React.FC = () => (
  <Result
    status="500"
    title="500"
    subTitle={'服务器开小差了～'}
    extra={
      <Button type="primary" onClick={() => history.push('/')}>
        返回
      </Button>
    }
  />
);

export default NoFoundPage;
