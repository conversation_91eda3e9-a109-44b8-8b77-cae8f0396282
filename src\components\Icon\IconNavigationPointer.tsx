/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 20:13:51
 * @LastEditTime: 2025-01-14 22:34:41
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconNavigationPointer = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="32" height="32" rx="8" fill="#5A5D64" />
      <path
        d="M9.75452 15.0812C9.32114 14.9126 9.10445 14.8284 9.04118 14.707C8.98634 14.6017 8.98627 14.4763 9.04099 14.371C9.10411 14.2495 9.3207 14.165 9.75388 13.9959L22.0473 9.19847C22.4383 9.04587 22.6339 8.96957 22.7588 9.01131C22.8673 9.04756 22.9524 9.1327 22.9887 9.2412C23.0304 9.36614 22.9541 9.56166 22.8015 9.9527L18.0041 22.2461C17.835 22.6793 17.7505 22.8959 17.629 22.959C17.5237 23.0137 17.3983 23.0137 17.293 22.9588C17.1716 22.8956 17.0874 22.6789 16.9188 22.2455L15.0057 17.326C14.9715 17.238 14.9544 17.194 14.928 17.157C14.9045 17.1242 14.8758 17.0955 14.843 17.072C14.806 17.0456 14.762 17.0285 14.674 16.9943L9.75452 15.0812Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
