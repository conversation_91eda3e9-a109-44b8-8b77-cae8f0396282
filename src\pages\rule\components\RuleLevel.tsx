export default function (props: { value: string }) {
  const { value } = props;
  const boxClass = 'flex  h-[24px] w-[62px] gap-[6px] justify-center rounded-[12px] items-center'
  const dotClass = 'w-[8px] h-[8px] rounded-[4px]'
  if (value === 'height' || value === '高') {
    return (
      <div className={`${boxClass} text-[#FD521B]  bg-[#FDF0EC] `} >
        <div className={`${dotClass} bg-[#FD521B]`}></div>
        <span>高等</span>
      </div>
    );
  }
  if (value === 'medium' || value === '中') {
    return (
      <div className={`${boxClass} text-[#FFA63F]  bg-[#FDF6EC]`} >
        <div className={`${dotClass} bg-[#FFA63F]`}></div><span>中等</span>
      </div>
    );
  }
  return (
    <div className={`${boxClass} text-[#07C777]  bg-[#ECFDF3]`}>
      <div className={`${dotClass} bg-[#07C777]`}></div><span>低等</span>
    </div>
  );
}