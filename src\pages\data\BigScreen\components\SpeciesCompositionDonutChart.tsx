import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { ReactComponent as ScdcIcon } from '../assets/scdc-icon.svg';
import '@/css/vw/SpeciesCompositionDonutChart.css';
import { statisticsAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { useEffect, useMemo } from 'react';
import { getMosColor, getMosName, isMainMos } from '@/utils/mosqutio.util';
import { EChartsOption } from 'echarts';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';

export default function SpeciesCompositionDonutChart(props: any) {
  const { region, startTime, endTime, monitorType } = props;
  const { run: getSpeciesCompose, data: speciesComposeRes } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getSpeciesCompose,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    const { device_id, ...rest } = region;
    if (!region?.province && !device_id) {
      return;
    }
    const [startTime1, endTime1] = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
    getSpeciesCompose({
      ...rest,
      dev_ids: device_id,
      start_time: startTime || startTime1,
      end_time: endTime || endTime1,
      monitor_type: monitorType,
    });
  }, [region, startTime, endTime, monitorType]);

  const speciesCompose = useMemo(() => {
    const list = speciesComposeRes?.species || [];
    const mosList: any[] = [];
    const otherList: any[] = [];
    list.forEach((item: any) => {
      if (isMainMos(item.monitor_type)) {
        item.name = getMosName(item.monitor_type);
        item.color = getMosColor(item.monitor_type, 'big-screen');
        mosList.push(item);
      } else {
        otherList.push(item);
      }
    });
    let result = mosList;

    const otherCount = otherList.reduce(
      (pre: number, cur: { count: number }) => pre + cur.count,
      0,
    );
    if (otherCount > 0) {
      result = result.concat({
        name: '其他',
        count: otherCount,
        color: getMosColor('other', 'big-screen'),
      });
    }
    return result;
  }, [speciesComposeRes]);

  const option = useMemo(() => {
    const echartsData = {
      tooltip: { show: false },
      legend: {
        show: false,
      },
      grid: {
        top: 40,
        bottom: 40,
      },
      series: [
        {
          type: 'pie',
          radius: ['50%', '70%'],
          center: ['50%', '50%'],
          label: { show: false, position: 'center' },
          data: speciesCompose.map((s: { count: number; name: string; color: string }) => {
            return {
              value: s.count,
              name: s.name + ' (' + s.count + ')',
              itemStyle: {
                color: s.color,
              },
            };
          }),
        },
      ],
    };
    return echartsData as EChartsOption;
  }, [speciesCompose]);
  const total = useMemo(() => {
    return speciesCompose.reduce((pre: number, cur: { count: number }) => pre + cur.count, 0);
  }, [speciesCompose]);

  return (
    <div className="scdc-card-container">
      <div className="scdc-title-container">
        <ScdcIcon className="scdc-title-icon" />
        <h3 className="scdc-title-text">蚊种构成</h3>
      </div>
      <div className="scdc-chart-content">
        <div className="scdc-stat-box">
          <EChartsCommon option={option} />
          <div className="scdc-stat-value">{total}</div>
          <div className="scdc-stat-label">总数</div>
        </div>
        <div className="scdc-list-box">
          {total > 0 ? (
            speciesCompose.map((item: { name: string; count: number; color: string }, index) => (
              <div className="scdc-list-item" key={index}>
                <div className="scdc-color-rect" style={{ backgroundColor: item.color }} />
                <div className="scdc-species-name text-one-row" title={item.name}>
                  {item.name}
                </div>
                <div className="scdc-species-value">{item.count}只</div>
              </div>
            ))
          ) : (
            <div className="scdc-list-item">
              <div className="scdc-species-name">暂无数据</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
