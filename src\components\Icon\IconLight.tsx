/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-23 22:01:51
 * @LastEditTime: 2025-01-14 22:34:02
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export const IconLight = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="16" height="16" rx="8" fill="#2953FF" />
      <g clipPath="url(#clip0_118_744)">
        <path
          d="M7.33317 9.88628V10.6667C7.33317 11.0349 7.63165 11.3334 7.99984 11.3334C8.36803 11.3334 8.6665 11.0349 8.6665 10.6667V9.88628M7.99984 4.66675V5.00008M4.99984 8.00008H4.6665M5.83317 5.83341L5.63314 5.63338M10.1665 5.83341L10.3666 5.63338M11.3332 8.00008H10.9998M9.99984 8.00008C9.99984 9.10465 9.10441 10.0001 7.99984 10.0001C6.89527 10.0001 5.99984 9.10465 5.99984 8.00008C5.99984 6.89551 6.89527 6.00008 7.99984 6.00008C9.10441 6.00008 9.99984 6.89551 9.99984 8.00008Z"
          stroke="white"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};
