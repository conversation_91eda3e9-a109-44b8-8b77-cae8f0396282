/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 02:21:28
 * @LastEditTime: 2025-02-12 15:37:57
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { IconChevronLeft } from '@/components/Icon/IconChevronLeft';
import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { memo } from 'react';
import { CurrentTime } from './CurrentTime';

interface IProps {
  title?: string | React.ReactNode;
  back?: () => void;
}

const NavContainer = styled.div`
  position: absolute;
  background-color: #090914;
  top: 0;
  width: ${() => px2vw(1920)};
  height: ${() => px2vh(60)};
  display: flex;
  align-items: center;
  justify-content: center;
`;
const NavLeftContainer = styled.div`
  cursor: pointer;
  position: absolute;
  z-index: 100;
  display: flex;
  left: ${() => px2vw(30)};
  align-items: center;
  gap: ${() => px2vw(5)};
`;
const NavLeftIcon = styled(IconChevronLeft)`
  width: ${() => px2vw(32)};
  height: ${() => px2vw(32)};
`;
const NavLeftTitle = styled.div`
  color: #fff;
  font-family: 'YouSheBiaoTiHei';
  font-size: ${() => px2vw(30)};
  font-style: normal;
  font-weight: 400;
  line-height: normal;
`;
const NavRightContainer = styled.div`
  position: absolute;
  z-index: 100;
  right: ${() => px2vw(30)};
  display: flex;
  align-items: center;
  color: #7979a6;
  font-style: normal;
  font-size: ${() => px2font(14)};
`;

export const Nav = memo((props: IProps) => {
  const { title, back } = props;

  return (
    <NavContainer>
      <NavLeftContainer
        onClick={() => {
          back?.();
        }}
      >
        {back && <NavLeftIcon />}
        <NavLeftTitle>{title}</NavLeftTitle>
      </NavLeftContainer>
      <NavRightContainer>
        <CurrentTime />
      </NavRightContainer>
    </NavContainer>
  );
});
