import { IColumns } from '@/components/XcTable/interface/search.interface';
import { request, useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { logApi } from '@/api/log';
import { permissionApi } from '@/api/permission';
import { XcTableNew } from '@/components/XcTable/XcTableNew';

const { Text } = Typography;

export default () => {
  const [operateList, setOperateList] = useState<any>([{ label: '全部对象', value: 'all' }]);
  const [typeList, setTypeList] = useState<any>([{ label: '全部类型', value: 'all' }]);

  useEffect(() => {
    request(permissionApi.getDataPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
        type: 'menu',
      },
    }).then((res) => {
      let list = res?.data?.list?.[0]?.children || [];
      list = list.map((item: any) => {
        return {
          label: item.name,
          value: item.code,
        };
      });
      list.unshift({ label: '全部对象', value: 'all' });
      setOperateList(list);
    });
    request(permissionApi.getDataPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
        type: 'action',
      },
    }).then((res) => {
      let list = res?.data?.list?.[0]?.children || [];

      list = list.map((item: any) => {
        return {
          label: item.name,
          value: item.code,
        };
      });
      list.unshift({ label: '全部类型', value: 'all' });
      setTypeList(list);
    });
  }, []);
  const logListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: logApi.getList,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.menu === 'all') {
      delete others.menu;
    }
    if (others.action === 'all') {
      delete others.action;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await logListRequest.run(queryPayload);
    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: result.data.list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const columns: IColumns[] = [
    {
      title: '全部对象',
      width: 120,
      key: 'menu',
      hideInTable: true,
      initialValue: 'all',
      valueType: 'drop',
      valueEnum: operateList,
    },
    {
      title: '全部类型',
      width: 120,
      key: 'action',
      hideInTable: true,
      initialValue: 'all',
      valueType: 'drop',
      valueEnum: typeList,
    },
    {
      title: '创建时间',
      key: 'range_time',
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '日志编号',
      dataIndex: 'id',
      key: 'id',
      render: (_, item: { id: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.id }}>
            {item.id}
          </Text>
        );
      },
    },
    {
      title: '操作人员',
      dataIndex: 'username',
      key: 'username',
      link: true,
      render: (value, item: any) => {
        return value;
      },
    },
    {
      title: '操作对象',
      dataIndex: 'menu',
      key: 'menu',
      render: (_, item: { menu: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.menu }}>
            {item.menu}
          </Text>
        );
      },
    },
    {
      title: '操作类型',
      dataIndex: 'action',
      key: 'action',
      render: (_, item: { action: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.action }}>
            {item.action}
          </Text>
        );
      },
    },
    {
      title: '操作内容',
      dataIndex: 'description',
      key: 'description',
      render: (_, item: { description: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.description }}>
            {item.description}
          </Text>
        );
      },
    },
    {
      title: '操作时间',
      dataIndex: 'time',
      key: 'time',
      render: (_, item: { time: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.time }}>
            {item.time}
          </Text>
        );
      },
    },
    {
      title: '操作结果',
      dataIndex: 'result',
      key: 'result',
      render: (_, item: { result: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.result }}>
            {item.result}
          </Text>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={logListRequest.loading}
        columns={columns}
        rowKey="id"
        request={requestTable}
        extend={null}
        batchRender={() => null}
        operator={null}
        searchTitle="操作日志"
      />
    </div>
  );
};
