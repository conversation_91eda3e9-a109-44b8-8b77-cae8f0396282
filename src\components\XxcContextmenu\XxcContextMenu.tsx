import { useMemoizedFn } from 'ahooks';
import { CONTEXT_MENU_ICONS, ContextMenuType, getMenuListKey } from './common';
import { ContextMenuData, useContextMenuStore } from '@/store/contextMenuStore';
import { useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import './XxcContextMenu.css';
import { closest } from '@/utils/common.util';

function XxcContextMenuItem(props: any) {
  const { data, onClick } = props;

  const clickMenu = useMemoizedFn((e: any) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    onClick?.(data);
  });
  const src = CONTEXT_MENU_ICONS[data.type as ContextMenuType];
  return (
    <div className="XxcContextMenuItem text-one-row" onClick={clickMenu}>
      {src ? <img src={src} className="XxcContextMenuItem-icon" /> : null}
      {data?.name}
    </div>
  );
}

export default function XxcContextMenu() {
  const menus = useContextMenuStore((state) => state.current.menus);
  const currentData = useContextMenuStore((state) => state.current.data);
  const menuKey = useContextMenuStore((state) => state.menuKey);
  const onToggleContextMenu = useContextMenuStore((state) =>
    state.getToggleCallback(state.menuKey),
  );
  const onContextMenuClick = useContextMenuStore((state) => state.getClickCallback(state.menuKey));
  const position = useContextMenuStore((state) => state.current.position);
  const visible = useContextMenuStore((state) => state.visible);
  const hideContextMenu = useContextMenuStore((state) => state.hideContextMenu);
  const showContextMenu = useContextMenuStore((state) => state.showContextMenu);

  const hideMenu = useMemoizedFn((e) => {
    const trigger = closest(e.target, '[data-contextmenu-key]');
    const dataText = trigger?.getAttribute('data-contextmenu-data') || '';
    let dataJson = {};
    try {
      dataJson = dataText ? JSON.parse(dataText) : {};
    } catch (error) {
      console.log(error);
    }
    if (trigger) {
      showContextMenu({
        menuKey: trigger.getAttribute('data-contextmenu-key') || '',
        data: dataJson,
        position: { x: e.pageX, y: e.pageY },
      });
      e.stopPropagation();
      e.preventDefault();
      e.stopImmediatePropagation();
      return;
    } else {
      hideContextMenu();
    }
  });

  const currentMenuList = useMemo(() => {
    return menus || [];
  }, [menus]);

  const clickMenu = useMemoizedFn((data: any) => {
    onContextMenuClick?.(data, currentData);
    setTimeout(() => {
      hideContextMenu();
    }, 100);
  });

  useEffect(() => {
    document.addEventListener('click', hideMenu);
    document.addEventListener('contextmenu', hideContextMenu);
    return () => {
      document.removeEventListener('click', hideMenu);
      document.removeEventListener('contextmenu', hideContextMenu);
    };
  }, [hideContextMenu]);

  useEffect(() => {
    if (visible) {
      onToggleContextMenu?.(visible, currentData);
    }
  }, [currentMenuList, visible, currentData]);

  const menuListKey = useMemo(() => getMenuListKey(menuKey), [menuKey]);

  if (!visible || !currentMenuList?.length) {
    return null;
  }

  return createPortal(
    <div
      className="XxcContextMenu"
      id={menuListKey}
      style={{ transform: `translate(${position.x}px, ${position.y}px)` }}
      onClick={(e) => {
        e.stopPropagation();
        e.nativeEvent.stopImmediatePropagation();
      }}
    >
      {currentMenuList.map((item: ContextMenuData, index: number) => {
        return (
          <XxcContextMenuItem key={index} data={item} onClick={clickMenu}></XxcContextMenuItem>
        );
      })}
    </div>,
    document.body,
  );
}
