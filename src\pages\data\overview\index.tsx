import React, { useEffect, useMemo, useState } from 'react';
import DashboardPage from './components/DashboardPage';
import { history, request } from '@umijs/max';
import { deviceAPI } from '@/api/device';
import { useModel } from '@umijs/max';
import dayjs from 'dayjs';
import { useMemoizedFn } from 'ahooks';
export default function Overview() {
  const gotoAnalysis = () => {
    history.push('/data/analysis');
  };

  const { setRegionDeviceList, defaultRegion, defaultRegionKey } = useModel('useDevice', (m) => ({
    setRegionDeviceList: m.setRegionDeviceList,
    defaultRegion: m.defaultRegion,
    defaultRegionKey: m.defaultRegionKey,
  }));

  useEffect(() => {
    request(deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setRegionDeviceList(list);
      }
    });
  }, []);

  const name = useMemo(() => {
    if (!defaultRegionKey) return '全国';
    const [province, city, district, street] = defaultRegionKey.split('.');
    return `${province}${city ? `-${city}` : ''}${district ? `-${district}` : ''}${street ? `-${street}` : ''}`;
  }, [defaultRegionKey]);

  const [pageData, setPageData] = useState({ totalCount: 0 });

  const updatePage = useMemoizedFn((data) => {
    setPageData(data);
  });

  return (
    <div className="flex-1 pb-[20px]">
      <div className="text-[20px] font-medium text-txt-main pt-[30px] pb-[20px] leading-[24px] flex justify-between">
        <span className="flex items-center gap-2">
          <span>
            {name || '全国'}近1个月蚊虫监测总数
            {pageData.totalCount > 0 ? `：${pageData.totalCount}(只)` : ''}
          </span>
          <span className="text-[12px] text-[#999] pt-[6px]">
            (截止到{dayjs().format('YYYY年MM月DD日')})
          </span>
        </span>
        <div onClick={gotoAnalysis} className=" cursor-pointer hidden">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      </div>
      <DashboardPage range={defaultRegion} updatePage={updatePage}></DashboardPage>
    </div>
  );
}
