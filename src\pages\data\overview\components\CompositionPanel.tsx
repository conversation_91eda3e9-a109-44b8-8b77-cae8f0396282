import React, { useEffect, useMemo } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { statisticsAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import { history } from '@umijs/max';
import useMosquitoModel from '@/models/useMosquito';
import { Spin } from 'antd';
import { formXLabelByTimeUnit } from '@/utils/time.util';
import { useRef } from 'react';

const CompositionPanel: React.FC<any> = (props) => {
  const { startTime, endTime, range, mosquitoType } = props;
  const queryRef = useRef<any>({});
  const {
    run: getSpeciesCompose,
    data: speciesComposeRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      queryRef.current = data;
      return {
        url: statisticsAPI.getSpeciesCompose,
        method: 'GET',
        params: { ...data, tenant_id: 1 },
        headers,
      };
    },
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    if (typeof range?.province === 'undefined') return;
    getSpeciesCompose({
      start_time: startTime,
      end_time: endTime,
      time_unit: 'day',
      ...range,
      monitor_type: mosquitoType,
    });
  }, [range, mosquitoType]);
  const { mosquitoList } = useMosquitoModel();
  const names = useMemo(() => {
    return mosquitoList.map((item: any) => item?.code?.toLowerCase());
  }, [mosquitoList]);

  const speciesCompose = useMemo(() => {
    const trendPoints = speciesComposeRes?.trend_points || [];
    const result = names
      .map((name: any) => {
        const list: any = trendPoints.map((point: any) => {
          const species = point.species || [];
          const mos = species.find((item: any) => item.monitor_type?.toLowerCase() === name);
          return {
            count: mos?.count || 0,
          };
        });
        return {
          name,
          data: list,
        };
      })
      .filter((item: any) => item.data.some((d: any) => d.count > 0));
    return result;
  }, [speciesComposeRes, names]);

  const option = useMemo(() => {
    const xLabels = speciesComposeRes?.trend_points.map((s: any) => {
      return formXLabelByTimeUnit(s.time_point, queryRef.current.time_unit);
    });
    const optData = {
      tooltip: { trigger: 'axis' },
      grid: { left: 70, right: 60, top: 60, bottom: 100 },
      legend: {
        bottom: 0,
        left: 'center',
      },
      xAxis: {
        type: 'category',
        name: '时间',
        nameLocation: 'end',
        nameTextStyle: { color: '#797A85' },
        data: xLabels,
        axisLabel: {
          color: '#797A85',
          overflow: 'truncate',
          ellipsis: '...',
          rotate: 0,
          margin: 20,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        name: '蚊虫数量：只',
        nameLocation: 'end',
        nameTextStyle: { color: '#797A85', align: 'center' },
        axisLabel: { color: '#797A85', rotate: 0, margin: 20 },
        splitLine: { lineStyle: { color: '#E4E6EB', type: 'dashed' } },
      },
      series: speciesCompose.map((s: any) => {
        return {
          name: getMosName(s.name),
          type: 'bar',
          stack: 'total',
          barGap: '10%',
          barWidth: '70%',
          data: s.data.map((item: { count: number }) => item.count),
          label: {
            color: '#333',
            position: 'center',
          },
          itemStyle: {
            color: getMosColor(s.name),
          },
        };
      }),
    };
    return optData;
  }, [speciesCompose]);

  const gotoAnalysis = () => {
    history.push('/data/analysis/region');
  };
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <span>蚊虫构成比</span>
          <div className="text-[12px] text-[#999]">（该模块展示的是蚊种构成随时间的变化趋势）</div>
        </span>
      }
      contentStyle={{
        overflow: 'visible',
        padding: '0',
      }}
      actions={
        <div onClick={gotoAnalysis} className=" cursor-pointer">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      }
    >
      <div className="w-full h-[325px] flex items-center justify-center">
        {loading ? <Spin /> : <EChartsCommon option={option} />}
      </div>
    </Panel>
  );
};

export default CompositionPanel;
