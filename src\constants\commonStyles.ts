/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 21:09:42
 * @LastEditTime: 2025-01-26 15:30:21
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const commonStyles = {
  baseBgColor: 'bg-[#F5F7FA]',
  lineBgColor: 'bg-[#EAECF0]',
  inputBgColor: 'bg-[#F4F4F6]',
  rounded: 'rounded-[44px]',
  rounded3: 'rounded-[66px]',
  rounded2: 'rounded-[88px]',
  lineTextColor: 'text-[#EAECF0]',
  inputTextColor: 'text-[#F4F4F6]',
  baseTextColor: 'text-txt-main',
  baseTextColor2: 'text-[#949BAA]',
  normalText14: 'text-[14px] font-normal',
  normalText16: 'text-[16px] font-normal',
  mediumText14: 'text-[14px] font-medium',
  mediumText16: 'text-[16px] font-medium',
  highlight: 'hover:text-txt-blue cursor-pointer',
  hightlightMedium: 'hover:text-txt-blue cursor-pointer hover:font-medium',
  hightlightBg: 'hover:bg-[#667085]/20 cursor-pointer',
  hightlightMenu: 'hover:font-semibold cursor-pointer',
  tableItemHighlight: 'hover:bg-[#F4F4F6] cursor-pointer',
  smoothScroll: 'scroll-smooth xc-scrollbar',
};
