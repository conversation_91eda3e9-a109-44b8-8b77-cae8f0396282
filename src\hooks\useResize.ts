import { useEffect } from 'react';

const useResize = (callback: () => void, deps: any[] = []) => {
  useEffect(() => {
    const handleResize = () => {
      // 当窗口大小改变时，调用传入的回调函数
      callback();
    };
    handleResize();
    // 添加 resize 事件监听器
    window.addEventListener('resize', handleResize);

    // 在组件卸载时移除事件监听器，防止内存泄漏
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [callback, ...deps]);
};

export default useResize;
