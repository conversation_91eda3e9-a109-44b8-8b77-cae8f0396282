import { ReactComponent as IconDevice } from '../../assets/device-icon.svg';
import { ReactComponent as IconHeat } from '../../assets/dd-heat-icon.svg';
import { ReactComponent as IconWinSpeed } from '../../assets/dd-winSpeed-icon.svg';
import { ReactComponent as IconDegree } from '../../assets/dd-degree-icon.svg';
import { ReactComponent as IconCo2 } from '../../assets/dd-co2-icon.svg';
import { ReactComponent as IconEws } from '../../assets/dd-ews-icon.svg';
import { Carousel, Drawer } from 'antd';
import '@/css/vw/DeviceDrawer.css';
import { useEffect, useMemo, useState } from 'react';
import DailyActivityLineChart from '../DailyActivityLineChart';
import SpeciesCompositionDonutChart from '../SpeciesCompositionDonutChart';
import RealTimeLog from '../RealTimeLog';
import { MosquitoAPI } from '@/api/mosquito';
import { request } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import { ewsAPI } from '@/api';

const LEVEL_MAP = {
  高: 'high',
  中: 'medium',
  低: 'low',
};
export default function DeviceDrawer(props: any) {
  const { onClose, device, monitorType } = props;
  const [open, setOpen] = useState(true);
  const [weather, setWeather] = useState({
    temperature: 0,
    humidity: 0,
    wind_speed: 0,
    co2: 0,
  });
  const [deviceDetail, setDeviceDetail] = useState({});
  const afterOpenChange = (visible: boolean) => {
    if (!visible) {
      onClose?.();
    }
  };

  const getDeviceDetail = useMemoizedFn(async () => {
    try {
      const list = await request(MosquitoAPI.getAuditList, {
        method: 'GET',
        params: {
          code: device.code,
          tenant_id: 1,
        },
      });
      const listData = list?.data?.list || [];
      const item = listData[0] || {};
      if (item.id) {
        setDeviceDetail(item);
        const result = await request(MosquitoAPI.getAuditDetail(item.id), {
          method: 'GET',
          params: {
            tenant_id: 1,
          },
        });
        setWeather(result?.data?.weather || {});
      }
    } catch (error) {
      setWeather({} as any);
      console.log(error);
    }
  });
  useEffect(() => {
    if (device?.code) {
      getDeviceDetail();
      setWeather({
        temperature: device.temp,
        humidity: device.hum,
        wind_speed: device.speed,
        co2: device.co2,
      });
    }
  }, [device]);

  const closeDrawer = () => {
    setOpen(false);
  };
  const address = useMemo(() => {
    const key = device.key || '';
    const province = key.split('.')[0];
    const city = key.split('.')[1];
    const district = key.split('.')[2];
    const street = key.split('.')[3];
    return `${province || ''}.${city || ''}.${district || ''}.${street || ''}`;
  }, [device]);
  const [ewsList, setEwsList] = useState([]);
  useEffect(() => {
    request(ewsAPI.getEwsList, {
      method: 'GET',
      params: {
        code: device.code,
        tenant_id: 1,
        page: 1,
        page_size: 10,
      },
    }).then((res) => {
      setEwsList(res?.data?.list || []);
    });
  }, []);
  const region = useMemo(() => {
    // console.log('device ', device)
    return {
      device_id: device.id,
      device_code: device.code,
    };
  }, [device]);
  return (
    <Drawer
      width={500}
      open={open}
      className="dd-drawer"
      footer={null}
      title={false}
      closable={false}
      onClose={closeDrawer}
      afterOpenChange={afterOpenChange}
      maskClosable
      styles={{
        mask: {
          background: 'rgba(0,0,0,0)',
        },
      }}
    >
      <div className="dd-drawer-content">
        <div className="dd-header">
          <div className="dd-title">
            <IconDevice />
            设备详情
          </div>
          {device.status === 1 && (
            <div className="dd-status">
              <div className="dd-dot"></div>
              监测中
            </div>
          )}
        </div>
        <div className="dd-content xc-scrollbar-none">
          <div className="dd-deviceInfo">
            <div className="dd-property ">设备名称：{device.name || '--'}</div>
            <div className="dd-property ">监测位置：{address || '--'}</div>
            <div className="dd-property ">生镜类型：{device.area_type || '--'}</div>
          </div>
          <div className="dd-envInfo">
            <div className="dd-envProperty dd-heat">
              <IconHeat />
              温度:{weather.temperature || '--'}°C
            </div>
            <div className="dd-envProperty dd-degree">
              <IconDegree />
              湿度:{weather.humidity || '--'}%
            </div>
            <div className="dd-envProperty dd-winSpeed">
              <IconWinSpeed />
              风速:{weather.wind_speed || '--'}m/s
            </div>
            <div className="dd-envProperty dd-co2">
              <IconCo2 />
              CO₂浓度:{weather.co2 || '--'}ppm
            </div>
          </div>
          <div className="dd-ewsList">
            <Carousel dots={false} vertical autoplay autoplaySpeed={3000}>
              {ewsList.map((item: any, index: number) => (
                <div className="dd-ewsItem" key={index}>
                  <div
                    className={`dd-ewsItemCont  is-${LEVEL_MAP[item.level as keyof typeof LEVEL_MAP] || 'low'}`}
                  >
                    <IconEws className="flex-shrink-0 size-[20px] "></IconEws>
                    <div className="dd-ewsText text-one-row">监测到{item.rule_name}</div>
                    <div className="flex-shrink-0 dd-ewsTime">{item.created_at}</div>
                  </div>
                </div>
              ))}
            </Carousel>
          </div>
          <SpeciesCompositionDonutChart
            region={region}
            monitorType={monitorType}
          ></SpeciesCompositionDonutChart>
          <DailyActivityLineChart region={region} monitorType={monitorType} />
          <RealTimeLog region={region} mosquitoType={monitorType} />
        </div>
      </div>
    </Drawer>
  );
}
