import { COLUMN_RENDER_TYPE, IColumns } from '@/components/XcTable/interface/search.interface';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import { useRef } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { logApi } from '@/api/log';
import { XcTableNew } from '@/components/XcTable/XcTableNew';
import { CustomTableBodyItemStatus } from '@/components/XcTable/XcTableBodyItemStatus';
import { DEVICE_STATUS } from '@/constants/common';
import XcAntdText from '@/components/XcAntdText';

export default () => {
  const logListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: logApi.getHealthLog,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.menu === 'all') {
      delete others.menu;
    }
    if (others.action === 'all') {
      delete others.action;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await logListRequest.run(queryPayload);

    if (result.code === 200) {
      let list = result.data.list || [];
      // 给每一行添加一个唯一的key
      list = list.map((item: any, index: number) => {
        return {
          ...item,
          __rowKey: item.id + '_' + index,
        };
      });
      return {
        total: result.data.total || 0,
        data: list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const columns: IColumns[] = [
    {
      title: '创建时间',
      key: 'range_time',
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '设备编号',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索设备编号',
      valueType: 'input',
    },
    {
      title: '设备编号',
      dataIndex: 'device_code',
      key: 'device_code',
      hideInSearch: true,
      width: 150,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      hideInSearch: true,
      width: 250,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '设备地址',
      dataIndex: 'province',
      key: 'province',
      hideInSearch: true,
      render: (value, item: any) => {
        return item.province + '-' + item.city + '-' + item.district + '-' + item.street;
      },
    },
    {
      title: '健康状态',
      dataIndex: 'health_status',
      key: 'health_status',
      hideInSearch: true,
      width: 120,
      render: (text) => {
        return (
          <XcAntdText>
            <span
              className={
                text === '警告'
                  ? 'text-sys-yellow'
                  : text === '异常'
                    ? ' text-sys-red'
                    : ' text-text-main'
              }
            >
              {text}
            </span>
          </XcAntdText>
        );
      },
    },
    {
      title: '设备问题',
      dataIndex: 'issues',
      key: 'issues',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '传感器/蚊虫/MQTT数据',
      dataIndex: 'mqttlog_count',
      key: 'mqttlog_count',
      hideInSearch: true,
      render: (value, item) => {
        return (
          <XcAntdText>
            {item.sensor_count}/{item.mosquito_count}/{item.mqttlog_count}
          </XcAntdText>
        );
      },
    },
    {
      title: '设备状态',
      dataIndex: 'device_status',
      key: 'device_status',
      hideInSearch: true,
      render: (value) => {
        return (
          <CustomTableBodyItemStatus
            status={value === 1 ? 'success' : 'default'}
            text={DEVICE_STATUS[value]}
          />
        );
      },
    },

    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      hideInSearch: true,
      width: 200,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
  ];
  const paginationRef = useRef({
    current: 1,
    defaultPageSize: 50,
    total: 0,
  });

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={logListRequest.loading}
        columns={columns}
        rowKey="__rowKey"
        request={requestTable}
        pagination={paginationRef.current}
        extend={null}
        batchRender={() => null}
        operator={null}
        rowSelection={null}
        searchTitle="设备健康日志"
      />
    </div>
  );
};
