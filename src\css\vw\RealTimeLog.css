.rtl-card-container {
  width: 100%;
  margin-top: 40px;
}

.rtl-title-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rtl-title-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.rtl-title-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 25px;
  color: #fff;
}

.rtl-content {
  margin: 16px auto 20px;
}

.rtl-item-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.50);
  min-height: 200px;
}

.rtl-item {
  padding-bottom: 6px;
}

.rtl-item-box {
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  border-radius: 8px;
  padding-right: 10px;
  border: 1px solid rgba(56, 119, 212, 0.21);
  background: rgba(20, 47, 88, 0.28);
}

.rtl-item-time-icon {
  width: 16px;
  height: 16px;
  color: #4C84FF;
  margin-right: 6px;
}

.rtl-item-time {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.rtl-item-content,
.rtl-item-time {
  font-weight: 500;
  font-size: 14px;
  line-height: 25px;
  color: #fff;
}
.rtl-item-content{
  width: 140px;
  text-align: left;
}
