/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:39:09
 * @LastEditTime: 2025-02-08 14:55:37
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import { BigScreenAPI } from '@/api';
import { formatNumberWithCommas } from '@/utils/number.util';
import { useRequest } from '@umijs/max';
import { forwardRef, memo, useEffect, useImperativeHandle, useState } from 'react';
import { SvgDeviceMonitor } from '../icon/SvgDeviceMonitor';
import { SvgDeviceMonitorPercent } from '../icon/SvgDeviceMonitorPercent';
import { SvgDeviceTotal } from '../icon/SvgDeviceTotal';
import { SvgDeviceUnMonitor } from '../icon/SvgDeviceUnMonitor';

interface IProps {
  className?: string;
  title?: string;
  value?: string;
  icon?: React.ReactNode;
}

const CustomTableDataItem = (props: IProps) => {
  const { className, title, value, icon } = props;
  return (
    <div
      className={`shrink-0 p-[20px] h-[122px] rounded-[16px] flex justify-between items-center ${className}`}
    >
      <div className="flex flex-col">
        <p className="font-normal text-[14px] text-txt-sub">{title}</p>
        <p className="font-bold text-[28px] text-[#202224]">{value}</p>
      </div>
      {icon}
    </div>
  );
};

export const StatData = memo(
  forwardRef((_: any, ref: any) => {
    const [stats, setStats] = useState({
      offline_devices: 0,
      online_devices: 0,
      online_rate: 0,
      total_devices: 0,
    });

    /**
     * 概览请求接口
     */
    const deviceOverviewRequest = useRequest(
      (data?: any, headers?: any) => ({
        url: BigScreenAPI.deviceOverview,
        params: { ...data, tenant_id: 1 },
        headers,
      }),
      {
        manual: true,
        formatResult: (r) => r,
        initialData: { result: { total: 0, onlineDevices: 0, offlineDevices: 0 } },
        throwOnError: true,
      },
    );

    useImperativeHandle(ref, () => {
      return {
        refresh: () => {
          deviceOverviewRequest.run();
        },
      };
    });

    useEffect(() => {
      if (deviceOverviewRequest?.data.data) {
        const { offline_devices, online_devices, online_rate, total_devices } =
          deviceOverviewRequest?.data.data;
        setStats({ offline_devices, online_devices, online_rate, total_devices });
      }
    }, [deviceOverviewRequest?.data.data]);

    useEffect(() => {
      deviceOverviewRequest.run();
    }, []);

    return (
      <div className="grid gap-[20px] grid-cols-4 grid-rows-1  h-[122px] mb-[16px]">
        <CustomTableDataItem
          className="bg-[#C8C6FF]/25"
          title="设备总数"
          value={formatNumberWithCommas(stats?.total_devices)}
          icon={<SvgDeviceTotal className="size-[62px]" />}
        />
        <CustomTableDataItem
          className="bg-[#FCE4A8]/30"
          title="监测中设备占比"
          value={`${stats?.online_rate ? stats?.online_rate.toFixed(2) : 0}%`}
          icon={<SvgDeviceMonitorPercent className="size-[62px]" />}
        />
        <CustomTableDataItem
          className="bg-[#ADEDCE]/30"
          title="监测中设备数"
          value={formatNumberWithCommas(stats?.online_devices)}
          icon={<SvgDeviceMonitor className="size-[62px]" />}
        />
        <CustomTableDataItem
          className="bg-[#FEBFA7]/20"
          title="未监测设备数"
          value={formatNumberWithCommas(stats?.offline_devices)}
          icon={<SvgDeviceUnMonitor className="size-[62px]" />}
        />
      </div>
    );
  }),
);
