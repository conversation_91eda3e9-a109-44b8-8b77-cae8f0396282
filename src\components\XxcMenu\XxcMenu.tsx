import { useLayoutStore } from '@/store';
import { useAppData, useLocation, history } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useMemo, useState } from 'react';
import { MENU_ICONS } from './constant';
import classNames from 'classnames';
import { ReactComponent as IconArrowDown } from './assets/arrow.svg';
import { ALL_ROUTES } from '../../../config/routes';
import { filterRoutesWithPermission } from '@/utils/permission';
import './XxcMenu.css';

const filterMenus = (routes: any) => {
  const flatRoutes = [] as any;
  Object.keys(routes).forEach((key) => {
    const route = routes[key];
    flatRoutes.push({ ...route });
  });
  return flatRoutes;
};

function XxcMenuItem(props: any) {
  const { onClick, data, className } = props;
  const { path, icon, name } = data || {};
  const normalIcon = MENU_ICONS[icon as keyof typeof MENU_ICONS] || '';
  const activeIcon = MENU_ICONS[`${icon}Active` as keyof typeof MENU_ICONS] || '';
  const [active, setActive] = useState(false);
  const [hover, setHover] = useState(false);
  const [expand, setExpand] = useState(false);

  const expandMenu = useMemoizedFn((e) => {
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setExpand(!expand);
  });

  useEffect(() => {
    if (location?.pathname.indexOf(path) > -1) {
      setActive(true);
      setExpand(true);
    } else {
      setActive(false);
    }
  }, [path, location?.pathname]);

  const onMenuClick = useMemoizedFn(() => {
    onClick(path);
  });

  const styleActive = hover || active;

  return (
    <div
      className={classNames(className, {
        'is-selected': active,
        'is-hover': hover,
        'is-expand': expand,
      })}
      data-path={path}
    >
      <div
        className="XxcMenuItem-cont"
        onClick={onMenuClick}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
      >
        {icon ? (
          <img src={styleActive ? activeIcon : normalIcon} className="XxcMenuItem-icon" />
        ) : null}
        {name}
        {data?.routes?.length > 0 ? (
          <IconArrowDown className="XxcMenuItem-arrow" onClick={expandMenu} />
        ) : null}
      </div>
      {data?.routes?.length > 0 ? (
        <div className="XxcMenuItem-children">
          {data?.routes?.map((item: any, index: number) => {
            return (
              <XxcMenuItem
                key={index}
                onClick={onClick}
                data={item}
                className={'XxcMenuItem-sub'}
              />
            );
          })}
        </div>
      ) : null}
    </div>
  );
}
/**
 * 过滤掉 hide: true 的路由项，并递归处理子路由
 */
function filterHiddenRoutes(routes: any[]): any {
  return routes
    .filter((route: any) => !route.hide) // 先过滤当前层级中 hide: true 的项
    .map((route: any) => {
      const { routes, ...rest } = route;

      // 如果有子路由，递归处理
      if (routes && routes.length > 0) {
        const filteredChildren = filterHiddenRoutes(routes);

        // 如果子路由被全部过滤掉了，就不返回 routes 字段
        if (filteredChildren.length > 0) {
          return {
            ...rest,
            routes: filteredChildren,
          };
        }
      }

      return rest;
    });
}
export default function XxcMenu() {
  const setSelectedMenu = useLayoutStore((state) => state.setSelectedMenu);
  const { routes } = useAppData();
  const location = useLocation();
  // 菜单点击
  const onSelectMenu = (path: string) => {
    history.push(path);
  };
  const flatRoutes = useMemo(() => {
    return filterMenus(routes);
  }, [routes]);

  useEffect(() => {
    const selected = flatRoutes.find((menu: { path: string }) => {
      if (location?.pathname !== '/') {
        const isCurrent = menu.path.indexOf(location?.pathname) === 0;
        return isCurrent;
      }
      return false;
    });
    setSelectedMenu(selected || '');
  }, [location?.pathname]);

  const renderRoutes = useMemo(() => {
    const list = filterHiddenRoutes(ALL_ROUTES);
    const authMenus = filterRoutesWithPermission(list);
    return authMenus;
  }, [ALL_ROUTES]);

  // console.log('renderRoutes', renderRoutes);
  return (
    <div className="XxcMenu-cateBox">
      {renderRoutes.map((menu: any, idx: number) => {
        return (
          <div key={idx} className="XxcMenu-cate">
            <div className="XxcMenu-cateTitle">{menu?.name}</div>
            <div className="XxcMenu-cateSplit" />
            <div className="XxcMenu">
              {menu?.routes?.map((item: any, index: number) => {
                return (
                  <XxcMenuItem
                    key={index}
                    onClick={onSelectMenu}
                    data={item}
                    className="XxcMenuItem"
                  />
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
}
