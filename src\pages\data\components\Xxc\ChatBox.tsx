/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-30 13:37:31
 * @LastEditTime: 2025-02-08 11:19:27
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { IconNavigationPointer } from '@/components/Icon/IconNavigationPointer';
import { IconNavigationPointerActive } from '@/components/Icon/IconNavigationPointerActive';
import { IconPhoto2 } from '@/components/Icon/IconPhoto2';
import { px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { IconClose } from '@/components/XcChatBox/icons/IconClose';
import { styled } from '@umijs/max';
import { ConfigProvider, Image, Input, Upload } from 'antd';
import { memo } from 'react';
import { useXxc } from '../../../../components/XcChatBox/useXxc';
import { DefaultChat } from './DefaultChat';
import { MessageBox } from './MessageBox';

const { TextArea } = Input;

const StyledContainer = styled.div`
  padding: 0 ${() => px2vw(30)} ${() => px2vh(30)};
`;
const StyledContent = styled.div<{ $upload: boolean }>`
  /* 有图片60px + 30px + 120px + 65px */
  /* 无图片60px + 30px + 120px */
  width: 100%;
  height: ${(props) => (props.$upload ? 'calc(100vh - 275px)' : 'calc(100vh - 210px)')};
`;
const StyledDefaultChat = styled(DefaultChat)`
  margin-top: ${() => px2vh(160)};
`;
const StyledInputContainer = styled.div<{ $upload: boolean }>`
  left: ${() => px2vw(30)};
  right: ${() => px2vw(30)};
  bottom: ${() => px2vh(30)};
  height: ${(props) => (props.$upload ? px2vh(185) : px2vh(120))};

  .xc-xxc-operator {
    bottom: ${() => px2vh(10)};
    right: ${() => px2vw(20)};
    .xc-xxc-operator-upload {
      margin-top: ${() => px2vh(4)};
      .xc-svg {
        width: ${() => px2vw(32)};
        height: ${() => px2vh(28)};
      }
    }
    .xc-xxc-operator-send {
      margin-left: ${() => px2vh(20)};
      .xc-svg {
        width: ${() => px2vw(32)};
        height: ${() => px2vh(32)};
      }
    }
  }
`;

export const ChatBox = memo(() => {
  const {
    messageList,
    currentTime,
    uniqueId,
    onSendMsg,
    sendMsg,
    beforeUpload,
    qa,
    onChangeText,
    onPressEnter,
    imageUrl,
    setImgUrl,
  } = useXxc();

  return (
    <StyledContainer className="overflow-x-hidden relative w-[100%] h-[100%]">
      {/* message */}
      <StyledContent $upload={!!imageUrl}>
        {messageList.length === 0 ? (
          <StyledDefaultChat />
        ) : (
          <MessageBox uniqueId={uniqueId} messageList={messageList} currentTime={currentTime} />
        )}
      </StyledContent>
      {/* 输入框 */}
      <StyledInputContainer $upload={!!imageUrl} className={`absolute flex flex-col items-center `}>
        <ConfigProvider
          theme={{
            components: {
              Input: {
                colorTextPlaceholder: '#999999',
                colorBgContainer: '#292933',
                colorText: '#ffffff',
              },
            },
          }}
        >
          <TextArea
            value={sendMsg}
            autoFocus={true}
            onChange={onChangeText}
            placeholder="输入您想了解的内容，Enter发送"
            disabled={qa === 'A'}
            classNames={{
              textarea: `!h-[100%]`,
            }}
            style={{
              paddingTop: `${imageUrl ? '88px' : '20px'}`,
            }}
            onPressEnter={onPressEnter}
          ></TextArea>
        </ConfigProvider>
        {/* 图片位置 */}
        {imageUrl && (
          <div className="absolute left-[20px] top-[20px]">
            <div className="relative size-[54px]">
              <IconClose
                className="absolute top-[-5px] right-[-5px] z-[99]"
                onClick={() => {
                  setImgUrl(undefined);
                }}
              />
              <Image
                width={54}
                height={54}
                src={imageUrl}
                style={{ objectFit: 'cover', borderRadius: 10, border: '0.5px solid #000000' }}
              />
            </div>
          </div>
        )}
        <div className="xc-xxc-operator absolute flex flex-row justify-center items-center">
          <div className="xc-xxc-operator-upload flex flex-col items-center justify-center ">
            <Upload
              disabled={qa === 'A'}
              beforeUpload={beforeUpload}
              maxCount={1}
              showUploadList={false}
              accept=".png, .jpg, .jpeg"
            >
              <IconPhoto2 />
            </Upload>
          </div>
          <div
            className={`xc-xxc-operator-send flex justify-center items-center `}
            onClick={onSendMsg}
          >
            {sendMsg.length > 0 ? <IconNavigationPointerActive /> : <IconNavigationPointer />}
          </div>
        </div>
      </StyledInputContainer>
    </StyledContainer>
  );
});
