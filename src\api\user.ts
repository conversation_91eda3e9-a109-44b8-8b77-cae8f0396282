/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 12:40:22
 * @LastEditTime: 2024-12-27 15:19:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const userApi = {
  login: '/v1/auth/login',
  getList: '/v1/user/list',
  postUser: '/v1/user',
  getCaptcha: '/v1/user/getCaptcha',
  deleteUser: (id: string) => `/v1/user/${id}`,
  getUser: (id: string) => `/v1/user/${id}`,
  putUser: (id: string) => `/v1/user/${id}`,
  postResetPassword: '/v1/user/batch/reset-password',
  postUpdateStatus: '/v1/user/batch/update-status',
  postUsersRoles: '/v1/relation/users/roles',
  postUsersOrgs: '/v1/relation/users/org',
  postPasswordReset: '/v1/auth/password/reset', // 修改密码
  postSendResetCode: '/v1/auth/password/send-reset-code', // 发送重置密码验证码
};
