.Login {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: url(@/assets/user/login-bg.png) no-repeat center center/2248px 1264px;

  .ant-form-item {
    height: 94px !important;
    margin-bottom: 0 !important;
  }

  .Login-logo {
    position: absolute;
    top: 60px;
    left: 100px;
    width: 176px;
    height: 39px;
  }

  .LoginForm {
    position: absolute;
    right: 200px;
    top: 50%;
    transform: translateY(-50%);
    width: 600px;
    height: 670px;
    border-radius: 36px;
    backdrop-filter: blur(10px);
    border: 1px solid #FFFFFF;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding-top: 60px;
    background: rgba(255, 255, 255, .3);

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url(@/assets/user/form-bg.png) no-repeat center center/100% 100%;
      opacity: 0.3;
      z-index: -1;
    }
  }


  .LoginForm-name {
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: url(@/assets/user/name.png) no-repeat center center;
    background-size: contain;
    width: 100%;
  }

  .LoginForm-desc {
    margin-top: 12px;
    font-size: 20px;
    color: #84909F;
    display: flex;
    justify-content: center;
  }

  .LoginForm-tabs {
    display: flex;
    justify-content: space-between;
    background: rgba(239, 245, 255);
    border-radius: 25px;
    padding: 4px;
    height: 50px;
    width: 244px;
    margin: 32px auto 0;
    opacity: 0;
    pointer-events: none;
  }

  .LoginForm-tabItem {
    width: 112px;
    height: 42px;
    line-height: 42px;
    text-align: center;
    font-size: 20px;
    cursor: pointer;

    &.is-active {
      color: #313747;
      background-color: #fff;
      border-radius: 21px;
    }
  }

  .LoginFormBox {
    margin-top: 32px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    width: 484px;
  }


  .LoginForm-account,
  .LoginForm-password,
  .LoginForm-captcha,
  .LoginForm-mobile {
    width: 484px;
    height: 70px;
    border-radius: 35px;
    background: #fff;
    font-size: 20px;
    padding: 0px 12px 0px 32px;
  }


  .LoginForm-submit {
    width: 484px;
    height: 70px;
    border-radius: 35px;
    color: #fff;
    font-size: 24px;
    margin-top: 100px;
    cursor: default;
    background: linear-gradient(98deg, #44B1FF 4%, #6F5DFF 81%);
    opacity: 0.4;
    cursor: not-allowed;

    &.is-active {
      opacity: 1;
      cursor: pointer;
    }
  }

  .LoginForm-captchaBtn {
    width: 112px;
    height: 46px;
    border-radius: 23px;
    background: #6265FF;
    color: #fff;
    font-size: 16px;
    pointer-events: none;
    opacity: 0.5;
    border: none !important;

    &.is-active {
      opacity: 1;
      pointer-events: auto;
      cursor: pointer;
    }
  }
}


