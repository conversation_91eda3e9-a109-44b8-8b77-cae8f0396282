/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:35:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconTrend2 = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#FDA81D" />
      <g clipPath="url(#clip0_118_673)">
        <path
          d="M16.5 16.5H8.3C8.01997 16.5 7.87996 16.5 7.773 16.4455C7.67892 16.3976 7.60243 16.3211 7.5545 16.227C7.5 16.12 7.5 15.98 7.5 15.7V7.5M16.5 9.5L13.7828 12.2172C13.6838 12.3162 13.6343 12.3657 13.5773 12.3842C13.527 12.4005 13.473 12.4005 13.4227 12.3842C13.3657 12.3657 13.3162 12.3162 13.2172 12.2172L12.2828 11.2828C12.1838 11.1838 12.1343 11.1343 12.0773 11.1158C12.027 11.0995 11.973 11.0995 11.9227 11.1158C11.8657 11.1343 11.8162 11.1838 11.7172 11.2828L9.5 13.5M16.5 9.5H14.5M16.5 9.5V11.5"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};
