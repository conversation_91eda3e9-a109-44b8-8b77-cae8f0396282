/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-19 14:22:22
 * @LastEditTime: 2025-02-13 12:38:51
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { IRegionDevice, IRegionDeviceList } from '@/interface/regionDevice';
import { useMemoizedFn } from 'ahooks';
import { useEffect, useState } from 'react';
const useDeviceModel = () => {
  const [currentDevice, setCurrentDevice] = useState<any>();
  const [deviceModalVisible, setDeviceModalVisible] = useState(false);
  // 区域设备列表
  const [regionDeviceList, setRegionDeviceList] = useState<IRegionDeviceList[]>([]);
  // 按区域分类设备
  const [provinceDeviceList, setProvinceDeviceList] = useState<Record<string, IRegionDevice[]>>({});
  const [cityDeviceList, setCityDeviceList] = useState<Record<string, IRegionDevice[]>>({});
  const [districtDeviceList, setDistrictDeviceList] = useState<Record<string, IRegionDevice[]>>({});
  const [streetDeviceList, setStreetDeviceList] = useState<Record<string, IRegionDevice[]>>({});
  const [defaultRegionKey, setDefaultRegionKey] = useState('');
  const [defaultRegion, setDefaultRegion] = useState<any>({});
  const [regionLoad, setRegionLoad] = useState<any>(false);

  const buildRegionDeviceMap = useMemoizedFn((regions: IRegionDeviceList[]) => {
    const provinceMap: Record<string, any> = {};
    const cityMap: Record<string, any> = {};
    const districtMap: Record<string, any> = {};
    const streetMap: Record<string, any> = {};

    // 递归遍历节点，构建路径并收集街道设备
    function processNode(node: IRegionDeviceList, parentPath: string[]) {
      // 当前路径 = 父路径 + 当前节点名称
      const currentPath = [...parentPath, node.name];

      // 如果是街道节点，将设备存入map
      let key = currentPath.join('.');

      let curMap = provinceMap;
      if (node.level === 'province') {
        // key = `province.city.district.street`
        key = `${key}...`;
        curMap = provinceMap;
      } else if (node.level === 'city') {
        key = `${key}..`;
        curMap = cityMap;
      } else if (node.level === 'district') {
        key = `${key}.`;
        curMap = districtMap;
      } else {
        key = `${key}`;
        curMap = streetMap;
      }

      if (node.devices?.length > 0) {
        curMap[key] = node.devices.map((item) => ({
          key: key + '.' + item.id,
          ...item,
        }));
        return;
      }
      if (node.children) {
        // 非街道节点：递归处理子节点
        for (const child of node.children) {
          processNode(child, currentPath);
        }
      }
    }

    // 从根节点（省份）开始遍历
    regions.forEach((region: any) => processNode(region, []));

    Object.keys(streetMap).forEach((streetKey) => {
      const [province, city, district] = streetKey.split('.');
      const districtKey = `${province}.${city}.${district}`;
      if (!districtMap[districtKey]) {
        districtMap[districtKey] = [];
      }
      districtMap[districtKey].push(...streetMap[streetKey]);
    });
    Object.keys(districtMap).forEach((districtKey) => {
      const [province, city] = districtKey.split('.');
      const cityKey = `${province}.${city}`;
      if (!cityMap[cityKey]) {
        cityMap[cityKey] = [];
      }
      cityMap[cityKey].push(...districtMap[districtKey]);
    });
    Object.keys(cityMap).forEach((cityKey) => {
      const [province] = cityKey.split('.');
      if (!provinceMap[province]) {
        provinceMap[province] = [];
      }
      provinceMap[province].push(...cityMap[cityKey]);
    });

    return [provinceMap, cityMap, districtMap, streetMap];
  });
  // 分类设备
  useEffect(() => {
    if (!regionDeviceList?.length) return;
    const [provinceMap, cityMap, districtMap, streetMap] = buildRegionDeviceMap(regionDeviceList);
    // console.log(provinceMap, cityMap, districtMap, streetMap);
    let defaultRegionKey = '';
    if (Object.keys(streetMap).length === 1) {
      defaultRegionKey = Object.keys(streetMap)[0];
    } else if (Object.keys(districtMap).length === 1) {
      defaultRegionKey = Object.keys(districtMap)[0];
    } else if (Object.keys(cityMap).length === 1) {
      defaultRegionKey = Object.keys(cityMap)[0];
    } else if (Object.keys(provinceMap).length === 1) {
      defaultRegionKey = Object.keys(provinceMap)[0];
      // const list = Object.entries(provinceMap)
      // list.sort((a, b) => {
      //   return b[1]?.length - a[1]?.length;
      // });
      // defaultRegionKey = list[0][0];
    }

    const [province, city, district, street] = defaultRegionKey.split('.');
    setDefaultRegion({
      province,
      city,
      district,
      street,
    });

    setProvinceDeviceList(provinceMap);
    setCityDeviceList(cityMap);
    setDistrictDeviceList(districtMap);
    setStreetDeviceList(streetMap);
    setDefaultRegionKey(defaultRegionKey);
    setRegionLoad(true);
  }, [regionDeviceList]);

  useEffect(() => {
    return () => {
      setDeviceModalVisible(false);
    };
  }, []);

  return {
    deviceModalVisible,
    setDeviceModalVisible,
    currentDevice,
    setCurrentDevice,
    streetDeviceList,
    setStreetDeviceList,
    districtDeviceList,
    setDistrictDeviceList,
    cityDeviceList,
    setCityDeviceList,
    provinceDeviceList,
    setProvinceDeviceList,
    regionDeviceList,
    setRegionDeviceList,
    defaultRegionKey,
    setDefaultRegionKey,
    defaultRegion,
    setDefaultRegion,
    regionLoad,
  };
};
export default useDeviceModel;
