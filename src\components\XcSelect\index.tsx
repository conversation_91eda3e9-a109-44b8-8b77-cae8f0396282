import { styled } from '@umijs/max';
import { Select, SelectProps } from 'antd';
import { IconDrop } from '../Icon/IconDrop';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-29 23:09:02
 * @LastEditTime: 2025-02-02 22:56:14
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const StyledSelect = styled(Select)`
  height: 40px;
  > .ant-select-selector {
    width: 120px !important;
    height: 40px !important;
    border-radius: 8px !important;
    background: #f4f4f6 !important;
    border: 0 !important;
    display: flex;
    text-align: center;
    justify-content: center;
    gap: 8px;
    padding: 0 !important;
    padding-right: 32px !important;
  }

  .ant-select-selection-item {
    color: #36383a !important;
    font-family: 'PingFang SC';
    font-style: normal !important;
    line-height: 20px !important;
    font-size: 14px !important;
    padding-left: 16px !important;
    padding-inline-end: 0 !important;
    text-align: left !important;
  }
  .ant-select-arrow {
    height: 40px;
    margin-top: 0;
    top: 0;
    right: 0;
    inset-inline-end: 16px;
  }
  .ant-select-selection-placeholder {
    color: #36383a;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    line-height: 20px;
  }
`;

export const XcSelect = (props: SelectProps) => {
  const { defaultValue, placeholder, onSelect, options, ...others } = props;
  return (
    <StyledSelect
      defaultValue={defaultValue}
      onSelect={onSelect}
      suffixIcon={<IconDrop className="size-[24px]" />}
      options={options}
      placeholder={placeholder}
      {...others}
    />
  );
};
