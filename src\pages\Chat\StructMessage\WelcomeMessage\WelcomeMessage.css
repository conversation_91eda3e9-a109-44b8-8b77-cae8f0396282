.WelcomeMessage {
  width: 844px;
  border-radius: 16px;
}

.WelcomeMessage-head {
  display: flex;
  height: 82px;
  width: 100%;
  background: url(./bg-welcome-top.png) no-repeat top 0 right 0/798px 82px;
  overflow: hidden;
}

.WelcomeMessage-headAvatar {
  position: relative;
  margin-left: 13px;
  width: 90px;
  height: 90px;
  background: url(../../assets/avatar-robot.png) no-repeat 0 0/100% 100%;
  z-index: 3;
}

.WelcomeMessage-headText {
  margin-top: 9px;
  font-size: 26px;
  line-height: 44px;
  font-weight: bold;
  color: #FFFFFF;
}

.WelcomeMessage-cont {
  padding: 25px 20px;
  border-radius: 16px;
}

.WelcomeMessage-title {
  height: 36px;
  display: flex;
  align-items: center;
  color: #313747;
  font-size: 20px;
}


.WelcomeMessage-icon {
  width: 36px;
  height: 36px;
  margin-right: 4px;

  &.is-weather {
    background: url(../../assets/icon-weather.png) no-repeat 0 0/100% 100%;
  }

  &.is-charts {
    background: url(../../assets/icon-charts.png) no-repeat 0 0/100% 100%;
  }
}

.WelcomeMessage-weatherContent {
  margin-top: 10px;
  display: flex;
}

.WelcomeMessage-cont {
  position: relative;
  margin-top: -20px;
  background-color: #fff;
}

.WelcomeMessage-weatherContent {
  display: flex;
  height: 86px;
}

.WelcomeMessage-weatherDetail {
  position: relative;
  flex: 1;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  border-radius: 8px;
  color: #333;
  background: linear-gradient(to right, #EEFBFF, #C1D1FF);
}

.WelcomeMessage-weatherImg {
  position: absolute;
  top: 8px;
  right: 24px;
  width: 92px;
  height: 70px;
  background-size: contain;
  background-position: top right;
}

.WelcomeMessage-weatherDate {
  margin-left: 24px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #333;
}

.WelcomeMessage-weatherDay {
  font-size: 44px;
  line-height: 50px;
  font-weight: bold;
  margin-top: -6px;
}

.WelcomeMessage-weatherHoliday {
  font-size: 14px;
  line-height: 18px;
  text-align: center;
}

.WelcomeMessage-weatherInfo {
  margin-left: 6px;
  line-height: 18px;
  font-size: 14px;
}

.WelcomeMessage-weatherTemp {
  line-height: 18px;
}

.WelcomeMessage-weatherText {
  font-weight: bold;
  margin-top: 4px;
  font-size: 14px;
  line-height: 18px;
}

.WelcomeMessage-weatherAir {
  font-family: Alibaba PuHuiTi 3.0;
  margin-top: 4px;
}


.WelcomeMessage-weatherAiList {
  flex: 1;
  margin-left: 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 0;
}


.WelcomeMessage-overview {
  margin-top: 25px;
}

.WelcomeMessage-overviewList {
  display: flex;
  gap: 16px;
  height: 256px;
  margin-top: 10px;

  .ChartMessage {
    flex: 1;
    height: 100%;
  }
}

.WelcomeMessage-ewsList {
  margin-top: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px 16px;

  .WelcomeMessage-ewsItem {
    overflow: hidden;
  }
}
