const path = require('path');
const vwPath = path.join(__dirname, 'src', 'css', 'vw');
module.exports = (ctx) => {
  const isVwFolder = ctx.file && ctx.file.indexOf(vwPath) !== -1;
  // console.log('1111111111111111111', ctx.file);
  // const isVwFolder = true;
  return {
    plugins: {
      'postcss-px-to-viewport': isVwFolder
        ? {
            viewportWidth: 1920,
            unitPrecision: 6,
            viewportUnit: 'vw',
            selectorBlackList: [],
            minPixelValue: 1,
            mediaQuery: false,
          }
        : false,
    },
  };
};
