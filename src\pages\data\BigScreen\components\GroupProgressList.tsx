import '@/css/vw/GroupProgressList.css';
import { ReactComponent as RegionMosIcon } from '../../analysis/assets/region-mos.svg';
import { useModel, useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { useEffect } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';

export default function GroupProgressList(props: any) {
  const { startTime, endTime, monitorType } = props;
  const { region } = useModel('useMenu', (m) => ({
    region: m.region,
  }));
  const { run: getHabitatRatio, data: habitatRatioRes } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getHabitatRatio,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    const { device_id, ...rest } = region;
    if (!region?.province && !device_id) {
      return;
    }
    const [startTime1, endTime1] = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
    getHabitatRatio({
      top_n: 5,
      start_time: startTime || startTime1,
      end_time: endTime || endTime1,
      ...rest,
      dev_ids: device_id,
      monitor_type: monitorType,
    });
  }, [region, monitorType]);

  const habitatRatio = habitatRatioRes?.habitats || [];
  return (
    <div className="gpl-card-container">
      <div className="gpl-header">
        <div className="gpl-header-title">
          <RegionMosIcon className="text-[#735CFE]" />
          近7天蚊虫高发生境
        </div>
      </div>
      <div className="gpl-split-line"></div>
      <div className="gpl-list">
        {habitatRatio.length > 0 ? (
          habitatRatio.map((item: any, idx: any) => (
            <div className="gpl-list-row" key={idx}>
              <div className="gpl-index-badge">
                <span>{idx + 1}</span>
              </div>
              <div className="gpl-group-name text-one-row">{item.area_type || '--'}</div>
              <div className="gpl-progress-bar-wrap">
                <div className="gpl-progress-bar-bg">
                  <div className="gpl-progress-bar-fg" style={{ width: `${item.ratio}%` }} />
                </div>
                <span className="gpl-progress-value">{item.count}</span>
              </div>
            </div>
          ))
        ) : (
          <div className="gpl-list-row-empty">暂无数据</div>
        )}
      </div>
    </div>
  );
}
