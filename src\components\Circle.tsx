/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 22:56:02
 * @LastEditTime: 2024-12-23 23:10:07
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { memo } from 'react';

interface IProps {
  className?: string;
  children?: React.ReactNode;
}

export const Circle = memo((props: IProps) => {
  const { children, className } = props;
  return (
    <div className={`${className} rounded-full flex flex-col items-center justify-center`}>
      {children}
    </div>
  );
});
