/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-05 16:28:48
 * @LastEditTime: 2025-02-09 16:09:32
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import imgXxx2 from '@/assets/img_xxc2.png';
import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { DEFAULT_IMAGE } from '@/constants/common';
import { isBaseImg } from '@/utils/file.util';
import { styled } from '@umijs/max';
import { Image, Spin } from 'antd';
import { memo, useCallback, useState } from 'react';
import Markdown from 'react-markdown';
import { IMessage } from './xxc.interface';

interface IProps {
  uniqueId: string;
  messageList: IMessage[];
  currentTime: string;
}

const StyledTitle = styled.p`
  margin-top: ${() => px2vh(60)};
`;
const StyledMessageItem = styled.div`
  gap: ${() => px2vw(10)};
  margin-bottom: ${() => px2vh(20)};
`;
const StyledUploadImg = styled(Image)`
  width: ${() => px2vw(140)};
  height: ${() => px2vh(140)};
`;
const StyledLeftPerson = styled.div`
  top: -${() => px2vh(35)};
  .xxc-name {
    margin-bottom: ${() => px2vh(20)};
  }
  .xxc-avatar {
    width: ${() => px2vw(50)};
    height: ${() => px2vh(50)};
  }
`;
const StyledLeftMessage = styled.div`
  margin-left: ${() => px2vw(70)};
  padding: ${() => px2vh(10)} ${() => px2vw(10)};
  border-radius: ${() => px2font(8)};
`;
const StyledRightMessage = styled.div`
  padding: ${() => px2vh(10)} ${() => px2vw(10)};
  border-radius: ${() => px2font(8)};
`;

const styles = {
  messageContainer: 'w-[100%] h-5/6',
  messages: `h-[100%] overflow-y-auto scroll-smooth  xc-scrollbar text-white`,
  personLeftHeader: 'absolute left-[0px] fade-in clear-both',
  personLeftMsg: `fade-in bg-[#535354] w-max max-w-[50%] clear-both`,
  personRightHeader: 'fade-in ml-auto clear-both ',
  personRightMsg: `fade-in bg-[#535354] w-max max-w-[50%] clear-both ml-auto`,
  inputClass: `absolute w-[100%] h-1/6 bottom-[0px] flex flex-col items-center gap-[10px] border-t-[1px] border-[#535354]`,
};

const MsgImage = (props: any) => {
  const { src } = props;
  const [preview, setPreview] = useState(true);
  return (
    <Image
      width={256}
      height={154}
      src={src}
      preview={preview}
      style={{ borderRadius: 16, objectFit: 'cover' }}
      onError={() => {
        setPreview(false);
      }}
      fallback={DEFAULT_IMAGE}
    />
  );
};
const genImg = ({ src }: any) => {
  return <MsgImage src={src} />;
};

export const MessageBox = memo((props: IProps) => {
  const { uniqueId, messageList, currentTime } = props;
  const isBase64Img = useCallback((str: string) => {
    const isbase64 = isBaseImg(str);
    return isbase64 ? (
      <StyledUploadImg src={str} style={{ objectFit: 'cover' }} />
    ) : (
      <Markdown
        components={{
          p: 'div',
          img: genImg,
        }}
      >
        {str}
      </Markdown>
    );
  }, []);

  return (
    <section id={'msg' + uniqueId} className={styles.messages}>
      <StyledTitle className="text-center">{currentTime}</StyledTitle>
      {messageList?.map((message, index) => {
        return (
          <StyledMessageItem key={index} className="relative flex flex-col">
            {message.person === 'secondary' ? (
              <>
                <StyledLeftPerson className={`${styles.personLeftHeader}`}>
                  <p className="xxc-name">{message.nickname}</p>
                  <img className="xxc-avatar" src={imgXxx2} />
                </StyledLeftPerson>
                <StyledLeftMessage className={`${styles.personLeftMsg}`}>
                  {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                </StyledLeftMessage>
              </>
            ) : (
              <>
                <p className={styles.personRightHeader}>{message.nickname}</p>
                <StyledRightMessage className={styles.personRightMsg}>
                  {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                </StyledRightMessage>
              </>
            )}
          </StyledMessageItem>
        );
      })}
    </section>
  );
});
