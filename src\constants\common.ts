/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-19 00:12:59
 * @LastEditTime: 2025-01-24 17:28:49
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export const DEVICE_STATUS: Record<any, any> = {
  1: '监测中',
  2: '未监测',
  3: '删除',
};

export const USER_STATUS: Record<any, any> = {
  1: '已启用',
  '-2': '已停用',
};
import userDefault from '@/assets/user/img_avatar.jpg';
export const IMAGES_DEFAULT = {
  user: userDefault,
};

export const DEFAULT_IMAGE =
  'data:image/webp;base64,UklGRgQDAABXRUJQVlA4IPgCAAAwUQCdASppAsEBPu12t1Yps6WjoVEYQnAdiWlu4XfkQ5+bO65pZwAS2IYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5emEHYTqdTqdTqdTqcohWo8eYxndTzLxDGd1PMqaSvWACDIbCbYTqdTqdTqcg8VUpKJ2d1PMvEMZ3U8y8QBWXaTKHgAQDm5LlQyujD3OJ5eIYzup4Y75u08JmFPZpqoEUaAvN0Ye5xPLxDGd1PL5trhA+YEl5XH8PI4NmF2DxldZl4hjO6nmXiGMrxUaYJDODzKCBsiQuUkGvO6dPGV1mXiGM7qeZeIYyvFJWwDH6zpJeSu5pyFv/UAp2AwhcZXWZeIYzup5l4hjK8yEceWzbEtlM7/FZMyjoAotVat2nmXiGM7qeZeIYzpHaTK6MDN5ir7+WDy8QxndTzLxDGdxcxLxDGdHCdb2CC5xPLxDGd1PMvELUkVAgZDuimFWwaYVbBphVsDNCICXiGM7qeZeIYzup5l4hjO6rYBMrow9zieXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6nmXiGM7qeZeIYzup5l4hjO6cAAP7/sxoAAAAAAAAAAC6ApC6f/IT8iAY70+sbs90qMeZwQJXn21jfiH+A0e5nshZ83Ph+uJvz4sEXbiBKHFSk7fQV0VVTkdLzBWWhiAAAABM9jAAAAAAAAAAABx7J8251z5tzrRwA';

export enum GLOBAL_MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  delete = 3,
  unbind = 5, // 解绑
  deleteRemind = 6, // 提示解绑用户
}

export const SEX_OPTIONS = [
  {
    value: 0,
    label: '不清',
  },
  {
    value: 1,
    label: '雄性',
  },
  {
    value: 2,
    label: '雌性',
  },
];

export const DEFAULT_EXPERT_USER_NAME = '星小尘病媒专家';

export const STORAGE_KEY_INIT_PASSWD = 'is_init_passwd';

export const SENSOR_TYPE = {
  hum: 'hum',
  temp: 'temp',
  wind: 'wind',
  light: 'light',
  co2: 'co2',
};
export const SENSOR_TYPE_NAME = {
  hum: '湿度',
  temp: '温度',
  wind: '风速',
  light: '光照',
  co2: '二氧化碳',
};
