import { deviceAPI } from '@/api';
import { filterTreeWidthProperty, updateTreeDefaultProperty } from '@/utils/tree.util';
import { request, useModel } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import { Tree, TreeSelect } from 'antd';
import { useEffect, useMemo, useState } from 'react';
interface IProps {
  onChange: any;
  visible?: boolean;
  deviceType?: string;
  onDataLoad?: (data: any) => void;
  CustomTree?: React.FC;
  [key: string]: any;
}

export default function DeviceTree(props: IProps) {
  const { defaultCheckedKeys = [], onChange, visible = true, disabled } = props;
  const [selectedKeys, setSelectedKeys] = useState<any>([]);
  const { setRegionDeviceList, defaultRegionKey, defaultRegion } = useModel('useDevice', (m) => ({
    setRegionDeviceList: m.setRegionDeviceList,
    defaultRegionKey: m.defaultRegionKey,
    defaultRegion: m.defaultRegion,
  }));

  const { menu } = useModel('useMenu', (m) => ({
    setRegion: m.setRegion,
    menu: m.menu,
  }));

  const deviceOptions = useMemo(() => {
    const deviceData = filterTreeWidthProperty(menu, 'id') || [];

    return deviceData;
  }, [menu]);

  useEffect(() => {
    request(deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setRegionDeviceList(list);
      }
    });
  }, []);

  const onDeviceCheck = useMemoizedFn((checkedKeys: any) => {
    setSelectedKeys(checkedKeys);
    onChange?.(checkedKeys);
  });

  useEffect(() => {
    if (!defaultRegionKey && deviceOptions[0]) {
      onDeviceCheck(deviceOptions[0]?.key);
    } else {
      onDeviceCheck(defaultRegionKey);
    }
  }, [defaultRegionKey, deviceOptions]);

  if (!visible || !deviceOptions?.length) {
    return null;
  }
  return (
    <TreeSelect
      disabled={disabled}
      onChange={onDeviceCheck}
      treeData={deviceOptions}
      value={selectedKeys}
      treeNodeLabelProp="key"
      treeDefaultExpandAll
      fieldNames={{
        label: 'name',
        value: 'key',
        children: 'children',
      }}
      style={{ height: 40 }}
      allowClear
    />
  );
}
