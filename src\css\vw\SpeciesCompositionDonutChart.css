/* SpeciesCompositionDonutChart 组件样式 */
.scdc-card-container {
  box-sizing: content-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.scdc-title-container {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-left: 30px;
}

.scdc-title-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.scdc-title-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 25px;
  color: #fff;
  margin: 0;
}

.scdc-chart-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 20px;
  padding: 0 30px;
}

.scdc-stat-box {
  margin-left: -40px;
  height: 200px;
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scdc-stat-value {
  color: #fff;
  font-size: 32px;
  font-weight: 500;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%);
  width: 100%;
  pointer-events: none;
}

.scdc-stat-label {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
  width: 100%;
  pointer-events: none;
}

.scdc-list-box {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex-shrink: 0;
}

.scdc-list-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.scdc-color-rect {
  width: 12px;
  height: 6px;
  border-radius: 1px;
}

.scdc-species-name {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
  flex-grow: 1;
  max-width: 72px;
}

.scdc-species-value {
  color: rgba(255, 255, 255, 0.9);
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  font-weight: 400;
}
