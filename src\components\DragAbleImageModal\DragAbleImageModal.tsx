import ReactDOM from 'react-dom';
import Draggable from 'react-draggable';
import { useRef, useState, useEffect, CSSProperties } from 'react';
import { IconClose, IconRotate, IconScaleDown, IconScaleUp } from '@/assets/svg';
import { formatDecimal } from '@/utils/number.util';

interface IProps {
  visible: boolean;
  onClose: () => any;
  src: string;
  maxScale?: number;
  minScale?: number;
  style?: CSSProperties;
  className?: string;
  title?: string;
}

export default function DraggableImageModal(props: IProps) {
  const { title, onClose, src, maxScale = 5, minScale = 0.1, style, className = '' } = props;
  const [disabled, setDisabled] = useState(false);
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  // 旋转和缩放操作
  const rotateUp = () => setRotate((r) => (r + 90) % 360);
  const rotateDown = () => setRotate((r) => (r - 90) % 360);
  const scaleUp = () => {
    const newScale = Math.min(scale + 0.2, maxScale);
    setScale(newScale);
  };
  const scaleDown = () => {
    const newScale = Math.max(scale - 0.2, minScale);
    setScale(newScale);
  };

  const handleWheel = (e: React.WheelEvent<HTMLImageElement>) => {
    // e.preventDefault();

    if (!imageRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();

    // Calculate mouse position relative to the container
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    // Calculate current image position relative to container
    const imgCenterX = container.clientWidth / 2 + position.x;
    const imgCenterY = container.clientHeight / 2 + position.y;

    // Calculate mouse position relative to image center
    const mouseFromCenterX = mouseX - imgCenterX;
    const mouseFromCenterY = mouseY - imgCenterY;

    // Calculate new scale
    const newScale =
      e.deltaY < 0 ? Math.min(scale + 0.1, maxScale) : Math.max(scale - 0.1, minScale);

    // Calculate the scale change ratio
    const scaleDiff = newScale / scale;

    // Calculate new position to keep mouse point fixed
    const newX = position.x - mouseFromCenterX * (scaleDiff - 1);
    const newY = position.y - mouseFromCenterY * (scaleDiff - 1);

    setScale(newScale);
    setPosition({ x: newX, y: newY });
  };

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.target as HTMLImageElement;
    const container = containerRef.current;
    if (!container) return;

    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;
    const imgWidth = img.naturalWidth;
    const imgHeight = img.naturalHeight;

    // Calculate scale to fit the image by the shorter side while maintaining aspect ratio
    const scaleX = containerWidth / imgWidth;
    const scaleY = containerHeight / imgHeight;

    // Use the larger scale to ensure the shorter side fits perfectly
    // But don't exceed maxScale
    const newScale = Math.min(Math.max(scaleX, scaleY), maxScale);

    setScale(newScale);
    setPosition({ x: 0, y: 0 }); // Reset position when image loads
  };

  // Reset scale and position when src changes
  useEffect(() => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  }, [src]);

  return ReactDOM.createPortal(
    <div
      className={
        'fixed left-0 top-0 w-[100vw] h-[100vh] z-[9999] flex justify-center items-center ' +
        className
      }
      style={style ?? {}}
    >
      <Draggable disabled={disabled} bounds="parent" handle=".js-drag-header">
        <div
          className="w-[64%] bg-white rounded-[8px] overflow-hidden"
          style={{
            boxShadow: '0px 0px 60px 0px rgba(0, 0, 0, 0.25)',
          }}
        >
          <div className="relative w-full pt-[68%] h-0">
            <div
              className="absolute top-[10px] right-[13px] size-[30px] cursor-pointer flex justify-center items-center z-10 "
              onClick={onClose}
            >
              <IconClose className="size-[10px]   text-icon-normal" />
            </div>
            <div className="absolute inset-0">
              <div
                className="js-drag-header px-[20px] h-[48px] leading-[48px] text-txt-main text-[14px] font-[500] cursor-pointer"
                onMouseOver={() => setDisabled(false)}
                onMouseOut={() => setDisabled(true)}
              >
                {title || '图片'}
              </div>
              <div
                ref={containerRef}
                className="mx-[20px] flex flex-col items-center justify-center rounded-[4px] overflow-hidden h-[calc(100%-112px)] bg-black/5"
              >
                <Draggable
                  disabled={false}
                  handle=".js-drag-image"
                  position={position}
                  onDrag={(e, data) => setPosition({ x: data.x, y: data.y })}
                >
                  <div className="js-drag-image w-full h-full relative">
                    <img
                      ref={imageRef}
                      src={src}
                      onLoad={handleImageLoad}
                      onWheel={handleWheel}
                      draggable={false}
                      style={{
                        position: 'relative',
                        left: '50%',
                        top: '50%',
                        transform: `translate(-50%,-50%) rotate(${rotate}deg) scale(${scale})`,
                        transformOrigin: 'center',
                        maxWidth: 'none',
                        maxHeight: 'none',
                        borderRadius: '4px',
                      }}
                    />
                  </div>
                </Draggable>
              </div>
              <div className="my-[18px] items-center flex justify-center gap-x-[12px] text-txt-sub select-none">
                <div className="flex items-center justify-between rounded-[4px] h-[28px] bg-[#f9f9fb] px-[10px] min-w-[80px]">
                  <IconRotate
                    onClick={rotateUp}
                    className="cursor-pointer hover:text-txt-blue"
                  ></IconRotate>
                  <IconRotate
                    onClick={rotateDown}
                    className="cursor-pointer scale-x-[-1] hover:text-txt-blue"
                  ></IconRotate>
                </div>
                <div className="flex items-center justify-between rounded-[4px] h-[28px] bg-[#f9f9fb] px-[10px]  min-w-[80px]">
                  <IconScaleUp
                    onClick={scaleUp}
                    className="cursor-pointer hover:text-txt-blue"
                  ></IconScaleUp>
                  <IconScaleDown
                    onClick={scaleDown}
                    className="cursor-pointer hover:text-txt-blue"
                  ></IconScaleDown>
                </div>
                <div className="flex items-center justify-center rounded-[4px] h-[28px] bg-[#f9f9fb] min-w-[58px]">
                  <span
                    className="cursor-pointer hover:text-txt-blue"
                    onClick={() => {
                      setScale(1);
                      setPosition({ x: 0, y: 0 });
                    }}
                  >
                    1:{formatDecimal(scale)}
                  </span>
                </div>
                <span className="hidden">旋转:{rotate}度</span>
              </div>
            </div>
          </div>
        </div>
      </Draggable>
    </div>,
    document.body,
  );
}
