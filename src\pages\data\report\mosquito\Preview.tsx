import { useMemo } from 'react';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import './Preview.css';

const parseMosquitoDetails = (details: string) => {
  return details.split('||').map((item) => {
    const [species, count] = item.split(',');
    return { species, count: parseInt(count) };
  });
};

export default function Preview(props: any) {
  const { onClose, toggleModalVisible, data } = props;
  const reportId = data?.report_id;
  const reportData = useMemo(() => {
    return Array.isArray(data?.details) ? data.details[0] : {};
  }, [data]);

  const mosquitoData = parseMosquitoDetails(reportData.mosquito_details || '');
  return (
    <XcModalForm
      title={'预览 ' + reportId}
      width={1200}
      open={true}
      onOpenChange={toggleModalVisible}
      onFinish={async () => {
        onClose();
        return true;
      }}
      // submitter={{ render: () => null }}
    >
      <div className="pt-[16px]">
        <table className="report-preview-table">
          <tbody>
            <tr>
              <td colSpan={12} className="text-center bg-bgc-normal">
                <div className="text-txt-main font-bold text-[18px] ">蚊虫成蚊监测表</div>
              </td>
            </tr>
            <tr>
              <td className={'label'} width="120">
                监测时段
              </td>
              <td colSpan={3}>
                {reportData.start_time} 至 {reportData.end_time}
              </td>
              <td className={'label'}>监测地点</td>
              <td colSpan={7}>{reportData.monitor_location}</td>
            </tr>
            <tr>
              <td className={'label'}>监测方法</td>
              <td colSpan={3}>{reportData.monitor_method}</td>
              <td className={'label'}>密度计算单位</td>
              <td colSpan={7}>{reportData.density_unit}</td>
            </tr>
            <tr>
              <td className={'label'}>温度</td>
              <td>{reportData.temperature}</td>
              <td className={'label'}>湿度</td>
              <td>{reportData.humidity}</td>
              <td className={'label'}>风速</td>
              <td>{reportData.wind_speed}</td>
              <td className={'label'}>二氧化碳浓度</td>
              <td>{reportData.co2_level}</td>
              <td className={'label'}>气候</td>
              <td colSpan={4}>{reportData.climate}</td>
            </tr>
            <tr>
              <td rowSpan={2} className={'label'}>
                设备名称
              </td>
              <td rowSpan={2} className={'label'}>
                监测点环境类型
              </td>
              <td colSpan={5} className={'label'}>
                蚊种种类及数量
              </td>
              <td colSpan={4} className={'label'}>
                统计数据
              </td>
              <td rowSpan={2} className={'label'}>
                备注
              </td>
            </tr>
            <tr>
              {mosquitoData.map((item, index) => (
                <td key={index} className={'label'}>
                  {item.species}
                </td>
              ))}
              <td className={'label'}>监测总数</td>
              <td className={'label'}>成蚊密度</td>
              <td className={'label'}>伊蚊成蚊密度</td>
              <td className={'label'}>有效监测时长</td>
            </tr>
            <tr>
              <td>{reportData.device_name || ''}</td>
              <td>{reportData.area_type || ''}</td>
              {mosquitoData.map((item, index) => (
                <td key={index}>{item.count || '0'}</td>
              ))}
              <td>{reportData.total_count || '0'}</td>
              <td>{reportData.adult_density || '0'}</td>
              <td>{reportData.aedes_density || '0'}</td>
              <td>{reportData.monitor_duration || '0'}</td>
              <td>{reportData.remark || ''}</td>
            </tr>
            <tr>
              <td colSpan={2} className={'label'}>
                监测负责人
              </td>
              <td colSpan={8}>{reportData.monitor_person || ''}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </XcModalForm>
  );
}
