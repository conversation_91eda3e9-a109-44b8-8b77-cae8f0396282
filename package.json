{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "repository": "**************:ant-design/ant-design-pro.git", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "openapi": "max openapi", "prepare": "husky", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env PORT=8007 REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "jest", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "tsc": "tsc --noEmit"}, "browserslist": ["defaults"], "lint-staged": {"*.{js,jsx}": ["eslint --fix", "prettier --write"], "*.{ts,tsx}": ["eslint --ext .ts,.tsx --fix", "prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "^2.7.19", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "ahooks": "^3.8.4", "antd": "^5.21.2", "antd-style": "^3.7.0", "classnames": "^2.5.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "5.6.0", "echarts-liquidfill": "3.1.0", "highlight.js": "^11.11.1", "lodash": "^4.17.21", "nanoid": "3", "postcss-px-to-viewport": "^1.1.1", "querystring": "^0.2.1", "react": "^18.3.1", "react-bmapgl": "^0.2.28", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-json-view": "^1.21.3", "react-markdown": "^9.0.3", "remark-gfm": "^4.0.1", "zustand": "^5.0.3"}, "devDependencies": {"@ant-design/pro-cli": "^3.3.0", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/bmapgl": "^0.0.7", "@types/classnames": "^2.3.1", "@types/express": "^4.17.21", "@types/history": "^5.0.0", "@types/jest": "^29.5.13", "@types/lodash": "^4.17.10", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@umijs/lint": "^4.3.24", "@umijs/max": "^4.3.24", "cross-env": "^7.0.3", "eslint": "^8.57.1", "express": "^4.21.1", "gh-pages": "^6.1.1", "husky": "^9.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "typescript": "^5.6.3", "umi-presets-pro": "^2.0.3", "umi-serve": "^1.9.11"}, "engines": {"node": ">=12.0.0"}}