/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 09:24:35
 * @LastEditTime: 2024-12-30 14:08:17
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  title: string;
  icon: React.ReactNode;
}

export const QuickTag = (props: IProps) => {
  const { title, icon } = props;
  return (
    <div
      className="px-[20px] h-[38px] rounded-[100px] flex items-center gap-[8px]"
      style={{
        backgroundColor: 'rgba(205, 215, 224, 0.1)',
      }}
    >
      {icon}
      <span>{title}</span>
    </div>
  );
};
