/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 23:46:47
 * @LastEditTime: 2024-12-12 23:50:17
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { memo } from 'react';
import EChartsCommon from '../Charts/EChartsCommon';

/**
 *  组合水波图
 */
export const Pie = memo(() => {
  const option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      top: '5%',
      left: 'center',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' },
        ],
      },
    ],
  };

  return <EChartsCommon option={option} />;
});
