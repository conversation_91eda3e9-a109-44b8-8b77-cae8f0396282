.XxcUserPopover {
  position: absolute;
  right: 20px;
  top: 65px;
  width: 280px;
  border-radius: 12px;
  z-index: 99;
  display: flex;
  flex-direction: column;
  padding: 16px 20px;
  gap: 12px;
  background: #FFFFFF;
  box-shadow: 0px 6px 18px 0px rgba(4, 60, 144, 0.08);
}

.XxcUserPopover-user {
  position: relative;
}

.XxcUserPopover-userAvatar {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 56px;
  height: 56px;
  opacity: 1;
  border-radius: 50%;
  box-sizing: border-box;
  border: 1px solid #D9EAFF;
  overflow: hidden;
}

.XxcUserPopover-userAvatarImg {
  width: 100%;
  height: 100%;
}

.XxcUserPopover-userInfo {
  margin-left: 68px;
  padding-top: 8px;
  height: 56px;
}

.XxcUserPopover-username {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 18px;
  color: #313747;
}

.XxcUserPopover-userRole {
  margin-top: 4px;
  font-size: 14px;
  font-weight: normal;
  line-height: 18px;
  color: #84909F;
}

.XxcUserPopover-menu {
  width: 240px;

}

.XxcUserPopover-menuItem {
  height: 42px;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  background: rgba(246, 246, 248, 0.7);
  font-size: 14px;
  color: #313747;
  display: flex;
  justify-content: space-between;
  cursor: pointer;
}

.XxcUserPopover-menuItemIcon {
  width: 16px;
  height: 16px;
}

.XxcUserPopover-logout {
  width: 240px;
  height: 38px;
  font-size: 14px;
  line-height: 18px;
  border-radius: 19px;
  padding: 10px 0px;
  box-sizing: border-box;
  border: 1px solid #DEE9F3;
  cursor: pointer;
  text-align: center;
}
