import { chatAPI } from '@/api';
import { getBase64 } from '@/utils/file.util';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import dayjs from 'dayjs';
import * as nano from 'nanoid';
import { useCallback, useEffect, useMemo, useState, useTransition } from 'react';
import { IMessage } from '../../pages/data/components/Xxc/xxc.interface';
const { nanoid } = nano;

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-05 17:40:46
 * @LastEditTime: 2025-02-05 18:38:00
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

const host = process.env.NODE_ENV === 'development' ? 'http://************:8001/api' : '/api';
console.log('🚀 ~ host:', host);
export const useXxc = () => {
  const [messageList, setMessageList] = useState<IMessage[]>([]);
  const [sendMsg, setSendMsg] = useState('');
  const [imageUrl, setImgUrl] = useState<string>();
  const uniqueId = useMemo(() => nanoid(), []);
  const currentTime = useMemo(() => dayjs().format('今天HH:mm'), []);
  const [, startTransition] = useTransition();
  const ctrl = new AbortController();
  // a回答， q问问题
  const [qa, setQA] = useState<'Q' | 'A' | null>();
  // 发图片
  const onSendImg = useCallback(async (img: string, messageList: IMessage[]) => {
    if (img) {
      const list = messageList.concat({
        text: img,
        person: 'primary',
        nickname: '管理员',
        timestamp: Date.now(),
      });
      setMessageList(list);

      try {
        await fetch(`${host}${chatAPI.chat}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            session_id: uniqueId,
            image_url: img,
            stream: true,
          }),
        });

        return list;
      } catch (error) {
        console.log('🚀 ~ onSendImg ~ error:', error);
      }
    }

    return messageList;
  }, []);
  // 发消息
  const onSendMsg = useCallback(async () => {
    if (qa === 'A') {
      return;
    }
    if (sendMsg.trim() === '') {
      return;
    }
    setQA('A');
    setSendMsg('');
    let prv = messageList.slice();
    if (imageUrl) {
      prv = await onSendImg(imageUrl, prv);
      setImgUrl(undefined);
    }

    if (sendMsg && sendMsg.trim() !== '') {
      prv = prv.concat(
        { text: sendMsg, person: 'primary', nickname: '管理员', timestamp: Date.now() },
        { text: '...', person: 'secondary', nickname: '星小尘', timestamp: Date.now() },
      );
      setMessageList(prv);
      fetchEventSource(`${host}${chatAPI.chat}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: uniqueId,
          question: sendMsg,
          stream: true,
        }),
        signal: ctrl.signal,
        async onopen(response) {
          console.log('🚀 ~ onopen', response.status);
        },
        onmessage(msg) {
          let m = JSON.parse(msg.data);
          const np = prv.slice();
          const lastItem = np[np.length - 1];

          if (lastItem.text === '...') {
            lastItem.text = '';
          }
          lastItem.text += m.content;

          startTransition(() => {
            setMessageList(np);
          });
        },
        onclose() {
          console.log('🚀 ~onclose:');
          setQA(null);
          ctrl.abort();
        },
        onerror(err) {
          console.log('🚀 ~ err:', err);
          throw err;
        },
      });
    }
  }, [imageUrl, sendMsg, messageList]);

  const onPressEnter = useCallback(
    (e: any) => {
      if (qa === 'A') {
        return;
      }
      e.preventDefault();
      onSendMsg();
    },
    [qa, sendMsg],
  );
  const onChangeText = useCallback((e: any) => setSendMsg(e.target.value), []);
  const beforeUpload = async (file: File) => {
    await new Promise((resolve) => {
      getBase64(file, (base64Str: string) => {
        setImgUrl(base64Str);
        resolve({ base64: base64Str, name: file.name });
      });
    });
    return false;
  };

  useEffect(() => {
    return () => {
      ctrl.abort();
    };
  }, []);

  useEffect(() => {
    startTransition(() => {
      const messageSection = document.getElementById('msg' + uniqueId);
      messageSection?.scrollTo(0, messageSection.scrollHeight);
    });
  }, [messageList]);

  return {
    imageUrl,
    setImgUrl,
    qa,
    onSendMsg,
    sendMsg,
    uniqueId,
    messageList,
    currentTime,
    beforeUpload,
    onChangeText,
    onPressEnter,
  };
};
