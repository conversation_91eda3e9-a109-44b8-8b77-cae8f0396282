import { Popover } from 'antd';
import './ChatTaskView.css';
import { useState } from 'react';
import SmartTask from '../SmartTask/SmartTask';
import TaskRecord from '../TaskRecord/TaskRecord';
export default function ChatTaskView() {
  const [selectedId, setSelectedId] = useState(1);
  const [taskList, setTaskList] = useState([
    { id: 1, name: '智能任务', content: <SmartTask /> },
    { id: 2, name: '其他任务', content: '其他任务' },
    { id: 3, name: '其他任务', content: '其他任务' },
    { id: 4, name: '其他任务', content: '其他任务' },
    { id: 5, name: '其他任务', content: '其他任务' },
    { id: 6, name: '其他任务', content: '其他任务' },
  ]);

  return (
    <div className="ChatTaskView">
      <div className="ChatTaskView-list">
        {taskList.map((item, index) => (
          <Popover
            placement="top"
            trigger="click"
            title={null}
            content={item.content || null}
            key={index}
          >
            <div
              className={`ChatTaskView-item ${selectedId === item.id ? 'is-selected' : ''}`}
              onClick={() => setSelectedId(item.id)}
            >
              {item.name}
            </div>
          </Popover>
        ))}
      </div>
      <Popover placement="top" trigger="click" title={null} content={TaskRecord || null}>
        <div className="ChatTaskView-record">
          <div className="ChatTaskView-recordIcon"></div>
          <div className="ChatTaskView-recordTitle">任务记录</div>
        </div>
      </Popover>
    </div>
  );
}
