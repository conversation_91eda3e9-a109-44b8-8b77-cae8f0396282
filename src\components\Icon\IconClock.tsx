/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-01 00:40:25
 * @LastEditTime: 2025-01-14 22:28:55
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export const IconClock = (props: any) => {
  const { className } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.10354 2.77815C4.10354 2.57177 4.02157 2.37384 3.87566 2.22788C3.72971 2.08197 3.53177 2 3.32539 2C3.11901 2 2.92108 2.08197 2.77512 2.22788L1.21848 3.78452C1.07671 3.93131 0.998256 4.12792 1.00003 4.33199C1.0018 4.53606 1.08366 4.73127 1.22796 4.87558C1.37227 5.01988 1.56748 5.10174 1.77156 5.10351C1.97563 5.10529 2.17223 5.02684 2.31902 4.88506L3.87566 3.32842C4.02157 3.18247 4.10354 2.98453 4.10354 2.77815ZM14.3254 5.11282C14.5318 5.11282 14.7297 5.03085 14.8757 4.88494C15.0216 4.73898 15.1035 4.54105 15.1035 4.33467C15.1035 4.12829 15.0216 3.93035 14.8757 3.7844L13.319 2.22776C13.1722 2.08598 12.9756 2.00753 12.7716 2.00931C12.5675 2.01108 12.3723 2.09293 12.228 2.23724C12.0837 2.38155 12.0018 2.57676 12 2.78083C11.9983 2.9849 12.0767 3.18151 12.2185 3.3283L13.7751 4.88494C13.9211 5.03085 14.119 5.11282 14.3254 5.11282ZM8 13.5C10.4853 13.5 12.5 11.4853 12.5 9C12.5 6.51472 10.4853 4.5 8 4.5C5.51472 4.5 3.5 6.51472 3.5 9C3.5 11.4853 5.51472 13.5 8 13.5ZM8 15C11.3137 15 14 12.3137 14 9C14 5.68629 11.3137 3 8 3C4.68629 3 2 5.68629 2 9C2 12.3137 4.68629 15 8 15ZM8.75 6.5C8.75 6.08579 8.41421 5.75 8 5.75C7.58579 5.75 7.25 6.08579 7.25 6.5V9V9.31066L7.46967 9.53033L8.46967 10.5303C8.76256 10.8232 9.23744 10.8232 9.53033 10.5303C9.82322 10.2374 9.82322 9.76256 9.53033 9.46967L8.75 8.68934V6.5Z"
        fill="#7979A6"
      />
    </svg>
  );
};
