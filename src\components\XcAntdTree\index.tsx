import { findAndAddParentIds, findNodeById, getAllChildIds } from '@/utils/tree.util';
import { Tree } from 'antd';
import { useEffect, useState } from 'react';

export const XcAntdTree = (props: any) => {
  const { treeData, defaultCheckedKeys, onCheck: onCheckNode, ...others } = props;
  const [checkedIds, setCheckedIds] = useState<any>([...(defaultCheckedKeys || [])]);
  useEffect(() => {
    setCheckedIds(defaultCheckedKeys);
  }, [defaultCheckedKeys]);
  const onCheck = (checkedIds: any, info: any) => {
    const { checked, node } = info;
    let newCheckedIds = [...checkedIds.checked];
    const nodeProps = node;

    if (checked) {
      // 如果点击的是父节点，只选中当前父节点
      if (nodeProps.children) {
        newCheckedIds.push(nodeProps.id);
      }
      // 选中子节点时，自动选中父节点
      const parentIds = findAndAddParentIds(treeData, nodeProps.id, []);
      if (parentIds) {
        newCheckedIds = Array.from(new Set([...newCheckedIds, ...parentIds]));
      }
    } else {
      // 取消选中节点
      newCheckedIds = newCheckedIds.filter((id) => id !== nodeProps.id);
      if (nodeProps.children) {
        // 如果取消的是父节点，递归取消所有子节点
        const nodeData = findNodeById(treeData, nodeProps.id);
        if (nodeData) {
          const childIds = getAllChildIds(nodeData);
          newCheckedIds = newCheckedIds.filter((id) => !childIds.includes(id));
        }
      }
    }

    setCheckedIds(newCheckedIds);
    onCheckNode(newCheckedIds);
  };

  return (
    <Tree
      checkedKeys={checkedIds}
      treeData={treeData}
      onCheck={onCheck}
      checkStrictly={true}
      {...others}
    ></Tree>
  );
};
