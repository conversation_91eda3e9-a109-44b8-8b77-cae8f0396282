/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 20:11:48
 * @LastEditTime: 2024-12-27 09:46:58
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconPhoto2 = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="28"
      height="28"
      viewBox="0 0 28 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.3394 9.95371C13.1381 9.76702 12.9004 9.61921 12.6286 9.50836C12.3547 9.39751 12.0647 9.34111 11.7586 9.34111C11.4424 9.34111 11.1484 9.39557 10.8745 9.50836C10.6006 9.61921 10.365 9.76702 10.1636 9.95371C9.96218 10.1404 9.80107 10.3602 9.68225 10.613C9.56142 10.8658 9.50302 11.1342 9.50302 11.4181C9.50302 11.7021 9.56344 11.9704 9.68225 12.2233C9.80309 12.4761 9.96218 12.6978 10.1636 12.8884C10.365 13.079 10.6026 13.2306 10.8745 13.3415C11.1484 13.4524 11.4424 13.5088 11.7586 13.5088C12.0647 13.5088 12.3547 13.4524 12.6286 13.3415C12.9024 13.2306 13.1381 13.079 13.3394 12.8884C13.5408 12.6978 13.7019 12.4761 13.8208 12.2233C13.9396 11.9704 14 11.7021 14 11.4181C14 11.1342 13.9396 10.8658 13.8208 10.613C13.7019 10.3582 13.5408 10.1385 13.3394 9.95371ZM21.2197 6H6.78228C5.79749 6 5 6.73901 5 7.65111V19.3489C5 20.261 5.79749 21 6.78228 21H21.2177C22.2025 21 23 20.261 23 19.3489V7.65111C23.002 6.73901 22.2045 6 21.2197 6ZM21.2056 17.0871C21.0143 16.6787 20.7908 16.2625 20.537 15.8366C20.2833 15.4107 20.0054 15.0257 19.7033 14.6795C19.4012 14.3333 19.0729 14.0513 18.7185 13.8335C18.3641 13.6157 17.9955 13.5068 17.6109 13.5068C17.1598 13.5068 16.7771 13.5885 16.461 13.7518C16.1448 13.9172 15.8709 14.1214 15.6413 14.3703C15.4117 14.6192 15.2043 14.8856 15.023 15.1696C14.8418 15.4535 14.6585 15.72 14.4773 15.9689C14.296 16.2178 14.0967 16.424 13.8812 16.5873C13.6657 16.7507 13.4099 16.8324 13.1119 16.8324C12.8138 16.8324 12.5601 16.8129 12.3506 16.7721C12.1392 16.7312 11.9499 16.6807 11.7827 16.6184C11.6156 16.5562 11.4585 16.4881 11.3155 16.4123C11.1725 16.3364 11.0154 16.2684 10.8483 16.2061C10.6811 16.1439 10.4898 16.0933 10.2723 16.0525C10.0568 16.0117 9.80107 15.9922 9.50302 15.9922C9.2533 15.9922 9.00761 16.0525 8.76191 16.1711C8.51824 16.2917 8.2806 16.4434 8.05102 16.6301C7.82144 16.8168 7.59991 17.0249 7.39047 17.2563C7.17901 17.4877 6.98769 17.7172 6.8145 17.9487V8.39984C6.8145 7.99144 7.17297 7.66083 7.61401 7.66083H20.4061C20.8472 7.66083 21.2056 7.99144 21.2056 8.39984V17.0871Z"
        fill="#667085"
      />
    </svg>
  );
};
