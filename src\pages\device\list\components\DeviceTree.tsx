import { deviceAPI } from '@/api';
import { updateTreeDefaultProperty } from '@/utils/tree.util';
import { request } from '@umijs/max';
import { Tree } from 'antd';
import { useEffect, useMemo, useState } from 'react';
interface IProps {
  onChange: any;
  visible?: boolean;
  deviceType?: string;
  onDataLoad?: (data: any) => void;
  CustomTree?: React.FC;
  [key: string]: any;
}

export default function DeviceTree(props: IProps) {
  const {
    defaultCheckedKeys = [],
    onChange,
    visible = true,
    deviceType = 'all',
    onDataLoad,
    disabled,
    checkable = true,
    ...others
  } = props;
  const [deviceData, setDeviceData] = useState<any>(null);
  const [selectedKeys, setSelectedKeys] = useState<any[]>([...defaultCheckedKeys]);

  useEffect(() => {
    setSelectedKeys(defaultCheckedKeys);
  }, [defaultCheckedKeys]);

  useEffect(() => {
    request(deviceType === 'all' ? deviceAPI.getDeviceTree : deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setDeviceData(list);
      }
    });
  }, []);
  const deviceOptions = useMemo(() => {
    if (!deviceData) {
      return [];
    }
    let treeData = updateTreeDefaultProperty({
      tree: deviceData,
      properties: ['id', 'devices'],
      callback: (node: any, value: any, property: string) => {
        if (property === 'devices' && value && value.length) {
          node.children = node.devices || [];
          return value;
        } else {
          if (!value) {
            return -Math.random().toString().slice(-16);
          } else {
            return value;
          }
        }
      },
    });
    console.log(treeData);
    onDataLoad?.(treeData);
    return treeData;
  }, [deviceData]);
  const onDeviceCheck = (checkedKeys: any, e: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys].filter((id) => id && id > 0);
    setSelectedKeys(keys);
    onChange(keys);
  };

  if (!visible || !deviceData) {
    return null;
  }
  return (
    <Tree
      checkable={checkable}
      selectedKeys={selectedKeys}
      checkedKeys={selectedKeys}
      disabled={disabled}
      blockNode={true}
      onCheck={onDeviceCheck}
      treeData={deviceOptions}
      defaultExpandAll
      fieldNames={{
        title: 'name',
        key: 'id',
        children: 'children',
      }}
      // {...others}
    />
  );
}
