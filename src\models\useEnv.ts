/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-19 14:22:22
 * @LastEditTime: 2025-02-12 09:12:57
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { useState } from 'react';

const useEnvModel = () => {
  const [envs, setEnvs] = useState({
    device_id: null,
    device_status: null,
    area_type: '-',
    water: 0,
    direction: '-',
    temp: 0,
    hum: 0,
    co2: 0,
    speed: 0,
  });

  return {
    envs,
    setEnvs,
  };
};
export default useEnvModel;
