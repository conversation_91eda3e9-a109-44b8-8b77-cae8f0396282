import userAvatar from '@/assets/user/img_avatar.jpg';
import aiAvatar from '../assets/avatar-robot.png';
import { MSG_TYPE } from '../constant';
import { ThinkingMessage } from '../StructMessage/StatusMessage/StatusMessage';
import ImageMessage from '../StructMessage/ImageMessage/ImageMessage';
import MarkdownMessage from '../StructMessage/MarkDwonMessage/MarkDownMessage';
import './MessageItem.css';
import SearchMessage from '../StructMessage/SearchMessage/SearchMessage';
import MessageToolbox from '../MessageToolbox/MessageToolbox';

export function TypedMessageItem(props: any) {
  const { msgType, content } = props.message || {};

  if (msgType === MSG_TYPE.THINKING) {
    return <ThinkingMessage />;
  }
  if (msgType === MSG_TYPE.IMAGE) {
    return <ImageMessage list={props.message?.ext?.imageList} />;
  }

  if (msgType === MSG_TYPE.MARKDOWN) {
    return <MarkdownMessage message={props.message} />;
  }

  if (msgType === MSG_TYPE.SEARCH) {
    return <SearchMessage message={props.message} />;
  }
  return <div className="px-[12px]">{content}</div>;
}

export default function MessageItem(props: any) {
  const { role = 'user' } = props.message || {};

  return (
    <div className={'MessageItem ' + (role === 'user' ? 'is-user' : 'is-ai')}>
      {role === 'user' ? (
        <img src={userAvatar} className="MessageItem-avatar" />
      ) : (
        <img src={aiAvatar} className="MessageItem-avatar" />
      )}
      <div className="MessageItem-content">
        <TypedMessageItem message={props.message} />
        {role === 'ai' ? <MessageToolbox message={props.message} /> : null}
      </div>
    </div>
  );
}
