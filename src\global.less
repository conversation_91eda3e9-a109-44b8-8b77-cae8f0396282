html,
body,
#root {
  overscroll-behavior: none;
  height: 100%;
  margin: 0;
  padding: 0;
  background:
    linear-gradient(to right, #e0f0ff, #e4f5ff),
    url(./assets/global_bg.jpg) no-repeat 0 0/100% 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: 12px;
  word-break: break-all;
  word-wrap: break-word;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  box-shadow: 0 0 0 100px white inset !important;
}

:root {
  /*  蓝色 */
  --txt-blue: #6265ff;
  --txt-white: #fff;
  /* // 白色 */
  --txt-main: #313847;
  /* // 主色 */
  --txt-sub: #505663;
  /* // 次色 */
  --txt-third: #84909f;
  /* // 辅助色 */
  --bgc-global: #edeef1;
  /* // 背景 全局 */
  --bgc-normal: #36383a15;
  /* // 背景通用 */
  --bgc-detail: #f7f8fc;
  /* // 背景详情 */
  --bgc-head: #fcfcfd;
  /* // 背景表头 */
  --line-split: #949baa26;
  /* // 分割线 */
  --line-border: #949baa4c;
  /* // 边框 */
  --icon-normal: #667085;
  /* // 图标通用色 */
  --btn-blue: #6265ff;
  /* // 按钮蓝色 */
  --btn-white: #f4f4f6;
  /* // 按钮白色 */
  --sys-red: #fd521b;
  /* // 红色 */
  --sys-purple: #6360e6;
  /* // 紫色 */
  --sys-green: #07c777;
  /* // 绿色 */
  --sys-yellow: #ffa63f;
  /* // 黄色 */
  --menu: #667085;
  /* // 菜单 */
  --menu-active: #00000026;
  /* // 菜单 - 选中 */
  --box-input: #f4f4f6;
  /* //  框 - 输入 */
  --box-robot: #f5f7fa;
  /* //  框 - 星小尘 */
  --box-menu: #dadee8;
  /* // 框 - 菜单 */
}

:root.theme-blue {
  --bgc-global: #e9f2fb;
  --bgc-cont: #f3f8fd;
  --text-th: #242533;
  --text-main: #313747;
  --text-sub: #667085;
  --border-white: rgba(255, 255, 255, 0.6);
  --border-normal: #62c8f0;
}

@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url(./assets/YouSheBiaoTiHei.ttf);
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.xxc-full-page {
  display: flex;
  height: calc(100vh - 66px);
}

.colorWeak {
  filter: invert(80%);
}

.xxc-full-page {
  display: flex;
  height: calc(100vh - 66px);
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

/*  自定义滚动条样式 */
.react-json-view,
.xc-scrollbar {
  overflow-y: scroll;
  /* 垂直滚动条 */
  scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
  /* 滚动条的颜色 */
  scrollbar-width: 8px;

  /* 滚动条的宽度 */
  /* 针对webkit内核浏览器的自定义样式 */
  &::-webkit-scrollbar {
    width: 8px;
    /* 滚动条宽度 */
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    /* 滚动条轨道的颜色 */
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.15);
    /* 滚动条的颜色 */
    border-radius: 6px;
    /* 滚动条圆角 */
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.15);
    /* 滚动条的悬停颜色 */
  }

  &:hover {
    scrollbar-width: 8px;
  }
}

.image-preview-no-full {
  transform: translate(0, 0);

  .ant-image-preview-operations {
    background: rgba(0, 0, 0, 0.5);
  }

  .ant-image-preview-mask {
    background: none !important;
  }
  .ant-image-preview-close {
    display: none;
  }
}
body .map-container,
.map-container canvas {
  background: none !important;
  background-color: transparent !important;
}

.xc-scrollbar-x {
  overflow-x: auto;

  &::-webkit-scrollbar {
    height: 8px;
  }

  /* 滚动条轨道样式 */
  &::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 4px;
  }

  /* 滚动条滑块样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #00000026;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #00000033;
    /* 悬停时颜色加深 */
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.xc-scrollbar-none {
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
  }

  &::-webkit-scrollbar-corner,
  &::-webkit-scrollbar-thumb,
  &::-webkit-scrollbar-track {
    display: none;
    background: transparent;
  }
}

.xc-scrollbar-y {
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #00000026;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #00000033;
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

.xc-form-footer {
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0 12px;
}

.xc-form-button-submit,
.xc-form-button-cancel {
  border: 0;
  font-size: 14px;
  font-weight: normal;
  border-radius: 4px;
  width: 60px;
  height: 36px;
}
.xc-form-button-submit {
  background-color: var(--btn-blue);
  color: var(--txt-white);
}
.xc-form-button-cancel {
  color: var(--txt-sub);
  border: 1px solid #EAEAF1;
  background: #fff;
}

.xc-from-tree-box {
  height: 376px;
  margin-right: 0;
  padding-right: 30px;
  box-sizing: border-box;
  padding: 12px;
  border: 1px solid var(--line-border);
  border-radius: 8px;
}

.XxcDrawer {
  .ant-drawer-content-wrapper {
    background-color: #fff;
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    overflow: hidden;
  }

  .ant-drawer-body {
    margin: 0;
    display: flex;
    flex-direction: column;
    min-height: 370px;
    padding: 0 0px;
    overflow: hidden;
  }
}

.wrap-tree-select {
  // v4
  .ant-select-selector {
    height: auto !important;
    min-height: 32px; // or 40px, based on your design
  }

  // v5
  .ant-select-selection-overflow {
    flex-wrap: wrap;
  }

  .ant-select-selection-item {
    margin-bottom: 4px;
  }

  .ant-select-arrow {
    align-self: center;
  }
}
