import { commonStyles } from '@/constants/commonStyles';
import { IconDot } from '../Icon/IconDot';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 11:47:10
 * @LastEditTime: 2025-02-06 19:55:36
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  status: 'success' | 'default' | 'error' | 'warning' | 'processing';
  text: string;
  dot?: boolean;
  successDotColor?: string;
  defaultDotColor?: string;
}

export const CustomTableBodyItemStatus = (props: IProps) => {
  const {
    dot = true,
    status,
    text,
    successDotColor = '#07C777',
    defaultDotColor = '#667085',
  } = props;

  if (status === 'success') {
    return (
      <div className="flex items-center max-w-fit gap-[6px] px-[10px] py-[3px] bg-[#ECFDF3] rounded-[16px]">
        {dot && <IconDot className="size-[8px]" color={successDotColor} />}
        <span className={`${commonStyles.normalText14} text-[#037847]`}>{text}</span>
      </div>
    );
  }
  return (
    <div className="flex items-center max-w-fit gap-[6px] px-[10px] py-[3px] bg-[#F4F4F6] rounded-[16px]">
      {dot && <IconDot className="size-[8px]" color={defaultDotColor} />}
      <span className={`${commonStyles.normalText14} text-txt-main`}>{text}</span>
    </div>
  );
};
