.circle-container {
  position: absolute;
  width: 40px;
  height: 60px;
  transform: translate(-50%, -50%);
  --main-color: 159, 159, 159;

  &.is-active {
    --main-color: 0, 230, 184 !important;

    .ring {
      animation: ring-blink 3s infinite;
    }
  }
}

.center-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  box-shadow: 0 0 32px 15px rgba(var(--main-color), 0.4);
  border: 5px solid rgb(var(--main-color));
  box-sizing: content-box;
}

.is-checked .center-dot{
  background-color: red;
}

.device-icon {
  position: absolute;
  bottom: 50%;
  left: 50%;
  transform: translate(-50%, -20px);
  width: 40px;
  height: 45px;
  background-repeat: no-repeat;
  background-position: center top;
  z-index: 2;
}

.ring {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid rgb(var(--main-color));
  border-radius: 50%;
  opacity: 0;
  box-sizing: border-box;
  transform: translate(-50%, -50%) scale(0);
}

@keyframes ring-blink {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }

  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}

.ring-1 {
  animation-delay: 0s;
  border-color: rgba(var(--main-color), 0.8);
}

.ring-2 {
  animation-delay: 1s;
  border-color: rgba(var(--main-color), 0.6);
}

.ring-3 {
  animation-delay: 2s;
  border-color: rgba(var(--main-color), 0.4);
}

.ring::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 10px;
  height: 10px;
  background-color: rgba(var(--main-color), 0.7);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.ring-1::after {
  transform: translate(-50%, -50%) scale(0.5);
}

.ring-2::after {
  transform: translate(-50%, -50%) scale(0.7);
}

.ring-3::after {
  transform: translate(-50%, -50%) scale(0.9);
}
