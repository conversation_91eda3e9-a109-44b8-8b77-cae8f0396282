/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-19 00:23:32
 * @LastEditTime: 2025-01-19 00:28:03
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgDeviceTotal = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="62"
      height="62"
      viewBox="0 0 62 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.21"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 31V40C0 52.1503 9.84974 62 22 62H31H40C52.1503 62 62 52.1503 62 40V31V22C62 9.84974 52.1503 0 40 0H31H22C9.84974 0 0 9.84974 0 22V31Z"
        fill="#8280FF"
      />
      <path
        d="M22.6667 43.5C25.4444 45.4444 32.6667 48.1667 39.3333 43.5"
        stroke="#8280FF"
        strokeWidth="2.5"
        strokeLinecap="round"
      />
      <path
        d="M18.9167 29.3333C18.9167 22.6606 24.3273 17.25 31 17.25C37.6727 17.25 43.0833 22.6606 43.0833 29.3333C43.0833 36.0061 37.6727 41.4167 31 41.4167C24.3273 41.4167 18.9167 36.0061 18.9167 29.3333Z"
        stroke="#8280FF"
        strokeWidth="2.5"
      />
      <path
        d="M25.5833 29.3334C25.5833 26.3422 28.0088 23.9167 31 23.9167C33.9912 23.9167 36.4166 26.3422 36.4166 29.3334C36.4166 32.3245 33.9912 34.75 31 34.75C28.0088 34.75 25.5833 32.3245 25.5833 29.3334Z"
        stroke="#ABA9FF"
        strokeWidth="2.5"
      />
      <path
        d="M30.175 21.8249C29.9562 21.6061 29.8333 21.3094 29.8333 21C29.8333 20.6906 29.9562 20.3938 30.175 20.175C30.3938 19.9562 30.6906 19.8333 31 19.8333C31.3094 19.8333 31.6061 19.9562 31.8249 20.175C32.0437 20.3938 32.1666 20.6906 32.1666 21C32.1666 21.3094 32.0437 21.6061 31.8249 21.8249C31.6061 22.0437 31.3094 22.1666 31 22.1666C30.6906 22.1666 30.3938 22.0437 30.175 21.8249Z"
        fill="#ABA9FF"
        stroke="#ABA9FF"
      />
    </svg>
  );
};
