/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-12 20:13:51
 * @LastEditTime: 2024-12-17 18:59:09
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
}
export const IconNavigationPointer = (props: IProps) => {
  const { className } = props;
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.75452 9.08118C3.32114 8.91264 3.10445 8.82837 3.04118 8.70695C2.98634 8.60169 2.98627 8.4763 3.04099 8.37098C3.10411 8.24948 3.3207 8.16495 3.75388 7.99591L16.0473 3.19847C16.4383 3.04587 16.6339 2.96957 16.7588 3.01131C16.8673 3.04756 16.9524 3.1327 16.9887 3.2412C17.0304 3.36614 16.9541 3.56166 16.8015 3.9527L12.0041 16.2461C11.835 16.6793 11.7505 16.8959 11.629 16.959C11.5237 17.0137 11.3983 17.0137 11.293 16.9588C11.1716 16.8956 11.0874 16.6789 10.9188 16.2455L9.00569 11.326C8.97148 11.238 8.95437 11.194 8.92795 11.157C8.90454 11.1242 8.87583 11.0955 8.843 11.072C8.80596 11.0456 8.76197 11.0285 8.674 10.9943L3.75452 9.08118Z"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
