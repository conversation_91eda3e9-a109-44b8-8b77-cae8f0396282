import { deviceAPI } from '@/api/device';
import { IColumns } from '@/components/XcTable/interface/search.interface';
import { CustomTableBodyItemStatus } from '@/components/XcTable/XcTableBodyItemStatus';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import { isBlank } from '@/utils/string.util';
import { DATE_FMT } from '@/utils/time.util';
import { useRequest } from '@umijs/max';
import { message, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef } from 'react';

const { Text } = Typography;

export default () => {
  const tableRef = useRef<ITableRef>(null);

  // 请求接口
  const queryOperatorLogRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: deviceAPI.deviceOperatorLog,
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, type, code, current, pageSize } = params;
    const payload: Record<string, any> = {
      type,
      code,
      page: current,
      page_size: pageSize,
    };
    if (type === '0') {
      delete payload.type;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DATE_TIME);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DATE_TIME);
    } else {
      return;
    }

    const result = await queryOperatorLogRequest.run({
      ...payload,
      tenant_id: 1,
    });
    if (result.code === 200) {
      return {
        total: result.data.total,
        data: result.data.list,
      };
    } else {
      message.error(result.message || '请求失败');
    }
    return {
      total: 0,
      data: [],
    };
  };

  const columns: IColumns[] = [
    {
      title: '监测状态',
      width: 120,
      key: 'type',
      hideInTable: true,
      initialValue: '0',
      valueType: 'drop',
      realtime: true,
      valueEnum: {
        0: '全部状态',
        online: '启动监测',
        offline: '停止监测',
      },
    },
    {
      title: '入库时间',
      key: 'range_time',
      realtime: true,
      hideInTable: true,
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      valueType: 'rangePicker',
    },
    {
      title: '设备编号',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索设备编号',
      valueType: 'input',
    },
    {
      title: '日志编号',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      render: (_, item: { id: string }) => {
        return <Text ellipsis={{ tooltip: item.id }}>{item.id}</Text>;
      },
    },
    {
      title: '设备编号',
      dataIndex: 'device_id',
      key: 'device_id',
      hideInSearch: true,
      render: (_, item: { device_code: string }) => {
        return <Text ellipsis={{ tooltip: item.device_code }}>{item.device_code}</Text>;
      },
    },

    {
      title: '运行状态',
      dataIndex: 'type',
      key: 'type',
      hideInSearch: true,
      render: (_, item: { type: string }) => {
        // 这里的状态是当前状态
        return (
          <CustomTableBodyItemStatus
            dot={false}
            status={item.type === 'online' ? 'success' : 'default'}
            text={item.type === 'online' ? '启动监测' : '停止监测'}
          />
        );
      },
    },
    {
      title: '状态更新原因',
      dataIndex: 'message',
      key: 'message',
      hideInSearch: true,
      render: (_, item: { message: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.message }}>
            {isBlank(item.message) ? '-' : item.message}
          </Text>
        );
      },
    },
    {
      title: '记录时间',
      dataIndex: 'created_at',
      key: 'created_at',
      hideInSearch: true,
      render: (_, item: { created_at: string }) => {
        if (item.created_at) {
          return <span>{dayjs(new Date(item.created_at)).format('YYYY.MM.DD HH:mm:ss')}</span>;
        }
        return '';
      },
    },
    {
      title: '操作人员',
      dataIndex: 'user_name',
      key: 'user_name',
      hideInSearch: true,
      render: (_, item: { user_name: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.user_name }}>
            {isBlank(item.user_name) ? '-' : item.user_name}
          </Text>
        );
      },
    },
  ];

  return (
    <XcTableNew
      ref={tableRef}
      loading={queryOperatorLogRequest.loading}
      columns={columns}
      request={requestTable}
      rowSelection={null}
      searchTitle="设备运行日志"
      operator={
        <>
          {/* <CustomButton
                type="primary"
                className={`w-[100px] h-[40px] rounded-[40px] `}
                icon={<SvgSearch className="size-[24px]" color="white" />}
                onClick={() => {
                  tableRef?.current?.onSubmit();
                }}
              >
                <span className={`${commonStyles.mediumText14} text-white !self-center`}>查询</span>
              </CustomButton> */}
        </>
      }
    />
  );
};
