/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-27 11:14:13
 * @LastEditTime: 2025-01-27 11:19:36
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
  color?: string;
}

export const SvgPlus = (props: IProps) => {
  const { className, color = '#667085' } = props;
  return (
    <svg
      className={`xc-svg !align-middle ${className ?? ''}`}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M19 12L5 12" stroke={color} strokeWidth="1.5" strokeLinecap="round" />
      <path d="M12 5L12 19" stroke={color} strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
};
