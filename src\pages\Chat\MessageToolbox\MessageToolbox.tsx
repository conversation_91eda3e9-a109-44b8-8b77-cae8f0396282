import { useMemoizedFn } from 'ahooks';
import { ReactComponent as IconCopy } from './icons/copy.svg';
import { ReactComponent as IconRefresh } from './icons/refresh.svg';
import './MessageToolbox.css';
import { message } from 'antd';

interface IProps {
  message: any;
}

export default function MessageToolbox(props: IProps) {
  const data = props.message || {};
  const copy = useMemoizedFn(() => {
    navigator.clipboard.writeText(data.content || '');
    message.success('复制成功');
  });
  const refresh = useMemoizedFn(() => {
    console.log(data);
  });
  return (
    <div className="MessageToolbox">
      <div className="MessageToolbox-func" title="刷新">
        <IconRefresh onClick={refresh} />
      </div>
      <div className="MessageToolbox-func" title="复制">
        <IconCopy onClick={copy} />
      </div>
    </div>
  );
}
