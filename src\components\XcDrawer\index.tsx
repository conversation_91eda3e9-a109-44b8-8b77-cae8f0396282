/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-28 08:58:08
 * @LastEditTime: 2025-02-06 19:58:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { Drawer, DrawerProps } from 'antd';
import { DrawerStyles } from 'antd/es/drawer/DrawerPanel';
import { memo, useEffect, useState } from 'react';
import XcTableMask from '../XcTableMask';
import './index.css';
import { useMemoizedFn } from 'ahooks';

type IProps = {
  children: React.ReactNode;
  onClose: () => void;
  mask?: boolean;
  open?: boolean;
  destroyOnClose?: boolean;
  styles?: DrawerStyles;
  width?: number | string;
  editColumnWidth?: number;
  editColumnLeft?: number;
  isFormChanged?: boolean;
  xcTheme?: string;
  xcTitle?: string;
} & DrawerProps;

export const XcDrawer = memo((props: IProps) => {
  const {
    children,
    width,
    onClose,
    destroyOnClose = false,
    afterOpenChange,
    mask = true,
    open,
    isFormChanged = false,
    styles,
    editColumnWidth,
    editColumnLeft,
    rootClassName,
    ...others
  } = props;

  const onDrawerOpenChange = (open: boolean) => {
    afterOpenChange?.(open);
  };

  return (
    <>
      {mask ? (
        <XcTableMask
          visible={open || false}
          setVisible={onClose}
          width={editColumnWidth}
          left={editColumnLeft}
          isFormChanged={isFormChanged}
        ></XcTableMask>
      ) : null}
      <Drawer
        title={false}
        closable={false}
        mask={false}
        open={open}
        width={width}
        destroyOnClose={destroyOnClose}
        afterOpenChange={onDrawerOpenChange}
        onClose={onClose}
        rootClassName={'XxcDrawer ' + rootClassName}
        {...others}
      >
        {children}
      </Drawer>
    </>
  );
});

export const DRAWER_THEME_TYPE = {
  none: 'drawer',
  normal: 'aiDrawer',
  float: 'aiFloat',
};

export function XcDrawerFloat(props: IProps) {
  const { xcTitle, children, onClose, open, afterOpenChange } = props;
  const openChange = useMemoizedFn((status: boolean) => {
    if (afterOpenChange) {
      afterOpenChange(status);
    }
  });

  useEffect(() => {
    openChange(!!open);
  }, [open]);

  return (
    <div className="XcDrawerFloat">
      <div className="XcDrawerFloat-head">
        <div className="XcDrawerFloat-head-title">{xcTitle}</div>
        <div className="XcDrawerFloat-head-close" onClick={onClose}>
          &times;
        </div>
      </div>
      {children}
    </div>
  );
}

export default (props: IProps) => {
  const { xcTheme, open } = props;
  if (xcTheme === DRAWER_THEME_TYPE.float) {
    return <XcDrawerFloat {...props} />;
  }

  return <XcDrawer {...props} open={open || false} />;
};
