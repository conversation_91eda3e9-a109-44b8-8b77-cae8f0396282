/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 23:07:58
 * @LastEditTime: 2025-01-11 17:26:23
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
  onClick?: () => void;
}

export const IconSearch = (props: IProps) => {
  const { className, onClick } = props;

  return (
    <svg
      onClick={onClick}
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="9.16667"
        cy="8.33341"
        r="5.91667"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.9216 13.0881L17.5 16.6666"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
