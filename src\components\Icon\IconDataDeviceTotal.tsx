/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 10:06:10
 * @LastEditTime: 2025-01-14 22:29:33
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconDeviceGuidlineTotal = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="36"
      height="37"
      viewBox="0 0 36 37"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M35.5 18.5002C35.5 28.1562 27.6654 35.9847 18 35.9847C8.33461 35.9847 0.5 28.1562 0.5 18.5002C0.5 8.84413 8.33461 1.01562 18 1.01562C27.6654 1.01562 35.5 8.84413 35.5 18.5002Z"
        stroke="#5A5A89"
        strokeOpacity="0.4"
      />
      <path
        d="M13 25.458C14.6667 26.6217 19 28.2509 23 25.458"
        stroke="#5A5A89"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M10.7502 16.9796C10.7502 12.989 13.9948 9.75 18.0002 9.75C22.0057 9.75 25.2502 12.989 25.2502 16.9796C25.2502 20.9702 22.0057 24.2092 18.0002 24.2092C13.9948 24.2092 10.7502 20.9702 10.7502 16.9796Z"
        stroke="#5A5A89"
        strokeWidth="1.5"
      />
      <path
        d="M14.7502 16.98C14.7502 15.1928 16.2037 13.7402 18.0002 13.7402C19.7968 13.7402 21.2502 15.1928 21.2502 16.98C21.2502 18.7673 19.7968 20.2198 18.0002 20.2198C16.2037 20.2198 14.7502 18.7673 14.7502 16.98Z"
        stroke="#5A5A89"
        strokeWidth="1.5"
      />
      <path
        d="M17.646 12.3439C17.5524 12.2505 17.5 12.1241 17.5 11.9926C17.5 11.861 17.5524 11.7347 17.646 11.6413C17.7397 11.5478 17.867 11.4951 18 11.4951C18.133 11.4951 18.2603 11.5478 18.354 11.6413C18.4476 11.7347 18.5 11.861 18.5 11.9926C18.5 12.1241 18.4476 12.2505 18.354 12.3439C18.2603 12.4373 18.133 12.49 18 12.49C17.867 12.49 17.7397 12.4373 17.646 12.3439Z"
        fill="#5A5A89"
        stroke="#5A5A89"
      />
    </svg>
  );
};
