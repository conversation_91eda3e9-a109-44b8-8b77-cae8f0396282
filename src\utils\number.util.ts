import { isNumber } from 'lodash';

/**
 * 千分位
 * @param number
 * @returns
 */
export const formatNumberWithCommas = (number: number) => {
  if (!isNumber(number)) {
    return '0';
  }
  let parts = number.toString().split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return parts.join('.');
};

export function formatDecimal(num: number, precision: number = 1) {
  const multiplier = Math.pow(10, precision);
  const fixedNum = Math.round(num * multiplier) / multiplier;
  return fixedNum % 1 === 0 ? fixedNum.toString() : fixedNum.toFixed(precision);
}
