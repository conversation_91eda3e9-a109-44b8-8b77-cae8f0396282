import { IconBar<PERSON>hart } from '@/components/Icon/IconBarChart';
import { IconBellRinging } from '@/components/Icon/IconBellRinging';
import { IconLightbulb } from '@/components/Icon/IconLightbulb';
import { IconMultipleModal } from '@/components/Icon/IconMultipleModal';
import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { memo } from 'react';
import { CardTips } from './CardTips';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-12 17:55:23
 * @LastEditTime: 2025-02-11 17:35:07
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

const StyledContainer = styled.div`
  align-items: center;
  gap: ${() => px2vh(20)};
  .section-container {
    /* sm:mb-[20px] md:mb-[40px] 2xl:mb-[60px] */
    margin-bottom: ${() => px2vh(60)};
    .section-title {
      font-size: ${() => px2font(28)};
    }
    .section-tips {
      font-size: ${() => px2font(14)};
      padding: ${() => px2vh(6)} ${() => px2vw(20)};
      border-radius: ${() => px2font(16)};
      margin-top: ${() => px2vh(20)};
    }
  }
`;
const StyledCardContainer = styled.section`
  gap: ${() => px2vw(20)};
`;

export const DefaultChat = memo((props: any) => {
  const { className } = props;
  return (
    <div className={`relative flex flex-col items-center ${className}`}>
      <StyledContainer className=" flex flex-col">
        <section className="section-container flex flex-col items-center text-[#ffffff] font-medium text-center ">
          <div className="section-title">开启您的病媒智能监测之旅</div>
          <div className="section-tips text-[#C8C8C8] bg-[#292933] font-normal">
            上传资料，体验精准快捷的监测服务
          </div>
        </section>

        <StyledCardContainer className="flex flex-row ">
          <CardTips
            icon={<IconMultipleModal />}
            title="多模态输入"
            tips="实现图片、音频、文件上传识别病媒种类"
          />
          <CardTips
            icon={<IconBarChart />}
            title="数据分析"
            tips="对上传的数据进行分析，提供相关分布、密度等信息"
          />
        </StyledCardContainer>

        <StyledCardContainer className="flex flex-row ">
          <CardTips
            icon={<IconLightbulb />}
            title="智能问答"
            tips="通过问题咨询，调用知识库回答病媒生物相关情况"
          />
          <CardTips
            icon={<IconBellRinging />}
            title="智能预警"
            tips="通过上传相关检测数据，预测可能爆发病害的目的地"
          />
        </StyledCardContainer>
      </StyledContainer>
    </div>
  );
});
