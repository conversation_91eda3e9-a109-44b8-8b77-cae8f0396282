/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-27 13:52:48
 * @LastEditTime: 2025-01-21 15:54:04
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const deviceAPI = {
  /**
   * 设备列表
   */
  deviceList: '/v1/device/list',
  /**
   * 设备开关机
   */
  deviceStatus: (id: string) => `/v1/device/${id}/status`,

  /**
   * 设备详情
   * @param id
   * @returns
   */
  deviceDetail: (id: string) => `/v1/device/${id}/detail`,
  /**
   * 设备统计
   */
  deviceStats: '/v1/device/stats',
  /**
   * 设备
   */
  deviceById: (id: string) => `/v1/device/${id}`,
  device: '/v1/device',
  /**
   * 操作日志
   */
  deviceOperatorLog: '/v1/device_log/operation',
  /**
   * mqtt日志
   */
  deviceMQTTLog: '/v1/device_log/mqtt',

  /* bigscreen */
  deviceOverView: '/v1/area/device-overview',
  deviceEnv: '/v1/area/environment',
  getDeviceTree: '/v1/system/device/tree', // get
  getAuthDeviceTree: '/v1/dashboard/device/region/list', // get

  getDeviceGroupList: '/v1/device/group/list',
  putDeviceGroupStatus: (id: string) => `/v1/device/group/${id}/status`,
  deviceGroupById: (id: string) => `/v1/device/group/${id}`,
  postDeviceGroup: '/v1/device/group',
};
