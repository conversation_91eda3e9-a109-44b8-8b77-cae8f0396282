.Chat<PERSON>ooter {
  position: relative;
  flex-shrink: 0;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(white, white) padding-box, conic-gradient(rgba(68, 177, 255, 0.2) 4%, rgba(111, 93, 255, 0.2) 81%) border-box;
  border: 2px solid transparent;
  position: relative;

  .ChatFooterInput {
    flex: 1;
    overflow: hidden;
    width: 100%;
  }

  .ChatFooterInputArea {
    color: #84909F;
    padding: 10px 20px;
    border: none;
    outline: none;
    resize: none;
    box-shadow: none;
    min-height: 92px;
    line-height: 18px;
  }

  .ChatFooterControl {
    display: flex;
    align-items: flex-end;
    height: 40px;
    margin: 0 10px 10px 20px;
    justify-content: space-between;

  }

  .ChatFooter-ability {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .ChatFooter-plus,
  .ChatFooter-deepThink,
  .ChatFooter-onlineSearch {
    width: 80px;
    height: 26px;
    cursor: pointer;
    border-radius: 6px;
    color: #84909F;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #F6F6F8;

    &:hover,
    &.is-selected {
      color: #444DFF;
      background-color: #E7E6FF;
    }

    .ChatFooter-icon {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }


  .ChatFooter-send {
    padding: 0 12px;
    height: 40px;
    border-radius: 12px;
    background: linear-gradient(122deg, rgba(68, 177, 255, 0.8) -56%, rgba(111, 93, 255, 0.8) 86%);
    color: #fff;
    font-size: 16px;
    line-height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    .ChatFooter-icon {
      width: 24px;
      height: 24px;
      margin-right: 6px;
    }

    &.is-disable {
      cursor: not-allowed;
      background: #F6F6F8;

      .ChatFooter-icon {
        color: #B2BDCA;
      }

    }
  }

  .ChatFooter-plus {
    width: 26px;
    height: 26px;

    .ChatFooter-icon {
      width: 12px;
      height: 12px;
      margin: 0;
    }
  }
}
