/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-23 23:08:57
 * @LastEditTime: 2025-01-14 22:36:15
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
  width?: number;
  height?: number;
}

export const IconWifi2 = (props: IProps) => {
  const { className, width = 24, height = 24 } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width={width} height={height} rx="8" fill="#2953FF" />
      <g clipPath="url(#clip0_118_400)">
        <path
          d="M12 15.75H12.005M17.4032 10.3504C15.9798 9.046 14.0828 8.25 11.9999 8.25C9.91707 8.25 8.02012 9.046 6.59668 10.3504M8.36597 12.1215C9.33503 11.2678 10.607 10.75 12 10.75C13.3929 10.75 14.6649 11.2678 15.634 12.1215M13.8492 13.8876C13.3396 13.4881 12.6976 13.25 11.9999 13.25C11.2917 13.25 10.6409 13.4954 10.1277 13.9058"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};
