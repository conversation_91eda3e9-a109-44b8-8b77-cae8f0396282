import { role<PERSON><PERSON> } from '@/api/role';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { ProForm } from '@ant-design/pro-components';
import { request, useRequest } from '@umijs/max';
import { Button, Form, message, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import RoleAddOrEdit from './components/AddOrEdit';
import { XcInput } from '@/components/XcInput';
import { ColumnsType } from 'antd/es/table';
import XcAntdTable from '@/components/XcAntdTable/XcAntdTable';
import { IconClose } from '@/components/Icon/IconClose';
import { IconSearch } from '@/components/Icon/IconSearch';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import { IconAdd } from '@/assets/svg';
import { XcTableNew } from '@/components/XcTable/XcTableNew';

const { Text } = Typography;
enum MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  delete = 3,
  unbind = 4, // 解绑
}
export default () => {
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [dataSource, setDataSource] = useState<any>([]);
  const formRef = useRef<any>(null);

  const roleListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: roleApi.getList,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );
  const requestTable = async (params: any) => {
    const { current, pageSize, ...others } = params;

    const queryPayload = {
      page: current,
      page_size: pageSize,
      ...others,
      tenant_id: 1,
    };
    const result = await roleListRequest.run(queryPayload);

    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: result.data.list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const closeModal = () => {
    setCurrentItem({});
    setModalType(MODAL_TYPE.none);
  };
  const onOpenModal = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };

  const showModal = async (item: any, type: MODAL_TYPE) => {
    setCurrentItem(item);
    if (type === MODAL_TYPE.edit) {
      const result = await request(roleApi.getRole(item.id), {
        method: 'GET',
        params: {
          tenant_id: 1,
        },
      });
      if (result.code === 200) {
        setCurrentItem(result.data);
      }
    }

    setModalType(type);
  };

  const onDeleteRole = async () => {
    return request(roleApi.deleteRole(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  const onUnbindRole = async () => {
    return request(roleApi.deleteBindRole(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('解绑成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.info(res.message || '解绑失败');
        return false;
      }
    });
  };
  const columns: any = [
    {
      title: '角色名称',
      width: 320,
      key: 'name',
      hideInTable: true,
      placeholder: '搜索角色名称',
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (_, item: { name: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.name }}>
            {item.name}
          </Text>
        );
      },
    },
    {
      title: '角色编号',
      dataIndex: 'code',
      key: 'code',
      link: true,
      render: (_, item: any) => {
        return item.code;
      },
    },
    {
      title: '角色用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (_, item: { user_count: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.user_count }}>
            {item.user_count}
          </Text>
        );
      },
    },

    {
      title: '操作',
      width: 154,
      link: true,
      align: 'center',
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px] w-full'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.解绑角色用户}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.unbind)}
                hidden={item.user_count <= 0}
              >
                解绑
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新角色}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.edit)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除角色}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.delete)}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={roleListRequest.loading}
        columns={columns}
        rowKey="__rowKey"
        request={requestTable}
        extend={null}
        batchRender={() => null}
        operator={
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建角色}>
            <Button
              type="primary"
              className={'h-[32px] rounded-[4px] bg-btn-blue text-txt-white text-[14px]'}
              icon={<IconAdd className="size-[16px]" />}
              onClick={() => {
                showModal({}, MODAL_TYPE.add);
              }}
            >
              新增角色
            </Button>
          </PermissionWrap>
        }
        rowSelection={null}
        searchTitle="验证日志"
      />
      {modalType === MODAL_TYPE.add || modalType === MODAL_TYPE.edit ? (
        <RoleAddOrEdit
          modalType={modalType}
          onClose={closeModal}
          onChange={requestTable}
          currentItem={currentItem}
          onOpenChange={onOpenModal}
        ></RoleAddOrEdit>
      ) : null}
      <XcModalForm
        title="确认"
        width={500}
        open={modalType === MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onDeleteRole}
      >
        <div className="my-[40px]">删除“{currentItem.name}”后，绑定该角色的用户将失去相应权限</div>
      </XcModalForm>
      <XcModalForm
        title="解绑"
        width={500}
        open={modalType === MODAL_TYPE.unbind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onUnbindRole}
      >
        <div className="my-[40px]">
          解绑“{currentItem.name}”角色后，该角色下的用户（{currentItem.user_count}
          人）将无法查看相关数据，请谨慎操作！确定解绑？
        </div>
      </XcModalForm>
    </div>
  );
};
