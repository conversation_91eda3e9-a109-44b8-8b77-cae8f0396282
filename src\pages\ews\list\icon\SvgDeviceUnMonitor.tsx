/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 10:07:41
 * @LastEditTime: 2025-01-19 00:29:28
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgDeviceUnMonitor = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="62"
      height="62"
      viewBox="0 0 62 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.3"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 31V39C0 51.7025 10.2975 62 23 62H31H39C51.7025 62 62 51.7025 62 39V31V23C62 10.2975 51.7025 0 39 0H31H23C10.2975 0 0 10.2975 0 23V31Z"
        fill="#FF9066"
      />
      <path
        d="M20.1111 41.8889H43.4444C44.3036 41.8889 45 42.5853 45 43.4444C45 44.3036 44.3036 45 43.4444 45H18.5556C17.6964 45 17 44.3036 17 43.4444V18.5556C17 17.6964 17.6964 17 18.5556 17C19.4147 17 20.1111 17.6964 20.1111 18.5556V41.8889Z"
        fill="#FF9A73"
      />
      <path
        opacity="0.5"
        d="M25.9124 35.1751C25.3249 35.8019 24.3405 35.8336 23.7137 35.246C23.0869 34.6584 23.0552 33.674 23.6428 33.0473L29.4761 26.8251C30.0444 26.2189 30.9887 26.1663 31.6207 26.7057L36.2247 30.6345L42.2233 23.0362C42.7557 22.3619 43.7339 22.2468 44.4082 22.7791C45.0825 23.3115 45.1975 24.2897 44.6652 24.964L37.6652 33.8306C37.1184 34.5232 36.1057 34.6228 35.4345 34.05L30.7305 30.0359L25.9124 35.1751Z"
        fill="#FF9A73"
      />
    </svg>
  );
};
