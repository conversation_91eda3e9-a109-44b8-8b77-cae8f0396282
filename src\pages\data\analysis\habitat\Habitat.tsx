import React from 'react';
import Panel from '@/components/Panel';
import { ReactComponent as IconHabitatRank } from '../assets/habitat-rank.svg';
import { useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import DataSearchForm from '../../components/DataSearchForm';
import { Spin } from 'antd';

export default function Habitat(props: any) {
  const { menu } = props;

  const {
    run: getHabitatRank,
    data: habitatRankRes,
    loading = true,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getHabitatRatio,
      method: 'GET',
      params: { ...data, monitor_type: data?.monitor_type || '', tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  const habitatRank = habitatRankRes?.habitats || [];

  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <IconHabitatRank />
          <span>蚊虫监测数量生境排名</span>
          <span className="text-[12px] text-[#999]">
            （该模块展示的是各蚊虫在不同生境中的分布）
          </span>
        </span>
      }
      boxStyle={{ height: 688 }}
    >
      <div className="flex items-center gap-3 mb-4">
        <DataSearchForm
          menu={menu}
          onRequest={getHabitatRank}
          allowFields={['region', 'monitor_type', 'dateRange']}
        ></DataSearchForm>
      </div>
      <div className="flex flex-col gap-y-[8px]">
        <div className="flex items-center h-[34px] gap-2  text-[#797A85] border-b border-[#F0F0F0] font-[500] text-[12px]">
          <div className="w-[32px] flex items-center justify-center">排名</div>
          <div className="flex-1">生境类型</div>
          <div className="text-right flex-shrink-0 w-[160px] ">蚊虫监测总数（只）</div>
        </div>
        {habitatRank.length > 0 ? (
          habitatRank.map((row: any, idx: any) => {
            let rankColor = '';
            if (row.rank === 1) {
              rankColor = '#ED8061';
            } else if (row.rank === 2) {
              rankColor = '#F0B261';
            } else if (row.rank === 3) {
              rankColor = '#3381F8';
            } else {
              rankColor = '#D7D9E5';
            }

            return (
              <div key={idx} className="flex items-center gap-2 h-[51px]" style={{ height: 52 }}>
                <div className="w-[32px] flex items-center justify-center">
                  <span
                    className="flex-shrink-0 w-[20px] h-[20px] flex items-center justify-center rounded-full text-white text-[14px] font-bold"
                    style={{ background: rankColor }}
                  >
                    {idx + 1}
                  </span>
                </div>
                <div className="flex-1 flex-col items-start justify-center">
                  <div className="mb-2 text-[12px] text-[#1F2134] text-one-row min-w-0">
                    {row.area_type || '-'}
                  </div>
                  <div className="bg-[#EDEFF7] rounded h-[8px]  ">
                    <div
                      className="bg-[#68DBAD] h-full rounded"
                      style={{ width: `${row.ratio}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-right  text-[#1F2134] flex-shrink-0 w-[100px] self-end">
                  {row.count}
                </div>
              </div>
            );
          })
        ) : (
          <div className="w-full h-full flex items-center justify-center text-txt-sub min-h-[300px]">
            {loading ? <Spin /> : <span>暂无可用生境数据</span>}
          </div>
        )}
      </div>
    </Panel>
  );
}
