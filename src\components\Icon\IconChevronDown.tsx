/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-11 08:56:28
 * @LastEditTime: 2025-01-14 22:28:29
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconChevronDown = (props: any) => {
  const { className } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15 7.5L10 12.5L5 7.5"
        stroke="#AAAAAA"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
