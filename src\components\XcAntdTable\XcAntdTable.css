.XcAntdTable {

  .ant-table-row-selected,
  .ant-table-cell-row-hover {
    .ant-table-cell {
      background: #fafafa !important;
    }
  }

  .ant-table-row>.ant-table-cell {
    min-height: 44px !important;
    padding: 11px 16px !important;
    box-sizing: border-box !important;

    &>div,
    &>button,
    &>p,
    &>span {
      line-height: 22px;
      vertical-align: top;
    }

    button {
      max-height: 22px;

      &.ant-table-row-expand-icon {
        max-height: 22px;
      }
    }
  }

  .ant-table-container,
  .ant-table-header,
  .ant-table-header>table {
    /* border-radius: 0px !important; */
  }

  .ant-table-thead,
  .ant-table-header {
    border-top: 1px solid var(--line-split);
    border-bottom: 1px solid var(--line-split);

    .ant-table-cell {
      background-color: #e9f2fb;
      border: none;
      color: var(--txt-main) !important;
      border-radius: 0 !important;

      &::before {
        display: none;
      }
    }
  }

  .ant-table-measure-row {
    border-bottom-color: var(--line-split);
  }

  .ant-table-body {
    table {
      overflow: hidden;
    }

    scrollbar-color: auto;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      /* 轨道背景透明 */
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #00000026;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #00000033;
    }

    td {
      border-bottom: 1px solid var(--line-split) !important;
    }
  }

  .ant-table-tbody-virtual-scrollbar-thumb {
    background: #00000026 !important;
    border-radius: 4px !important;
  }

  .ant-select-selector,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-item {
    border: 1px solid var(--line-split);
    border-radius: 4px;
    color: var(--txt-main);
    box-sizing: border-box;
  }

  .ant-pagination-item-active {
    >a {
      color: var(--txt-main) !important;
    }

    background-color: color-mix(in srgb, var(--txt-third) 15%, transparent);
  }

  .ant-pagination-options {
    height: 30px;
  }

  .ant-select-item-option-content {
    color: var(--txt-main);
  }

  .ant-pagination-total-text {
    flex: 1;
  }

  .ant-checkbox-input {
    border: 1px solid var(--line-split);
  }

  .ant-table-selection-column+.ant-table-cell {
    padding-left: 0 !important;
  }

  .ant-table-cell-row-hover {

    .ant-btn-link,
    .cursor-pointer {
      color: var(--txt-blue);

      &:hover {
        font-weight: 600;
      }
    }
  }
}
