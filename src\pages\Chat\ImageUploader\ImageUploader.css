.ImageUploader {
  margin-top: 8px;
  position: relative;
}

.ImageUploader-list {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 20px;
}

.ImageUploader-item {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 6px;
  background-color: #F6F6F8;
  cursor: pointer;
}

.ImageUploader-itemImg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ImageUploader-itemClose {
  width: 20px;
  height: 20px;
  background: url(../assets/icon-close.png) no-repeat 0 0/100% 100%;
  cursor: pointer;
  position: absolute;
  top: -8px;
  right: -10px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
}

.ImageUploader-imageInput {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  cursor: pointer;
}
