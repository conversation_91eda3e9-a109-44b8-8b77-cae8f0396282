import { ConfigProvider, Tabs } from 'antd';
import { styled } from '@umijs/max';
const StyledTabs = styled(Tabs)`
  display: flex;
  flex: 1;
  .ant-tabs-tab {
    height: 40px;
  }
  .ant-tabs-content,
  .ant-tabs-tabpane,
  .ant-tabs-content-holder {
    display: flex;
    flex: 1;
    min-width: 0;
  }
`;

export default function (props: any) {
  return (
    <ConfigProvider
      theme={{
        components: {
          Tabs: {
            itemActiveColor: '#36383A',
            itemColor: '#667085',
            itemHoverColor: '#36383A',
            inkBarColor: '#0080FF',
            itemSelectedColor: '#36383A',
          },
        },
      }}
    >
      <StyledTabs {...props}></StyledTabs>
    </ConfigProvider>
  );
}
