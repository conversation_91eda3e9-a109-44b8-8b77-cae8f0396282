/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-30 00:17:12
 * @LastEditTime: 2025-02-02 22:53:31
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { styled } from '@umijs/max';
import { ConfigProvider, DatePicker } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
const { RangePicker } = DatePicker;

const StyledRangePicker = styled(RangePicker)`
  height: 40px;
  width: 248px;
  border-radius: 8px !important;
  border: 0;
  background: #f4f4f6 !important;
  padding: 0 16px;
  .ant-picker-input {
    input {
      color: #36383a;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      line-height: 20px;
      text-align: center;
      &::placeholder {
        color: #36383a;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        line-height: 20px;
      }
    }
  }
  .ant-picker-suffix {
    margin-left: 0;
    margin-right: -6px;
  }
`;

export const XcRangePicker = (props: RangePickerProps) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          DatePicker: {},
        },
      }}
    >
      <StyledRangePicker {...props} />
    </ConfigProvider>
  );
};
