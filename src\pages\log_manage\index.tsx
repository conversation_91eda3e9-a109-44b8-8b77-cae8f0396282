import { Tabs } from 'antd';
import React, { useEffect } from 'react';
import DeviceRunLog from './DeviceRunLog';
import OperateLog from './OperateLog';
import AIVerifyLog from './AIVerifyLog';
import VerifyLog from './VerifyLog';
import HealthLog from './HealthLog';
import MQTTLog from './MqttLog';
import { useLocation, useNavigate } from '@umijs/max';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import StyledTabs from '@/components/XcAntdTabs';
import BindLog from './BindLog';

export default function EnterpriseManage() {
  const tabs = [
    {
      key: 'audit',
      code: STATIC_PERMISSION_CODE.蚊媒验证日志,
      label: '验证日志',
      children: <VerifyLog />,
    },
    {
      key: 'ai-audit',
      code: STATIC_PERMISSION_CODE.AI验证日志,
      label: 'AI验证日志',
      children: <AIVerifyLog />,
    },
    {
      key: 'operate',
      code: STATIC_PERMISSION_CODE.操作日志,
      label: '操作日志',
      children: <OperateLog />,
    },
    {
      key: 'deviceRun',
      label: '设备运行日志',
      code: STATIC_PERMISSION_CODE.设备运行日志,
      children: <DeviceRunLog />,
    },
    {
      key: 'mqtt',
      label: 'MQTT日志',
      code: STATIC_PERMISSION_CODE.MQTT日志,
      children: <MQTTLog />,
    },
    {
      key: 'health',
      label: '设备健康日志',
      code: STATIC_PERMISSION_CODE.设备健康日志,
      children: <HealthLog />,
    },
    {
      key: 'bind',
      label: '设备绑定日志',
      code: STATIC_PERMISSION_CODE.设备绑定日志,
      children: <BindLog />,
    },
  ].filter((item) => {
    return checkFunPermission(item.code as string);
  });
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location?.pathname.split('/').pop();
  const activeKey = tabs.map((item) => item.key).includes(pathname || '')
    ? pathname
    : tabs?.[0]?.key;
  useEffect(() => {
    if (!activeKey) {
      navigate('/welcome');
    }
  }, [activeKey]);
  const onChange = (key: string) => {
    navigate(`/settings/log/${key}`);
  };
  if (!activeKey) {
    return null;
  }
  return (
    <div className="flex flex-1 flex-col mt-[20px] mb-[16px]">
      {/* <StyledTabs
        defaultActiveKey={activeKey}
        activeKey={activeKey}
        onChange={onChange}
        className="text-txt-main h-[40px]"
        destroyInactiveTabPane
        items={tabs}
        tabBarStyle={{ marginBottom: 0 }}
      ></StyledTabs> */}
      {tabs?.find((item) => item.key === activeKey)?.children}
    </div>
  );
}
