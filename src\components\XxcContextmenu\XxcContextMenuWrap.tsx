import { useMemoizedFn } from 'ahooks';
import { useContextMenuStore } from '@/store/contextMenuStore';
import { getMenuListKey } from './common';
import { useRef } from 'react';
import { throttle } from 'lodash';

export default function XxcContextMenuWrap(props: any) {
  const { data, menuKey, children, align = 'center', ...others } = props;
  const showContextMenu = useContextMenuStore((state) => state.showContextMenu);
  const elementRef = useRef<HTMLElement>(null);

  const updateMenuPosition = throttle(
    () => {
      const rect = elementRef.current?.getBoundingClientRect() as DOMRect;
      const maxWidth = document.documentElement.offsetWidth;
      const maxHeight = document.documentElement.offsetHeight;
      const menuListKey = getMenuListKey(menuKey);
      const menuListElement = document.getElementById(menuListKey) as HTMLElement;
      const menuListElementRect = menuListElement?.getBoundingClientRect() as DOMRect;
      const menuHeight = menuListElementRect?.bottom - menuListElementRect?.top;
      const menuWidth = menuListElementRect?.right - menuListElementRect?.left;
      let x = rect.left + rect.width / 2;
      let y = rect?.bottom;
      if (x + menuWidth > maxWidth) {
        x = x - menuWidth;
      }
      if (y + menuHeight > maxHeight) {
        y = rect?.top - menuHeight;
      }
      showContextMenu({
        menuKey,
        data,
        position: { x, y },
      });
    },
    20,
    { leading: false, trailing: true },
  );

  const showMenu = useMemoizedFn((e: any) => {
    const rect = elementRef.current?.getBoundingClientRect();
    if (!rect) return;
    let x = rect.left + rect.width / 2;
    let y = rect.bottom;
    if (align === 'left') {
      x = rect.left;
    } else if (align === 'right') {
      x = rect.right;
    }

    e.stopPropagation();
    e.preventDefault();
    e.nativeEvent.stopImmediatePropagation();
    showContextMenu({
      menuKey,
      data,
      position: { x, y },
    });
    updateMenuPosition();
  });

  return (
    <div onContextMenu={showMenu} ref={elementRef} {...others}>
      {children}
    </div>
  );
}
