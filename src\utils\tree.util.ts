import _ from 'lodash';
import { pick } from 'lodash';

export function getTreeProperty(tree: any, property: string): string[] {
  const codes: string[] = [];
  function traverse(data: any) {
    const node = data || {};
    if (node[property]) {
      codes.push(node[property]);
    }
    if (node.children) {
      node.children.forEach((child: any) => traverse(child));
    }
  }
  if (Array.isArray(tree)) {
    tree.forEach((item) => traverse(item));
  } else {
    traverse(tree);
  }
  return codes;
}

// 递归查找父节点并添加到选中列表
export const findAndAddParentIds = (tree: any, id: number, result: any): any => {
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.id === id) {
      return result;
    }
    if (node.children) {
      const newResult = findAndAddParentIds(node.children, id, [...result, node.id]);
      if (newResult) {
        return newResult;
      }
    }
  }
  return null;
};

export const getAllChildIds = (node: any): number[] => {
  let childIds: number[] = [];
  if (node.children) {
    for (const child of node.children) {
      childIds.push(child.id);
      childIds = childIds.concat(getAllChildIds(child));
    }
  }
  return childIds;
};

export const findNodeById = (tree: any, id: number, property?: string): any => {
  let nodeTree = tree;
  if (!Array.isArray(tree)) {
    nodeTree = [tree];
  }
  const key = property || 'id';
  for (const node of nodeTree) {
    if (node[key] === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id, key);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

// 获取树的指定属性 例子：getTreeNodeWithProperty(treeData, 'id')
export function getTreeNodeWithProperty<T>(
  tree: T,
  property: string,
  fieldNames: any = { children: 'children' },
): T[] {
  const result: T[] = [];
  const children = fieldNames.children;
  // 定义递归函数来遍历树
  function traverse(node: any) {
    if (node[property]) {
      // 如果节点包含指定属性，则添加到结果数组中
      result.push(node);
    }
    if (node[children] && node[children].length > 0) {
      // 如果节点有子节点，则递归遍历子节点
      node[children].forEach((child: T) => traverse(child));
    }
  }
  // 如果传入的 tree 是一个数组，则遍历数组中的每个节点
  if (Array.isArray(tree)) {
    tree.forEach((node) => traverse(node));
  } else {
    // 如果传入的 tree 是单个节点，则直接开始遍历
    traverse(tree);
  }
  return result;
}
// 更新树的默认属性 例子：updateTreeDefaultProperty({tree: treeData, properties: ['id', 'name'], callback: (node, value, property, parent) => {
//   return value || node[property]
// }})
export function updateTreeDefaultProperty<T>(params: {
  tree: T;
  properties: string[];
  callback: (node: any, value: any, property: string, parent?: any) => any;
  fieldNames?: any;
}): T {
  const { tree, properties, fieldNames, callback } = params;
  const children = fieldNames?.children || 'children';
  // 定义递归函数来遍历树
  function traverse(node: any, parent?: any) {
    properties.forEach((property: string) => {
      node[property] = callback(node, node?.[property], property, parent);
    });
    if (node[children] && node[children].length > 0) {
      // 如果节点有子节点，则递归遍历子节点
      node[children].forEach((child: T) => traverse(child, node));
    }
  }
  // 如果传入的 tree 是一个数组，则遍历数组中的每个节点
  if (Array.isArray(tree)) {
    tree.forEach((node) => traverse(node));
  } else {
    // 如果传入的 tree 是单个节点，则直接开始遍历
    traverse(tree);
  }
  return tree;
}

// 提取树的指定属性 例子：extractKeysFromTree(treeData, ['id', 'name'])
export function extractKeysFromTree(treeData: any, keysToKeep: any) {
  // 处理空值情况
  if (!treeData) return Array.isArray(treeData) ? [] : undefined;

  // 统一转换为数组处理
  const treeList = Array.isArray(treeData) ? treeData : [treeData];

  const result = treeList.map((node) => {
    // 提取当前节点的指定属性
    const newNode = pick(node, keysToKeep);

    // 递归处理子节点
    if (node.children && Array.isArray(node.children)) {
      const children = extractKeysFromTree(node.children, keysToKeep);
      newNode.children = children;
    }

    return newNode;
  });

  // 根据输入类型返回相应结果
  return Array.isArray(treeData) ? result : result[0];
}
// global.extractKeysFromTree = extractKeysFromTree;
export function filterTreeWidthProperty(tree: any, property: string) {
  const result: any[] = [];

  function traverse(node: any) {
    if (Array.isArray(node)) {
      for (const item of node) {
        const filteredItem = traverse(item);
        if (filteredItem) {
          result.push(filteredItem);
        }
      }
    } else {
      if (!node[property]) {
        // 检查是否包含 id 属性
        const filteredChildren = node.children
          ? filterTreeWidthProperty(node.children, property)
          : null;
        return {
          ...node,
          children: filteredChildren,
        };
      }
    }
  }

  traverse(tree);
  return result;
}

// 按idkey打平一个树
export function extractTreeAsObject(tree: any, idKey = 'id') {
  const result: any = {};

  function traverse(node: any) {
    if (Array.isArray(node)) {
      node.forEach((item) => traverse(item));
    } else {
      // 创建一个不包含 children 属性的节点副本
      const { children, ...nodeWithoutChildren } = node;
      result[node[idKey]] = nodeWithoutChildren;

      // 如果有子节点，递归处理
      if (children) {
        traverse(children);
      }
    }
  }

  traverse(tree);
  return result;
}

/**
 * 根据ID合并两棵树
 * @param {Array} sourceTree 源树
 * @param {Array} targetTree 目标树
 * @param {string} idKey ID字段名，默认为'key'
 * @returns {Array} 合并后的树
 */
export function mergeTrees(targetTree: any, sourceTree: any, idKey = 'id', force = false): any {
  const idTreeMap = extractTreeAsObject(sourceTree, idKey);
  function traverse(node: any) {
    if (Array.isArray(node)) {
      node.forEach((item) => traverse(item));
    } else {
      const newNode = idTreeMap[node[idKey]];

      if (newNode) {
        if (force) {
          Object.assign(node, newNode);
        } else {
          _.mergeWith(node, newNode, (objValue, srcValue) => {
            if (objValue !== undefined) {
              return objValue;
            }
            return srcValue;
          });
        }
      }

      if (node.children) {
        traverse(node.children);
      }
    }
  }

  traverse(targetTree);

  return targetTree;
}
