/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-24 11:32:13
 * @LastEditTime: 2024-11-24 11:32:16
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export function fontSize(res: any) {
  let clientWidth =
    window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return;
  let fontSize = clientWidth / 1920;
  return res * fontSize;
}

function changeFontSize(s: any) {
  const changeList = [
    'fontSize',
    'itemWidth',
    'itemHeight',
    'symbolSize',
    'width',
    'height',
    'itemGap',
    'length',
    'top',
    'bottom',
    'left',
    'right',
    'margin',
    'size',
    'borderWidth',
    'distance',
    'radius',
  ];
  for (let i in s) {
    if (typeof s[i] === 'object') {
      changeFontSize(s[i]);
    } else {
      if (changeList.includes(i)) {
        s[i] = fontSize(s[i]);
      }
    }
  }
  return s;
}

export function calculate(options: Record<string, any>) {
  const copy = JSON.parse(JSON.stringify(options));
  return changeFontSize(copy);
}
