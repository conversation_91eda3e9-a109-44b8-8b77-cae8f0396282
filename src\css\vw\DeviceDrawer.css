.dd-drawer {
  background-color: rgba(17, 19, 41, 1) !important;
  width: 100% !important;

  .ant-drawer-body {
    overflow: hidden !important;
  }
}

.dd-drawer-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.dd-header {
  flex-shrink: 0;
  height: 28px;
  color: #fff;
  display: flex;
  justify-content: space-between;
}

.dd-title {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 8px;
  font-size: 22px;
  font-weight: 600;
}

.dd-status {
  display: flex;
  align-items: center;
  color: #68dbad;
  gap: 8px;
}

.dd-dot {
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background-color: currentColor;
}

.dd-content {
  margin-top: 20px;
  height: calc(100vh - 40px);
  padding-bottom: 20px;
}

.dd-deviceInfo {
  display: flex;
  color: rgba(255, 255, 255, 0.80);
  font-size: 16px;
  line-height: 22px;
  flex-direction: column;
  gap: 4px;
}

.dd-envInfo {
  color: rgba(255, 255, 255, 0.80);
  margin-top: 18px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
}

.dd-envProperty {
  display: flex;
  gap: 5px;
}

.dd-ewsList {
  margin: 30px auto;
  overflow: hidden;
  border-radius: 60px;
  background: rgba(238, 121, 117, 0.10);
}

.dd-ewsItem {
  width: 100%;
  height: 48px;
  overflow: hidden;
}

.dd-ewsItemCont {
  display: flex;
  padding: 0 16px;
  font-weight: 600;
  height: 48px;
  align-items: center;
  color: #68DBAD;
  gap: 6px;

  &.is-high {
    color: #EE7975;
  }

  &.is-medium {
    color: #FFA63F;
  }

  &.is-low {
    color: #68DBAD;
  }
}

.dd-ewsTime {
  flex: 1;
  text-align: right;
  flex-shrink: 0;
  min-width: 140px;
}
