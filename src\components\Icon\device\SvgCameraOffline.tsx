/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-26 22:30:26
 * @LastEditTime: 2025-01-26 22:30:54
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgCameraOffline = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.25 6.25C0.25 2.93629 2.93629 0.25 6.25 0.25H15.75C19.0637 0.25 21.75 2.93629 21.75 6.25V15.75C21.75 19.0637 19.0637 21.75 15.75 21.75H6.25C2.93629 21.75 0.25 19.0637 0.25 15.75V6.25Z"
        fill="#667085"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 5C8.05472 5 5.66663 7.3881 5.66663 10.3333C5.66663 13.2786 8.05472 15.6667 11 15.6667C13.9452 15.6667 16.3333 13.2786 16.3333 10.3333C16.3333 7.3881 13.9452 5 11 5ZM11 6.33333C11.3154 6.33333 11.5714 6.58929 11.5714 6.90476C11.5714 7.22024 11.3154 7.47619 11 7.47619C10.6845 7.47619 10.4285 7.22024 10.4285 6.90476C10.4285 6.58929 10.6845 6.33333 11 6.33333ZM11 13C9.52734 13 8.33329 11.806 8.33329 10.3333C8.33329 8.86071 9.52734 7.66667 11 7.66667C12.4726 7.66667 13.6666 8.86071 13.6666 10.3333C13.6666 11.806 12.4726 13 11 13ZM12.3333 10.3333C12.3333 11.0697 11.7363 11.6667 11 11.6667C10.2636 11.6667 9.66663 11.0697 9.66663 10.3333C9.66663 9.59695 10.2636 9 11 9C11.7363 9 12.3333 9.59695 12.3333 10.3333ZM7.9534 15.5904C7.72718 15.432 7.41541 15.487 7.25705 15.7133C7.0987 15.9395 7.15371 16.2513 7.37994 16.4096C8.5816 17.2508 11.7119 18.4453 14.6201 16.4096C14.8463 16.2513 14.9013 15.9395 14.743 15.7133C14.5846 15.487 14.2728 15.432 14.0466 15.5904C11.6215 17.288 8.97396 16.3048 7.9534 15.5904Z"
        fill="white"
      />
    </svg>
  );
};
