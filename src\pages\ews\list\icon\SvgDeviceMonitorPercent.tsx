/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 10:06:44
 * @LastEditTime: 2025-01-19 00:28:30
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
export const SvgDeviceMonitorPercent = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="62"
      height="62"
      viewBox="0 0 62 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.21"
        d="M0 22C0 9.84974 9.84974 0 22 0H40C52.1503 0 62 9.84974 62 22V40C62 52.1503 52.1503 62 40 62H22C9.84974 62 0 52.1503 0 40V22Z"
        fill="#FEC53D"
      />
      <path
        d="M28.7358 31.1818H28.7358L30.8205 32.015L30.819 33.265L30.8197 33.265C30.8199 33.265 30.8202 33.265 30.8205 33.265L41.4141 33.2777C41.3546 35.8483 40.4956 38.2433 38.9265 40.2211C38.1588 41.1888 37.2389 42.031 36.1941 42.7237L36.194 42.7237C35.1429 43.4206 33.9853 43.9476 32.7511 44.292L32.7491 44.2925C31.6587 44.5987 30.5429 44.75 29.4368 44.75C28.3379 44.75 27.2457 44.5989 26.1921 44.2999L26.1911 44.2996C24.1982 43.736 22.3491 42.6469 20.8503 41.148C19.355 39.6526 18.2654 37.8031 17.6985 35.8056L17.6985 35.8055C17.0983 33.6918 17.0992 31.4258 17.7092 29.2513L17.7097 29.2495C18.054 28.0152 18.581 26.8575 19.2779 25.8063L19.2792 25.8042C19.967 24.7623 20.8111 23.8428 21.7797 23.0741C23.7586 21.5069 26.1509 20.6456 28.7231 20.5859L28.7358 31.1818ZM41.5655 33.2778C41.565 33.2778 41.5645 33.2779 41.564 33.2778H41.5655Z"
        stroke="#FFB85B"
        strokeWidth="2.5"
      />
      <path
        d="M41.6069 20.387C42.593 21.3749 43.3664 22.5205 43.9065 23.7963L43.9065 23.7964C44.4348 25.0468 44.7185 26.369 44.7475 27.7367L34.25 27.7497V17.2525C35.6177 17.2814 36.9425 17.5648 38.1955 18.0929C39.4757 18.6335 40.6245 19.4029 41.6069 20.387Z"
        stroke="#FEC53D"
        strokeWidth="2.5"
      />
    </svg>
  );
};
