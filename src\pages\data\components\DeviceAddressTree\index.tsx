import { deviceAPI } from '@/api';
import { XcAntdTree } from '@/components/XcAntdTree';
import { updateTreeDefaultProperty } from '@/utils/tree.util';
import { request, styled } from '@umijs/max';
import { useEffect, useMemo, useState } from 'react';

interface IProps {
  onChange?: (keys: string[]) => void;
  onDataLoad?: (data: any) => void;
  visible?: boolean;
  [key: string]: any;
}
const StyledTree = styled(XcAntdTree)`
  &.ant-tree {
    flex: 1;
    background: transparent;
    color: #fff;
    font-size: 12px;

    .ant-tree-treenode {
      border-radius: 6px;
      line-height: 18px;
      height: 18px;
      overflow: hidden;
    }
    .ant-tree-switcher {
      width: 12px;
    }
    .ant-tree-node-content-wrapper {
      padding-inline: 0;
    }
    .ant-tree-switcher-noop {
      display: none;
    }
    .ant-tree-treenode-selected {
      background: var(--btn-blue);
      .ant-tree-node-content-wrapper {
        background: transparent;
        color: #fff !important;
      }
    }
    & .ant-tree-node-content-wrapper {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &:hover {
        color: #fff !important;
      }
    }
  }
`;
export default function DeviceAddressTree(props: IProps) {
  const { onChange, onDataLoad, visible, ...others } = props;
  const [deviceData, setDeviceData] = useState<any>(null);
  useEffect(() => {
    request(deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setDeviceData(list);
      }
    });
  }, []);

  const deviceOptions = useMemo(() => {
    if (!deviceData) {
      return [];
    }
    let treeData = updateTreeDefaultProperty({
      tree: deviceData,
      properties: ['id', 'devices'],
      callback: (node: any, value: any, property: string) => {
        if (property === 'devices' && value && value.length) {
          node.children = node.devices || [];
          return value;
        } else {
          if (!value) {
            return -Math.random().toString().slice(-16);
          } else {
            return value;
          }
        }
      },
    });
    onDataLoad?.(treeData);
    return treeData;
  }, [deviceData]);
  const onDeviceCheck = (checkedKeys: any, e: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys].filter((id) => id);
    onChange?.(keys);
  };

  if (!visible || !deviceData) {
    return null;
  }

  return (
    <StyledTree
      blockNode={true}
      onCheck={onDeviceCheck}
      treeData={deviceOptions}
      defaultExpandAll
      fieldNames={{
        title: 'name',
        key: 'id',
      }}
      {...others}
    />
  );
}
