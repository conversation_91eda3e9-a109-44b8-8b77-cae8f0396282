/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-30 13:37:31
 * @LastEditTime: 2025-02-05 18:38:19
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { ConfigProvider, Divider, Image, Input, Upload } from 'antd';
import { memo } from 'react';
import { DefaultChat } from './DefaultChat';
import { IconClose } from './icons/IconClose';
import { IconNavigationPointer } from './icons/IconNavigationPointer';
import { IconPhoto2 } from './icons/IconPhoto2';
import { MessageBox } from './MessageBox';
import { useXxc } from './useXxc';

const { TextArea } = Input;

export interface IMessage {
  timestamp?: number;
  nickname?: string;
  person?: 'primary' | 'secondary';
  text: string;
}

export const AIChatBox = memo(() => {
  const {
    imageUrl,
    setImgUrl,
    messageList,
    uniqueId,
    sendMsg,
    onPressEnter,
    onChangeText,
    onSendMsg,
    beforeUpload,
    qa,
  } = useXxc();

  return (
    <>
      {/* 消息区无图片的情况 100vh - 100px头部+20px底部+120px输入框 */}
      {/* 消息区有图片的情况 100vh - 100px头部+20px底部+197px输入框 */}
      <div
        className={`${imageUrl ? 'h-[calc(100vh-317px)]' : 'h-[calc(100vh-240px)]'} w-[800px] flex`}
      >
        {messageList.length === 0 ? (
          <DefaultChat />
        ) : (
          <MessageBox messageList={messageList} uniqueId={uniqueId} />
        )}
      </div>

      {/* 操作区 */}
      <div className={`w-[800px] px-[30px] ${imageUrl ? 'h-[197px]' : 'h-[120px]'}`}>
        {/* 快捷指令 */}
        {/* <div className="flex gap-[8px] text-[16px] font-normal text-white h-[36px] mb-[10px]">
          <QuickTag icon={<IconAnotation className="size-[16px]" />} title="病媒知识答疑" />
          <QuickTag icon={<IconPresentation className="size-[16px]" />} title="智能监测" />
          <QuickTag icon={<IconFileCode className="size-[16px]" />} title="智能决策" />
          <QuickTag icon={<IconFileAttachment className="size-[16px]" />} title="信息梳理" />
        </div> */}
        <div className={`relative ${imageUrl ? 'h-[197px]' : 'h-[120px]'}`}>
          {/* 输入框 */}
          <ConfigProvider
            theme={{
              components: {
                Input: {
                  colorTextPlaceholder: '#949BAA',
                  colorBgContainer: '#FEFEFE',
                  colorText: '#36383A',
                  borderRadius: 16,
                  paddingBlock: 20,
                  paddingInline: 20,
                  inputFontSize: 14,
                },
              },
            }}
          >
            <TextArea
              value={sendMsg}
              autoFocus={true}
              onChange={onChangeText}
              placeholder="输入您想了解的内容，Enter发送"
              disabled={qa === 'A'}
              onPressEnter={onPressEnter}
              classNames={{
                textarea: `!h-[100%] !align-top `,
              }}
              style={{
                boxShadow: '0px 3px 8px 0px rgba(84, 90, 128, 0.10)',
                paddingTop: `${imageUrl ? '88px' : '20px'}`,
              }}
            />
          </ConfigProvider>
          {/* 图片位置 */}
          {imageUrl && (
            <div className="absolute left-[20px] top-[20px]">
              <div className="relative size-[54px]">
                <IconClose
                  className="absolute top-[-5px] right-[-5px] z-[99]"
                  onClick={() => {
                    setImgUrl(undefined);
                  }}
                />
                <Image
                  width={54}
                  height={54}
                  src={imageUrl}
                  style={{ objectFit: 'cover', borderRadius: 10, border: '0.5px solid #000000' }}
                />
              </div>
            </div>
          )}
          {/* 按钮区 */}
          <div className="absolute bottom-[14px] right-[20px] h-[50px] flex flex-row justify-center items-center">
            <Upload
              disabled={qa === 'A'}
              beforeUpload={beforeUpload}
              maxCount={1}
              showUploadList={false}
              accept=".png, .jpg, .jpeg"
              className="w-[32px] h-[32px] flex flex-col items-center justify-center "
            >
              <IconPhoto2 className="w-[30px] h-[30px]" />
            </Upload>
            <Divider type="vertical" />
            <div
              className={`flex justify-center items-center w-[32px] h-[32px] rounded-[25px] ml-[20px] ${
                sendMsg.length > 0 ? 'bg-[#2B65F6]' : 'bg-[#DADEE8]'
              }`}
              onClick={onSendMsg}
            >
              <IconNavigationPointer className="size-[18px]" />
            </div>
          </div>
        </div>
      </div>
    </>
  );
});
