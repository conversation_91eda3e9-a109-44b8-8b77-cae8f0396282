import { XcModalForm } from '@/components/XcModal/XcModalForm';
import './DataPreview.css';
import { useMemo } from 'react';
type MonitorData = {
  start_time: string;
  end_time: string;
  monitor_metdod: string;
  density_unit: string;
  monitor_location: string;
  device_code: string;
  device_name: string;
  area_type: string;
  mosquito_details: string;
  total_count: number;
  adult_density: number;
  aedes_density: number;
  monitor_duration: number;
  temperature: string;
  humidity: string;
  wind_speed: string;
  co2_level: string;
  climate: string;
  remark: string;
  monitor_person: string;
};

const parseMosquitoDetails = (details: string) => {
  return details.split('||').map((item) => {
    const [species, count] = item.split(',');
    return { species, count: parseInt(count) };
  });
};
export default function Preview(props: any) {
  const { onClose, toggleModalVisible, data } = props;
  const reportId = data?.report_id;
  const reportData = useMemo(() => {
    return Array.isArray(data?.details) ? data.details : [];
  }, [data]);
  const labelData = reportData[0] || {};
  const mosquitoData = parseMosquitoDetails(labelData.mosquito_details || '');
  return (
    <XcModalForm
      title={'预览 ' + reportId}
      width={1200}
      open={true}
      onOpenChange={toggleModalVisible}
      onFinish={async () => {
        onClose();
        return true;
      }}
    >
      <div className="pt-[16px]">
        <table className="data-preview-table">
          <tbody>
            <tr>
              <td className="label" colSpan={18}>
                监测数据表
              </td>
            </tr>
            <tr>
              <td className="label" colSpan={2}>
                监测时段
              </td>
              <td colSpan={15}>
                {labelData.start_time} 至 {labelData.end_time}
              </td>
            </tr>
            <tr>
              <td className="label" colSpan={2}>
                监测方法
              </td>
              <td colSpan={7}>{labelData.monitor_metdod}</td>
              <td className="label" colSpan={3}>
                密度计量单位
              </td>
              <td colSpan={6}>{labelData.density_unit}</td>
            </tr>
            <tr>
              <td className="label" rowSpan={2}>
                监测地点
              </td>
              <td className="label" rowSpan={2}>
                设备名称
              </td>
              <td className="label" rowSpan={2}>
                生境类型
              </td>
              <td className="label" colSpan={5}>
                蚊种种类及数量
              </td>
              <td className="label" colSpan={4}>
                统计数据
              </td>
              <td className="label" colSpan={5}>
                环境数据
              </td>
              <td className="label" rowSpan={2}>
                备注
              </td>
            </tr>
            <tr>
              {mosquitoData.map((item, index) => (
                <td key={index} className={'label'}>
                  {item.species}
                </td>
              ))}
              <td className="label">监测总数</td>
              <td className="label">成蚊密度</td>
              <td className="label">伊蚊成蚊密度</td>
              <td className="label">有效监测时长</td>
              <td className="label">温度</td>
              <td className="label">湿度</td>
              <td className="label">风速</td>
              <td className="label">二氧化碳浓度</td>
              <td className="label">气候</td>
            </tr>
            {reportData.map((row: any, idx: any) => {
              const mosquitoArr = parseMosquitoDetails(row.mosquito_details);
              return (
                <tr key={idx}>
                  <td>{row.monitor_location}</td>
                  <td>{row.device_name}</td>
                  <td>{row.area_type}</td>
                  {mosquitoArr.map((item, index) => (
                    <td key={'mos_' + index}>{item.count}</td>
                  ))}
                  <td>{row.total_count || '0'}</td>
                  <td>{row.adult_density || '0'}</td>
                  <td>{row.aedes_density || '0'}</td>
                  <td>{row.monitor_duration || '0'}</td>
                  <td>{row.temperature}</td>
                  <td>{row.humidity}</td>
                  <td>{row.wind_speed}</td>
                  <td>{row.co2_level}</td>
                  <td>{row.climate}</td>
                  <td>{row.remark}</td>
                </tr>
              );
            })}
            <tr>
              <td className="label">监测人</td>
              <td colSpan={16}>{labelData.monitor_person}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </XcModalForm>
  );
}
