/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-22 22:48:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */

export const IconFullScreen = (props: any) => {
  const { color = '#C6C6E3', className, ...rest } = props;
  return (
    <svg
      {...rest}
      className={`xc-svg ${className ?? ''}`}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13 8.33333V9.8C13 10.9201 13 11.4802 12.782 11.908C12.5903 12.2843 12.2843 12.5903 11.908 12.782C11.4802 13 10.9201 13 9.8 13H8.33333M5.66667 1H4.2C3.0799 1 2.51984 1 2.09202 1.21799C1.71569 1.40973 1.40973 1.71569 1.21799 2.09202C1 2.51984 1 3.0799 1 4.2V5.66667M9 5L13 1M13 1H9M13 1V5M5 9L1 13M1 13H5M1 13L1 9"
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
