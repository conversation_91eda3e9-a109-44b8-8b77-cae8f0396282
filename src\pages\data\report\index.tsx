import React, { useEffect } from 'react';
import Mosquito from './mosquito';
import Monitor from './monitor';
import { useLocation, useNavigate } from '@umijs/max';
import { checkFunPermission } from '@/utils/permission';
import StyledTabs from '@/components/XcAntdTabs';

export default function EnterpriseManage() {
  const tabs = [
    {
      key: 'mosquito',
      code: '',
      label: '蚊虫成蚊监测表',
      children: <Mosquito key="mosquito" />,
    },
    {
      key: 'monitor',
      label: '监测数据表',
      code: '',
      children: <Monitor key="monitor" />,
    },
  ].filter((item) => {
    if (item.code) {
      return checkFunPermission(item.code as string);
    } else {
      return true;
    }
  });
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location?.pathname.split('/').pop();
  const activeKey = ['mosquito', 'monitor'].includes(pathname || '') ? pathname : tabs?.[0]?.key;
  useEffect(() => {
    if (!activeKey) {
      navigate('/404');
    }
  }, [activeKey]);
  const onChange = (key: string) => {
    navigate(`/data/report/${key}`);
  };
  if (!activeKey) {
    return null;
  }
  return (
    <div className="flex flex-1 flex-col mt-[20px] mb-[16px]">
      {/* <StyledTabs
        defaultActiveKey={activeKey}
        activeKey={activeKey}
        onChange={onChange}
        className="text-txt-main flex-1"
        destroyInactiveTabPane
        items={tabs}
        tabBarStyle={{ marginBottom: 0 }}
      ></StyledTabs> */}
      {tabs?.find((item) => item.key === activeKey)?.children}
    </div>
  );
}
