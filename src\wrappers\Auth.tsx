import { IInitialState } from '@/interface/shared';
import { useLayoutStore } from '@/store';
import { checkMenuPermission } from '@/utils/permission';
import { Navigate, Outlet, useModel } from '@umijs/max';
import { useMemo } from 'react';

export default function Auth() {
  const { initialState } = useModel('@@initialState');
  const { user } = initialState as IInitialState;
  const { permission } = useLayoutStore((state) => state.selectedMenu);
  const hasAuth = useMemo(() => {
    return permission?.length > 0
      ? permission.some((code: string) => checkMenuPermission(code))
      : true;
  }, [permission]);

  if (!user) {
    return <Navigate to="/user/login" />;
  } else if (hasAuth) {
    return <Outlet />;
  } else {
    return <Navigate to="/403" />;
  }
}
