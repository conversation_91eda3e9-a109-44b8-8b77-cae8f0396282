import { ThemeConfig } from 'antd';

export const customTheme: ThemeConfig = {
  token: { colorPrimary: '#0080FF' },
  components: {
    Tabs: {
      itemActiveColor: '#36383A',
      itemColor: '#667085',
      itemHoverColor: '#36383A',
      inkBarColor: '#0080FF',
      itemSelectedColor: '#36383A',
    },
    Switch: {
      handleSize: 16,
      trackHeight: 20,
      trackMinWidth: 32,
    },
  },
};

export default customTheme;
