/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-20 16:32:59
 * @LastEditTime: 2025-01-27 01:03:47
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export function convertJsonObjectToArray(obj: any, parentPath = [] as any[]) {
  if (obj.children) {
    obj.children.forEach((item: any) => {
      convertJsonObjectToArray(item, parentPath);
    });
    return;
  }

  parentPath.push({
    key: obj.key,
    label: obj.key,
    value: obj.label,
  });

  return parentPath;
}
