/* AddOrEdit 组件样式 */

/* 步骤导航容器 */
.add-or-edit-steps-container {
  padding: 24px 30px;
  background: #fafafa;
  border-bottom: 1px solid var(--line-split);
}

/* 步骤导航样式 */
.add-or-edit-steps {
  max-width: 600px;
  margin: 0 auto;
}

.add-or-edit-steps .ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--txt-main);
}

.add-or-edit-steps .ant-steps-item-description {
  font-size: 12px;
  color: var(--txt-sub);
}

.add-or-edit-steps .ant-steps-item-process .ant-steps-item-icon {
  background: var(--btn-blue);
  border-color: var(--btn-blue);
}

.add-or-edit-steps .ant-steps-item-finish .ant-steps-item-icon {
  background: var(--btn-blue);
  border-color: var(--btn-blue);
}

.add-or-edit-steps .ant-steps-item-finish .ant-steps-item-icon .ant-steps-icon {
  color: #fff;
}

.add-or-edit-steps .ant-steps-item-wait .ant-steps-item-icon {
  background: #f5f5f5;
  border-color: #d9d9d9;
}

.add-or-edit-steps .ant-steps-item-wait .ant-steps-item-icon .ant-steps-icon {
  color: #999;
}

/* 第三步完成页面样式 */
.add-or-edit-step3 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.add-or-edit-step3 .success-icon {
  margin-bottom: 24px;
}

.add-or-edit-step3 .checkmark {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #52c41a;
  color: #fff;
  font-size: 40px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: checkmarkScale 0.3s ease-in-out;
}

@keyframes checkmarkScale {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.add-or-edit-step3 .success-text {
  font-size: 18px;
  font-weight: 500;
  color: var(--txt-main);
  margin-bottom: 8px;
}

.add-or-edit-step3 .success-desc {
  font-size: 14px;
  color: var(--txt-sub);
}

/* 表单项间距调整 */
.add-or-edit-steps-container + div .ant-form-item {
  margin-bottom: 24px;
}

/* 触发类型卡片样式优化 */
.add-or-edit-steps-container + div .ant-form-item .bg-white {
  border: 1px solid var(--line-border);
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  background: #fff;
}

/* 按钮样式 */
.xc-form-footer .ant-btn {
  height: 36px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  min-width: 88px;
}

.xc-form-footer .xc-form-button-cancel {
  background: #f4f4f6;
  border: none;
  color: var(--txt-main);
}

.xc-form-footer .xc-form-button-cancel:hover {
  background: #e8e8ea;
}

.xc-form-footer .xc-form-button-submit {
  background: var(--btn-blue);
  border: none;
  color: #fff;
}

.xc-form-footer .xc-form-button-submit:hover {
  background: var(--btn-blue-hover);
}

.xc-form-footer .xc-form-button-submit.opacity-30 {
  opacity: 0.3;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-or-edit-steps-container {
    padding: 16px 20px;
  }
  
  .add-or-edit-steps {
    max-width: 100%;
  }
  
  .add-or-edit-step3 {
    padding: 40px 20px;
  }
  
  .add-or-edit-step3 .checkmark {
    width: 60px;
    height: 60px;
    font-size: 30px;
  }
}
