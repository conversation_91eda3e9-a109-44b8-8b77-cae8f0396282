.RuleAddOrEditPage {

  .Steps-container {
    margin: 20px;
  }

  .XcDrawerScroll {
    padding: 0 20px;
  }

  .ant-steps-item-icon {
    overflow: hidden;
    width: 22px !important;
    height: 22px !important;
    margin-top: -6px;
    margin-inline-start: 59px !important;
  }

  .ant-steps-item-container {
    padding-top: 11px;
  }

  .ant-steps-item-tail {
    top: 13.5px !important;
  }

  .ant-steps-item-content {
    width: 100%;
  }

  .Steps-dot {
    position: relative;
    width: 22px !important;
    height: 22px !important;

    background: rgba(246, 246, 248, 0.7);
    border-radius: 11px !important;

    &::after {
      content: '';
      position: absolute;
      left: 7px;
      top: 7px;
      width: 8px;
      height: 8px;
      background: #6265ff;
      border-radius: 4px;
    }
  }
  .ant-steps-item-finish .Steps-dot,
  .ant-steps-item-active .Steps-dot {
    background: rgba(98, 101, 255, 0.2);

    &::after {
      background: #6265ff;
    }

  }

  .Steps-step3 {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    background: #f8f9fa;
    min-height: 400px;
  }

  .Steps-step3 .success-icon {
    margin-bottom: 32px;
  }

  .Steps-step3 .checkmark {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    font-size: 48px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    animation: checkmarkScale 0.5s ease-in-out;
  }

  @keyframes checkmarkScale {
    0% {
      transform: scale(0);
      opacity: 0;
    }

    50% {
      transform: scale(1.1);
      opacity: 0.8;
    }

    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .Steps-step3 .success-text {
    font-size: 20px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 16px;
  }

  .Steps-step3 .success-desc {
    font-size: 14px;
    color: #666666;
    margin-bottom: 8px;
    line-height: 1.5;
  }

  /* 表单项间距调整 */
  .Steps-container+div .ant-form-item {
    margin-bottom: 24px;
  }

  /* 触发类型卡片样式优化 */
  .Steps-container+div .ant-form-item .bg-white {
    border: 1px solid var(--line-border);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    background: #fff;
  }

  /* 按钮样式 */
  .xc-form-footer .ant-btn {
    height: 36px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    min-width: 88px;
  }

  .xc-form-footer .xc-form-button-cancel {
    background: #f4f4f6;
    border: none;
    color: var(--txt-main);
  }

  .xc-form-footer .xc-form-button-cancel:hover {
    background: #e8e8ea;
  }

  .xc-form-footer .xc-form-button-submit {
    background: var(--btn-blue);
    border: none;
    color: #fff;
  }

  .xc-form-footer .xc-form-button-submit:hover {
    background: var(--btn-blue-hover);
  }

  .xc-form-footer .xc-form-button-submit.opacity-30 {
    opacity: 0.3;
    cursor: not-allowed;
  }

}
