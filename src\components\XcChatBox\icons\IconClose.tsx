/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-31 18:07:25
 * @LastEditTime: 2024-12-31 18:11:54
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
  onClick?: () => void;
}

export const IconClose = (props: IProps) => {
  const { className, onClick } = props;
  return (
    <svg
      onClick={onClick}
      className={className}
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_5473_25493)">
        <path
          d="M8.26748 0.62485C9.52834 0.8756 10.6865 1.4946 11.5956 2.40357C12.1992 3.00716 12.6781 3.72375 13.0048 4.51241C13.3315 5.30107 13.4996 6.14635 13.4996 7C13.4996 7.85365 13.3315 8.69894 13.0048 9.4876C12.6781 10.2763 12.1992 10.9928 11.5956 11.5964C10.6865 12.5054 9.52834 13.1244 8.26748 13.3752C7.00662 13.6259 5.69972 13.4971 4.51204 13.0052C3.32436 12.5132 2.30924 11.68 1.59504 10.6111C0.880835 9.54223 0.499634 8.28555 0.499634 7C0.499634 5.71445 0.880835 4.45777 1.59504 3.38886C2.30924 2.31996 3.32436 1.48684 4.51204 0.99485C5.69972 0.50286 7.00662 0.3741 8.26748 0.62485Z"
          fill="#202329"
          stroke="white"
        />
        <path
          d="M9.86994 9.02566C9.90927 9.06476 9.94048 9.11124 9.96177 9.16244C9.98307 9.21364 9.99403 9.26854 9.99403 9.32399C9.99403 9.37945 9.98307 9.43435 9.96177 9.48555C9.94048 9.53675 9.90927 9.58323 9.86994 9.62232L9.61636 9.8759C9.57727 9.91523 9.53078 9.94644 9.47958 9.96774C9.42838 9.98904 9.37348 10 9.31803 10C9.26258 10 9.20767 9.98904 9.15647 9.96774C9.10527 9.94644 9.05879 9.91523 9.0197 9.8759L7 7.84428L4.97434 9.86994C4.93524 9.90926 4.88876 9.94047 4.83756 9.96177C4.78636 9.98307 4.73146 9.99403 4.67601 9.99403C4.62056 9.99403 4.56565 9.98307 4.51445 9.96177C4.46325 9.94047 4.41677 9.90926 4.37768 9.86994L4.1241 9.61636C4.08477 9.57726 4.05356 9.53078 4.03226 9.47958C4.01096 9.42838 4 9.37348 4 9.31803C4 9.26257 4.01096 9.20767 4.03226 9.15647C4.05356 9.10527 4.08477 9.05879 4.1241 9.0197L6.15573 7L4.13006 4.97434C4.09074 4.93524 4.05953 4.88876 4.03823 4.83756C4.01693 4.78636 4.00597 4.73146 4.00597 4.67601C4.00597 4.62056 4.01693 4.56565 4.03823 4.51445C4.05953 4.46325 4.09074 4.41677 4.13006 4.37768L4.38364 4.1241C4.42274 4.08477 4.46922 4.05356 4.52042 4.03226C4.57162 4.01096 4.62652 4 4.68197 4C4.73743 4 4.79233 4.01096 4.84353 4.03226C4.89473 4.05356 4.94121 4.08477 4.9803 4.1241L7 6.15573L9.02566 4.13006C9.06476 4.09074 9.11124 4.05953 9.16244 4.03823C9.21364 4.01693 9.26854 4.00597 9.32399 4.00597C9.37945 4.00597 9.43435 4.01693 9.48555 4.03823C9.53675 4.05953 9.58323 4.09074 9.62232 4.13006L9.87591 4.38364C9.91523 4.42274 9.94644 4.46922 9.96774 4.52042C9.98904 4.57162 10 4.62652 10 4.68197C10 4.73743 9.98904 4.79233 9.96774 4.84353C9.94644 4.89473 9.91523 4.94121 9.87591 4.9803L7.84428 7L9.86994 9.02566Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_5473_25493">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
