import mosAjyw from '@/assets/mosquito/mos_ajyw.png';
import mosBwyw from '@/assets/mosquito/mos_bwyw.png';
import mosDskw from '@/assets/mosquito/mos_dskw.png';
import mosSdzkw from '@/assets/mosquito/mos_sdzkw.png';
import mosSsaw from '@/assets/mosquito/mos_ssaw.png';
import mosZhaw from '@/assets/mosquito/mos_zhaw.png';
import mosZjkw from '@/assets/mosquito/mos_zjkw.png';
import _ from 'lodash';

export const MOS_NAME_LIST_EN = {
  白纹伊蚊: 'aedes albopictus',
  埃及伊蚊: 'aedes aegypti',
  三带喙库蚊: 'culex tritaeniorhynchus giles',
  致倦库蚊: 'culex quinquefasciatus',
  淡色库蚊: 'culex pipiens pallens',
  中华按蚊: 'anopheles sinensis wiedemann',
  斯氏按蚊: 'subgenus stephensi liston',
  骚扰阿蚊: 'armigeres subalbatus',
};

export const MOS_NAME_LIST_ZH = _.invert(MOS_NAME_LIST_EN);

export const MOS_CONFIG_LIST = [
  {
    en: 'Aedes albopictus',
    zh: '白纹伊蚊',
  },
  {
    en: 'Aedes aegypti',
    zh: '埃及伊蚊',
  },

  {
    en: 'Culex tritaeniorhynchus Giles',
    zh: '三带喙库蚊',
  },
  {
    en: 'Culex quinquefasciatus',
    zh: '致倦库蚊',
  },
  {
    en: 'Culex pipiens pallens',
    zh: '淡色库蚊',
  },
  {
    en: 'Anopheles sinensis Wiedemann',
    zh: '中华按蚊',
  },
  {
    en: 'Subgenus stephensi Liston',
    zh: '斯氏按蚊',
  },
];

export const MOS_COLOR: Record<string, string> = {
  'aedes albopictus': '#1770FF',
  'aedes aegypti': '#33FF2E',
  'culex tritaeniorhynchus giles': '#FF2DFF',
  'culex quinquefasciatus': '#FF0004',
  'culex pipiens pallens': '#FFFF5A',
  'anopheles sinensis wiedemann': '#2CFFF3',
  'subgenus stephensi liston': '#FF9900',
  other: '#999999',
  //
  白纹伊蚊: '#1770FF',
  埃及伊蚊: '#33FF2E',
  三带喙库蚊: '#FF2DFF',
  致倦库蚊: '#FF0004',
  淡色库蚊: '#FFFF5A',
  中华按蚊: '#2CFFF3',
  斯氏按蚊: '#FF9900',
  其他: '#999999',
};

export const MOS_COLOR_BIG_SCREEN: Record<string, string> = {
  白纹伊蚊: '#735CFE',
  中华按蚊: '#E27E20',
  致倦库蚊: '#2784FF',
  三带喙库蚊: '#9F8CF7',
  淡色库蚊: '#ED6D95',
  埃及伊蚊: '#2A50EB',
  斯氏按蚊: '#FDF679',
  骚扰阿蚊: '#82DDFB',
  其他: '#78C49D',
};

export const MOS_COLOR_OVERVIEW: Record<string, string> = {
  白纹伊蚊: '#70A3FF',
  埃及伊蚊: '#FC91AD',
  三带喙库蚊: '#4C84FF',
  致倦库蚊: '#68DBAD',
  淡色库蚊: '#9F8CF7',
  中华按蚊: '#FFB16D',
  斯氏按蚊: '#82DDFB',
  骚扰阿蚊: '#FFDD8D',
  其他: '#8D99FF',
};
// code 转换中文
export const MOS_ZH: Record<string, any> = {
  mosqutio: '蚊媒',
  rodents: '鼠类',
  flies: '苍蝇',
  cockroaches: '蟑螂',
  cx: '库蚊属',
  ae: '伊蚊属',
  an: '按蚊属',
  'culex tritaeniorhynchus giles': '三带喙库蚊',
  'culex quinquefasciatus': '致倦库蚊',
  'culex pipiens pallens': '淡色库蚊',
  'aedes albopictus': '白纹伊蚊',
  'aedes aegypti': '埃及伊蚊',
  'anopheles sinensis wiedemann': '中华按蚊',
  'subgenus stephensi liston': '斯氏按蚊',
};

// NOTE 大小写问题
export const firstClassMap: Record<string, any>[] = [
  {
    value: 'mosqutio',
    label: '蚊媒',
  },
];
// NOTE 大小写问题
export const secondClassMap: Record<string, any> = {
  mosqutio: [
    {
      value: 'Ae',
      label: '伊蚊',
    },
    {
      value: 'CX',
      label: '库蚊',
    },
    {
      value: 'An',
      label: '按蚊',
    },
  ],
};
// NOTE 大小写问题
export const thirdClassMap: Record<string, any> = {
  Ae: [
    {
      value: 'Aedes albopictus',
      label: '白纹伊蚊',
    },
    {
      value: 'Aedes aegypti',
      label: '埃及伊蚊',
    },
  ],
  CX: [
    {
      value: 'Culex tritaeniorhynchus Giles',
      label: '三带喙库蚊',
    },
    {
      value: 'Culex quinquefasciatus',
      label: '致倦库蚊',
    },
    {
      value: 'Culex pipiens pallens',
      label: '淡色库蚊',
    },
  ],
  An: [
    {
      value: 'Anopheles sinensis Wiedemann',
      label: '中华按蚊',
    },
    {
      value: 'Subgenus stephensi Liston',
      label: '斯氏按蚊',
    },
  ],
};
export const MOS_REVERSE_MAP: Record<string, any> = {
  三带喙库蚊: {
    name: '三带喙库蚊',
    code: 'Culex tritaeniorhynchus Giles',
    url: mosSdzkw,
    sn: 'SN/T 5042.7-2018',
    species: '库蚊',
    temperature: '18-28摄氏度',
    humidity: '65-85%',
    physicality: '中小型; 喙部暗色约中部有白环, 各足附节1-4有窄的基白环',
    geo: [
      {
        title: '地理分布：',
        content:
          '遍布亚洲温带、亚热带地区，在中国主要分布在长江流域以南的热带、亚热带地区，以广东、广西、云南、福建等省份最为常见',
      },
      {
        title: '环境偏好：',
        content:
          '适应性强，偏好水质较清洁、植被丰富的水域，易出现在稻田、池塘、沟渠、水坑、地下室等有积水的场所',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '流行性乙型脑炎(最主要的媒介)，也能传播登革热和丝虫病',
      },
      {
        title: '防治介绍：',
        content:
          '搞好环境卫生是关键，主要包括清除积水、整治沟渠、合理灌溉稻田、定期更换花盆水，结合使用生物防治（如放养食蚊鱼）和化学防治，个人防护要做好，建议使用纱窗和蚊帐，外出时涂抹驱蚊产品',
      },
    ],
    parent: {
      name: '库蚊',
      code: 'CX',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  致倦库蚊: {
    name: '致倦库蚊',
    code: 'Culex quinquefasciatus',
    url: mosZjkw,
    sn: 'SN/T 5042.5-2018',
    species: '库蚊',
    temperature: '22-28摄氏度',
    humidity: '65-85%',
    physicality: '中型；胸腹部有白色环斑，翅膀暗褐色',
    geo: [
      {
        title: '地理分布：',
        content: '主要分布于亚洲热带和亚热带地区，在中国分布广泛，以华南、华东和华中地区较多',
      },
      {
        title: '环境偏好：',
        content:
          '喜欢在阴暗潮湿的环境活动，常见于水田、沟渠、树洞等积水处，易出现在住宅区周边的地下室、下水道、积水坑、稻田边缘、沟渠等场所',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '是日本脑炎、丝虫病等疾病的重要传播媒介，也可传播脑膜炎和某些病毒性疾病',
      },
      {
        title: '防治介绍：',
        content:
          '清除积水和改善排水系统等环境治理，配合安装纱窗纱门、使用蚊帐等物理隔离，同时可采用杀虫剂、驱蚊药物进行化学防治，或引入食蚊鱼等天敌进行生物防治，外出时建议使用驱蚊喷剂并避开蚊虫活跃时段',
      },
    ],
    parent: {
      name: '库蚊',
      code: 'CX',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  淡色库蚊: {
    name: '淡色库蚊',
    code: 'Culex pipiens pallens',
    url: mosDskw,
    sn: 'SN/T 5042.6-2018',
    species: '库蚊',
    temperature: '20-28摄氏度',
    humidity: '65-85%',
    physicality: '中小型；喙暗色，腹部背板有基带',
    geo: [
      {
        title: '地理分布：',
        content: '主要分布于亚洲的温带和亚热带地区，在中国分布较广，以南方地区为主',
      },
      {
        title: '环境偏好：',
        content:
          '喜欢在阴凉潮湿的环境活动，偏好于水域附近和植被茂密区域，易出现在稻田、水沟、灌溉渠、池塘边缘、下水道等积水处',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '是流行性乙型脑炎的重要传播媒介，同时也可传播丝虫病和其他蚊媒传染病',
      },
      {
        title: '防治介绍：',
        content:
          '合理管理农田水田灌溉、及时清理沟渠和下水道积水、定期投放食蚊鱼进行生物防治、科学使用农药防治、加强个人防护措施如使用蚊帐和驱蚊剂',
      },
    ],
    parent: {
      name: '库蚊',
      code: 'CX',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  白纹伊蚊: {
    name: '白纹伊蚊',
    code: 'Aedes albopictus',
    url: mosBwyw,
    sn: 'SN/T 5042.9-2018',
    species: '伊蚊',
    temperature: '20-30摄氏度',
    humidity: '70-80%',
    physicality: '中型；黑色体型，中胸盾片有白色纵条，腹部有基白带，足的跗节有基白环',
    geo: [
      {
        title: '地理分布：',
        content: '广泛分布于亚洲、非洲和大洋洲的热带、亚热带地区，在中国除西藏、青海外均有分布',
      },
      {
        title: '环境偏好：',
        content:
          "喜欢在小型积水容器中孳生，属于'半家蚊'，易出现在竹林的竹桶、树洞、石穴等积水容器，以及人居周边的积水处",
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '流行性乙型脑炎(最主要的媒介)脑膜炎、荨麻疹、西尼罗病毒(在美洲地区)',
      },
      {
        title: '防治介绍：',
        content:
          '及时清除各类积水容器、定期检查清理室内外积水、使用生物防治方法如天敌、合理使用化学药剂、加强个人防护如使用驱蚊剂和蚊帐',
      },
    ],
    parent: {
      name: '伊蚊',
      code: 'Ae',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  埃及伊蚊: {
    name: '埃及伊蚊',
    code: 'Aedes aegypti',
    url: mosAjyw,
    sn: 'SN/T 5042.8-2018',
    species: '伊蚊',
    temperature: '25-35摄氏度',
    humidity: '75-85%',
    physicality: '中型；黑色体表有银白色鳞片斑纹',
    geo: [
      {
        title: '地理分布：',
        content:
          '主要分布在全球热带和亚热带地区，在中国主要分布于海南、广东、广西和云南等南方省份，特别是热带和亚热带沿海地区',
      },
      {
        title: '环境偏好：',
        content:
          '适应城市环境，喜欢在人工容器中产卵和繁殖，易出现在花瓶、轮胎、水桶等积水容器，以及建筑物阴暗角落和室内环境等场所',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '登革热（主要媒介）、黄热病、基孔肯雅热、寨卡病毒',
      },
      {
        title: '防治介绍：',
        content:
          '清除室内外积水容器、定期更换花瓶水、保持室内通风、使用纱窗和蚊帐、使用驱蚊产品物理灭杀：电蚊拍、灭蚊灯、化学防治：喷洒杀虫剂或投放灭蚊药片',
      },
    ],
    parent: {
      name: '伊蚊',
      code: 'Ae',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  中华按蚊: {
    name: '中华按蚊',
    code: 'Anopheles sinensis Wiedemann',
    url: mosZhaw,
    sn: 'SN/T 5042.2-2018',
    species: '按蚊',
    temperature: '18-28摄氏度',
    humidity: '60-80%',
    physicality: '中型；体色暗褐，翅膀上有暗色斑点，腹部有深色环带',
    geo: [
      {
        title: '地理分布：',
        content: '主要分布在中国、日本、韩国等东亚地区，在中国以长江流域及其以南地区较为常见',
      },
      {
        title: '环境偏好：',
        content:
          '喜欢在清洁的流动水体或静水中繁殖，易出现在山涧溪流、池塘、水田、引水渠、河流边缘等清洁水域',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '是疟疾的主要传播媒介，也可能传播丝虫病等其他蚊媒疾病',
      },
      {
        title: '防治介绍：',
        content:
          '及时清理水生植物和水域杂物、改善灌溉系统排水、引入天敌鱼类进行生物防治、合理使用杀虫剂进行化学防治、加强个人防蚊措施如使用蚊帐和防蚊喷剂',
      },
    ],
    parent: {
      name: '按蚊',
      code: 'An',
      parent: {
        code: 'mosqutio',
        name: '蚊媒',
      },
    },
  },
  斯氏按蚊: {
    name: '斯氏按蚊',
    code: 'Subgenus stephensi Liston',
    url: mosSsaw,
    species: '按蚊',
    temperature: '22-30摄氏度',
    humidity: '65-85%',
    physicality: '中型；翅膀上有4个黑色斑块，腹部有暗色环纹，足部有白色环带',
    geo: [
      {
        title: '地理分布：',
        content: '主要分布于亚洲东南部，在中国主要分布于南方地区，以广东、广西、海南等地较为常见',
      },
      {
        title: '环境偏好：',
        content:
          '喜欢在清洁、有植被的水域环境繁殖，易出现在林地水塘、山溪、稻田、沟渠等清洁水域，尤其是有水生植物的地方',
      },
    ],
    prevent: [
      {
        title: '携带疾病：',
        content: '是恶性疟疾的主要传播媒介，也可传播其他蚊媒传染病',
      },
      {
        title: '防治介绍：',
        content:
          '定期清理积水和水生杂物、改善农田灌溉系统、投放食蚊鱼等生物防治、科学使用杀虫剂、加强个人防护如使用蚊帐和驱蚊药剂',
      },
    ],
    parent: {
      name: '按蚊',
      code: 'An',
      parent: {
        code: 'mosquito',
        name: '蚊媒',
      },
    },
  },
};

export const sickDataSource = {
  top: [
    {
      code: 'mosquito',
      level: '1',
      name: '蚊媒',
    },
    {
      code: 'Rodents',
      level: '1',
      name: '鼠类',
    },
    {
      code: 'Flies',
      level: '1',
      name: '苍蝇',
    },
    {
      code: 'Cockroaches',
      level: '1',
      name: '蟑螂',
    },
  ],
  second: [
    {
      code: 'CX',
      level: '2',
      name: '库蚊属',
      parent_code: 'mosquito',
    },
    {
      code: 'Ae',
      level: '2',
      name: '伊蚊属',
      parent_code: 'mosquito',
    },
    {
      code: 'An',
      level: '2',
      name: '按蚊属',
      parent_code: 'mosquito',
    },
  ],
  third: [
    {
      code: 'Culex tritaeniorhynchus Giles',
      level: '3',
      parent_code: 'CX',
      name: '三带喙库蚊',
      snt: 'SN/T 5042.7-2018',
    },
    {
      code: 'Culex quinquefasciatus',
      level: '3',
      parent_code: 'CX',
      name: '致倦库蚊',
      snt: 'SN/T 5042.5-2018',
    },
    {
      code: 'Culex pipiens pallens',
      level: '3',
      parent_code: 'CX',
      name: '淡色库蚊',
      snt: 'SN/T 5042.6-2018',
    },
    {
      code: 'Aedes albopictus',
      level: '3',
      parent_code: 'Ae',
      name: '白纹伊蚊',
      snt: 'SN/T 5042.9-2018',
    },
    {
      code: 'Aedes aegypti',
      level: '3',
      parent_code: 'Ae',
      name: '埃及伊蚊',
      snt: 'SN/T 5042.8-2018',
    },
    {
      code: 'Anopheles sinensis Wiedemann',
      level: '3',
      parent_code: 'An',
      name: '中华按蚊',
      snt: 'SN/T 5042.2-2018',
    },
    {
      code: 'Subgenus stephensi Liston',
      level: '3',
      parent_code: 'An',
      name: '斯氏按蚊',
      snt: '',
    },
  ],
};
