import { useModel, useRequest } from '@umijs/max';
import { memo, useCallback, useEffect, useState } from 'react';
import { BigScreenAPI } from '@/api';
import { isBlank } from '@/utils/string.util';
import { omitBy } from 'lodash';
import { filterTreeWidthProperty, getTreeNodeWithProperty } from '@/utils/tree.util';
import { TreeSelect } from 'antd';

import '@/css/vw/BigScreenTree.css';
import { useMemoizedFn } from 'ahooks';

const BigScreenTree = memo(() => {
  const { setRegion, menu } = useModel('useMenu', (m) => ({
    setRegion: m.setRegion,
    menu: m.menu,
  }));

  const { setRegionDeviceList, cityDeviceList } = useModel('useDevice', (m) => ({
    setRegionDeviceList: m.setRegionDeviceList,
    cityDeviceList: m.cityDeviceList,
  }));

  const regionDeviceListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: BigScreenAPI.regionDevicelist,
      params: omitBy({ ...data, tenant_id: 1 }, isBlank),
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { data: [], code: 200, msg: '' },
    },
  );

  const getMenuData = useCallback(async () => {
    const result = await regionDeviceListRequest.run();
    if (result.code === 200) {
      setRegionDeviceList(result.data.list);
    }
  }, []);
  // 排除设备
  const newMenu = filterTreeWidthProperty(menu, 'id');
  // 查询租户区域设备列表
  const onClickItem = useMemoizedFn(async (value: any, node: any) => {
    const item = node || { key: '' };
    const [province, city, district, street, device_id] = (item.value || value).split('.');
    // const device_code = item.code || '';
    let criteria = {
      province: province,
      city: city,
      district: district,
      street: street,
      device_id: device_id,
      // device_code: device_code,
    };
    setRegion(criteria);
  });

  useEffect(() => {
    getMenuData();
  }, []);

  const [defaultExpandKey, setDefaultExpandKey] = useState('');

  useEffect(() => {
    const keyList = Object.keys(cityDeviceList);
    let maxCityLength = 0;
    let nodeKey = '';
    keyList.forEach((key: any) => {
      const length = cityDeviceList[key].length;
      if (length > maxCityLength) {
        maxCityLength = length;
        nodeKey = key;
      }
    });
    if (nodeKey) {
      const node = {
        key: nodeKey,
        value: nodeKey,
      };
      setDefaultExpandKey(node?.value || '');
      setTimeout(() => {
        onClickItem(node.value, node);
      }, 1000);
    }
  }, [cityDeviceList, onClickItem]);
  if (!menu?.length || !defaultExpandKey) {
    return null;
  }

  return (
    <div className="BigScreenTree">
      <TreeSelect
        fieldNames={{
          label: 'key',
          value: 'key',
          children: 'children',
        }}
        listHeight={480}
        className="BigScreenTree-tree"
        popupClassName="BigScreenTree-tree-popup"
        treeData={newMenu}
        onSelect={onClickItem}
        defaultValue={defaultExpandKey}
        treeDefaultExpandAll
        multiple={false}
        placeholder="全部区域"
        showSearch
      />
    </div>
  );
});

export default BigScreenTree;
