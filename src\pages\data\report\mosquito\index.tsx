import { IColumns } from '@/components/XcTable/interface/search.interface';
import { DATE_FMT } from '@/utils/time.util';
import { request, useRequest } from '@umijs/max';
import { Button, message, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { downloadWithToken } from '@/utils/common.util';
import _ from 'lodash';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import { reportApi } from '@/api/report';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import PermissionWrap from '@/components/PermissionWrap';
import Preview from './Preview';

const { Text } = Typography;

enum MODAL_TYPE {
  none = 0,
  // add = 1,
  preview = 2,
}

export default function MosquitoSheet() {
  const tableRef = useRef<ITableRef>(null);
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});

  const userListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: reportApi.getList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, org_ids, role_ids, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };

    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await userListRequest.run(queryPayload);

    if (result.code === 200) {
      const list = Array.isArray(result.data.list)
        ? result.data.list.map((item: any) => {
            return {
              ...item,
              __rowKey: item.report_id + '_' + item.device_code,
            };
          })
        : [];
      return {
        total: result.data.total || 0,
        data: list,
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const closeModal = () => {
    setModalType(MODAL_TYPE.none);
  };
  const toggleModalVisible = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };

  // 修改--start
  const showModal = (item: any, type: MODAL_TYPE) => {
    setCurrentItem(item);
    setModalType(type);
  };
  const reportDetailRef = useRef<any>([]);
  const previewMosquito = (item: any) => {
    request(reportApi.getReportPreview(item.report_id), {
      method: 'get',
      params: {
        tenant_id: 1,
      },
    }).then((res) => {
      reportDetailRef.current = res?.data || {};
      showModal(item, MODAL_TYPE.preview);
    });
  };

  const columns: IColumns[] = [
    {
      title: '全部状态',
      width: 120,
      key: 'download_status',
      hideInTable: true,
      initialValue: '0',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        { label: '全部状态', value: '0' },
        { label: '未下载', value: '1' },
        { label: '已下载', value: '2' },
      ],
    },

    {
      title: '创建时间',
      key: 'range_time',
      realtime: true,
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '设备名称',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '请输入设备名称',
    },
    {
      title: '报表编号',
      dataIndex: 'report_id',
      key: 'report_id',
      link: true,
      render: (value, item: any) => {
        return (
          <Text
            className="text-txt-main font-normal hover:text-txt-blue  cursor-pointer"
            ellipsis={{ tooltip: value }}
            onClick={() => {
              // showModal(item, MODAL_TYPE.edit);
            }}
          >
            {value}
          </Text>
        );
      },
    },
    // {
    //   title: '设备编号',
    //   dataIndex: 'device_code',
    //   key: 'device_code',
    //   hideInSearch: true,
    //   width: 130,
    //   render: (value) => {
    //     return (
    //       <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
    //         {value}
    //       </Text>
    //     );
    //   },
    // },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      width: 200,
      render: (value) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
            {value || '-'}
          </Text>
        );
      },
    },
    {
      title: '监测时段',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (value: any, item: any) => {
        const time = item.start_time + '-' + item.end_time;
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: time }}>
            {time}
          </Text>
        );
      },
    },
    {
      title: '生成时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
      render: (value: any) => {
        const txt = dayjs(value).format('YYYY.MM.DD HH:mm:ss');
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: txt }}>
            {txt}
          </Text>
        );
      },
    },
    {
      title: '下载状态',
      dataIndex: 'download_status',
      key: 'download_status',
      render: (value: number) => {
        return +value === 2 ? '已下载' : '未下载';
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 104,
      className: 'flex items-center justify-center',
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-center gap-[16px]'}>
            <Button
              type="link"
              className="text-txt-main px-[0px] cursor-pointer"
              onClick={() => previewMosquito(item)}
            >
              预览
            </Button>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.下载蚊虫成蚊监测表}>
              <Button
                type="link"
                download
                className="text-txt-main px-[0px] hover:text-txt-blue"
                onClick={() => {
                  downloadWithToken(
                    reportApi.downloadAdult(item.report_id),
                    `${item.report_id}_.xls`,
                  );
                }}
              >
                下载
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex">
      <XcTableNew
        ref={tableRef}
        loading={userListRequest.loading}
        columns={columns}
        request={requestTable}
        extend={null}
        batchRender={() => null}
        rowSelection={null}
        operator={null}
        rowKey="report_id_device_code"
        searchTitle="蚊虫成蚊监测表"
      />
      {MODAL_TYPE.preview === modalType && (
        <Preview
          onClose={() => setModalType(MODAL_TYPE.none)}
          toggleModalVisible={toggleModalVisible}
          data={reportDetailRef.current}
        />
      )}
    </div>
  );
}
