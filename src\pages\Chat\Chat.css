.Chat {
  display: flex;
  height: calc(100vh - 66px - 16px);
  width: 100%;
  gap: 20px;
  word-break: break-all;
  word-wrap: break-word;
}

.ChatCenterBox {
  flex: 1;
  display: flex;
  justify-content: center;
}

.ChatCenterBox-content {
  width: 844px;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-shrink: 0;
  padding: 36px 0;
  box-sizing: border-box;
}

.ChatCenterBox-messageList {
  overflow-x: hidden;
  flex: 1;
  word-break: break-all;
  word-wrap: break-word;
  margin-right: -8px;
}

.ChatAside {
  position: relative;
  height: 100%;
  padding: 16px 0;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
}

.ChatAside-content {
  width: 100%;
  transition: width 1.5s ease;
}

.ChatAside-content.is-aiFloat {
  position: relative;
  max-width: 1200px;
  flex: 1;
  z-index: 99;
  background: #fff;
  border-radius: 16px;
  height: 100%;
}

.ChatAside-drawer.is-aiDrawer {
  position: relative
}
