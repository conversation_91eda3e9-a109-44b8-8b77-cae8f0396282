.ChatTaskView {
  display: flex;
  min-height: 38px;
  margin: 12px 0;
  flex-shrink: 0;
}

.ChatTaskView-list {
  display: flex;
  gap: 12px;
  font-size: 14px;
  color: #505663;
  flex: 1;
  overflow: hidden;
}

.ChatTaskView-item {
  width: 108px;
  height: 38px;
  border-radius: 6px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  flex-shrink: 0;

  &.is-selected {
    color: #3437FF;
    background-color: rgba(98, 101, 255, .2);
  }
}

.ChatTaskView-record {
  width: 108px;
  height: 38px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #DEE9F3;
  flex-shrink: 0;
  align-self: flex-end;
}

.ChatTaskView-recordIcon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
  background: url(../assets/icon-task-record.svg) no-repeat 0 0/100% 100%;
}

.ChatTaskView-recordTitle {
  font-size: 14px;
  color: #505663;
}
