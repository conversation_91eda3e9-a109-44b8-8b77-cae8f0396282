/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-15 15:12:52
 * @LastEditTime: 2025-01-14 22:34:24
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  className?: string;
  width?: number;
  height?: number;
}

export const IconLogo = (props: IProps) => {
  const { className, height = 31, width = 34 } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.3032 6.7463C13.3914 4.19586 12.0486 1.97621 13.9745 1.30846C16.5888 0.396483 18.7349 -0.932835 19.4265 0.968399C20.3569 3.56212 21.6687 5.71994 19.7552 6.40933L16.2477 7.66136C12.8642 8.86702 13.5961 14.7717 14.5048 17.3036C16.7966 23.6967 7.13307 27.1158 4.83503 20.7505C4.38094 19.473 4.45295 18.0681 5.0353 16.8434C5.61766 15.6186 6.66296 14.6737 7.9425 14.2152C8.90079 14.2152 15.3793 9.76045 14.3032 6.7463Z"
        fill="#002E6F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M25.898 13.2877C25.4297 15.7609 26.3353 22.0303 29.0954 22.562C31.6974 23.0628 34.3676 23.0628 33.9613 25.0352C33.3938 27.87 33.4434 30.2566 31.4803 29.8887C28.8846 29.3972 26.2361 29.3786 26.6144 27.4156L27.3215 23.7615C27.9976 20.2404 22.527 17.8723 19.8785 17.3622C19.2159 17.2352 18.585 16.9791 18.022 16.6085C17.459 16.2379 16.9749 15.7602 16.5974 15.2028C16.22 14.6453 15.9567 14.019 15.8225 13.3599C15.6884 12.7008 15.6861 12.0217 15.8158 11.3617C17.0873 4.70587 27.1913 6.64421 25.898 13.2877Z"
        fill="#0C65C5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.95866 27.5427C6.04828 29.7314 4.85739 31.8089 3.36569 30.5043C1.1948 28.6092 -0.895452 27.4407 0.397775 25.932C1.88638 24.1947 3.42461 21.604 4.98765 22.9704L7.8036 25.4127C10.5141 27.7529 15.318 24.2163 17.0764 22.1852C17.9703 21.1596 19.2362 20.5298 20.5958 20.4341C21.9555 20.3385 23.2975 20.7848 24.3271 21.6751C29.4659 26.1392 22.7114 33.8461 17.5912 29.4037C18.3665 29.4161 10.3094 24.8469 7.95866 27.5427Z"
        fill="#58A7DF"
      />
    </svg>
  );
};
