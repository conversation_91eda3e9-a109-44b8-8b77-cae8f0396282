/**
 * 判断是否是base64图片
 * @param str
 * @returns
 */
export const isBaseImg = (str: string) => {
  return (
    str.startsWith('data:image/png;base64') ||
    str.startsWith('data:image/jpeg;base64') ||
    str.startsWith('data:image/jpg;base64')
  );
};

export const getBase64 = (file: any, callback: any) => {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(file);
};
