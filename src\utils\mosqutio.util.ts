import {
  MOS_COLOR_BIG_SCREEN,
  MOS_COLOR_OVERVIEW,
  MOS_NAME_LIST_ZH,
  MOS_ZH,
} from '@/constants/mosquitor';

export function getAllMosqutio(list: any) {
  const mqlist: any = [];
  if (Array.isArray(list)) {
    list.forEach((element: any) => {
      if (Array.isArray(element.children)) {
        element.children.forEach((item: any) => {
          mqlist.push(item);
        });
      }
    });
  }
  return mqlist;
}

export function getMosColor(name: string, type: 'overview' | 'big-screen' = 'overview') {
  const newName = (name || '').replace(/\s+/g, ' ').toLocaleLowerCase();
  const zhName = MOS_NAME_LIST_ZH[newName] || '其他';
  return type !== 'big-screen' ? MOS_COLOR_OVERVIEW[zhName] : MOS_COLOR_BIG_SCREEN[zhName];
}

export function getMosName(name: string) {
  const newName = (name || '').replace(/\s+/g, ' ').toLocaleLowerCase();

  return MOS_NAME_LIST_ZH[newName] || name;
}

export function isMainMos(name: string) {
  const newName = (name || '').replace(/\s+/g, ' ').toLocaleLowerCase();
  return MOS_NAME_LIST_ZH[newName];
}
