import deviceIcon from '@/pages/data/BigScreen/assets/mp-device-icon.svg';
import deviceIconGray from '@/pages/data/BigScreen/assets/map-device-icon-gray.svg';
export function createXcPoint() {
  const properties = this.properties;
  const div = document.createElement('div');
  // console.log('this.properties ', JSON.stringify(this.properties));
  div.className = `circle-container js-device-point ${properties.status === 1 ? 'is-active' : ''}`;
  div.style.width = '100px';
  div.style.height = '100px';
  div.dataset.device_id = properties.id;
  div.innerHTML = `
    <div class="center-dot"></div>
    <div class="device-icon" style="background-image: url(${properties.status === 1 ? deviceIcon : deviceIconGray})"></div>
    ${
      properties.status === 1
        ? `
          <div class="ring ring-1"></div>
        `
        : ''
    }
    `;

  div.onclick = function () {
    window.postMessage(
      {
        type: 'deviceClick',
        device: properties,
      },
      '*',
    );
  };

  return div;
}
