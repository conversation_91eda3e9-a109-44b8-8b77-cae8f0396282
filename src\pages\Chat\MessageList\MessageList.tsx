import { useChatStore } from '@/store';
import MessageItem from '../MessageItem/MessageItem';
import WelcomeMessage from '../StructMessage/WelcomeMessage/WelcomeMessage';
import './MessageList.css';

export default function MessageList() {
  const list = useChatStore((state) => state.messageList);

  return (
    <div className="MessageList">
      {list.length === 0 && <WelcomeMessage />}

      {list.map((item, index) => {
        return <MessageItem key={index} message={item} />;
      })}
    </div>
  );
}
