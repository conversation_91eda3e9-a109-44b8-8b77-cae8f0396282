import React, { lazy, Suspense } from 'react';
import { CHAT_EVENT_NAME_MAP } from '@/services/chatService';

export const loadComponent = (name: string) => {
  switch (name) {
    case CHAT_EVENT_NAME_MAP.device_list:
      return lazy(() => import('@/pages/device/list/index'));
    case CHAT_EVENT_NAME_MAP.device_add:
      return lazy(() => import('@/pages/device/list/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.device_edit:
      return lazy(() => import('@/pages/device/list/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.device_delete:
      break;
    case CHAT_EVENT_NAME_MAP.user_add:
      return lazy(() => import('@/pages/user_manage/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.user_edit:
      return lazy(() => import('@/pages/user_manage/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.user_delete:
      break;
    case CHAT_EVENT_NAME_MAP.role_add:
      return lazy(() => import('@/pages/role_manage/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.role_edit:
      return lazy(() => import('@/pages/role_manage/components/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.role_delete:
      break;
    case CHAT_EVENT_NAME_MAP.org_add:
      return lazy(() => import('@/pages/org_manage/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.org_edit:
      return lazy(() => import('@/pages/org_manage/AddOrEdit'));
    case CHAT_EVENT_NAME_MAP.org_delete:
      break;
    default:
      return null;
  }
};

export const AsyncComponent = (props: Record<string, any> & { componentName: string }) => {
  const { componentName, ...others } = props;
  const Component = loadComponent(componentName);
  console.log('🚀 ~ AsyncComponent ~ componentName:', componentName, others);
  return (
    <Suspense fallback={<div>加载中...</div>}>{Component && <Component {...others} />}</Suspense>
  );
};
