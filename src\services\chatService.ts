import { useChatStore } from '@/store';

export const CHAT_EVENT_NAME_MAP = {
  device_add: 'device_add',
  device_edit: 'device_edit',
  device_delete: 'device_delete',
  device_list: 'device_list',

  user_add: 'user_add',
  user_edit: 'user_edit',
  user_delete: 'user_delete',
  user_list: 'user_list',

  role_add: 'role_add',
  role_edit: 'role_edit',
  role_list: 'role_list',
  role_delete: 'role_delete',

  org_add: 'org_add',
  org_edit: 'org_edit',
  org_list: 'org_list',
  org_delete: 'org_delete',
};
export function getInstructFromText(value: string) {
  const isAddDevice = /(创建|新增)设备/.test(value);
  if (isAddDevice) {
    return CHAT_EVENT_NAME_MAP.device_add;
  }
  const isEditDevice = /(修改|编辑)设备/.test(value);
  if (isEditDevice) {
    return CHAT_EVENT_NAME_MAP.device_edit;
  }
  const isDeviceList = /设备(数据|统计|列表|管理)/.test(value);
  if (isDeviceList) {
    return CHAT_EVENT_NAME_MAP.device_list;
  }
  const isDeviceDelete = /删除设备/.test(value);
  if (isDeviceDelete) {
    return CHAT_EVENT_NAME_MAP.device_delete;
  }

  const isAddUser = /(创建|新增)用户/.test(value);
  if (isAddUser) {
    return CHAT_EVENT_NAME_MAP.user_add;
  }
  const isEditUser = /(修改|编辑)用户/.test(value);
  if (isEditUser) {
    return CHAT_EVENT_NAME_MAP.user_edit;
  }
  const isUserList = /用户(数据|统计|列表|管理)/.test(value);
  if (isUserList) {
    return CHAT_EVENT_NAME_MAP.user_list;
  }
  const isUserDelete = /删除用户/.test(value);
  if (isUserDelete) {
    return CHAT_EVENT_NAME_MAP.user_delete;
  }
  const isAddRole = /(创建|新增)角色/.test(value);
  if (isAddRole) {
    return CHAT_EVENT_NAME_MAP.role_add;
  }
  const isEditRole = /(修改|编辑)角色/.test(value);
  if (isEditRole) {
    return CHAT_EVENT_NAME_MAP.role_edit;
  }
  const isRoleList = /角色(数据|统计|列表|管理)/.test(value);
  if (isRoleList) {
    return CHAT_EVENT_NAME_MAP.role_list;
  }
  const isRoleDelete = /删除角色/.test(value);
  if (isRoleDelete) {
    return CHAT_EVENT_NAME_MAP.role_delete;
  }

  const isAddOrg = /(创建|新增)部门/.test(value);
  if (isAddOrg) {
    return CHAT_EVENT_NAME_MAP.org_add;
  }
  const isEditOrg = /(修改|编辑)部门/.test(value);
  if (isEditOrg) {
    return CHAT_EVENT_NAME_MAP.org_edit;
  }
  const isOrgList = /部门(数据|统计|列表|管理)/.test(value);
  if (isOrgList) {
    return CHAT_EVENT_NAME_MAP.org_list;
  }
  const isOrgDelete = /删除部门/.test(value);
  if (isOrgDelete) {
    return CHAT_EVENT_NAME_MAP.org_delete;
  }

  return null;
}

export function sendMessage(value: string) {
  useChatStore.setState({
    messageList: [
      ...useChatStore.getState().messageList,
      { id: Date.now(), role: 'user', content: value },
    ],
  });
}

const chatService = {
  getInstructFromText,
  sendMessage,
};

export default chatService;
