/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 10:07:12
 * @LastEditTime: 2025-01-19 00:29:04
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const SvgDeviceMonitor = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className}`}
      {...rest}
      width="62"
      height="62"
      viewBox="0 0 62 62"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        opacity="0.21"
        d="M0 22C0 9.84974 9.84974 0 22 0H40C52.1503 0 62 9.84974 62 22V40C62 52.1503 52.1503 62 40 62H22C9.84974 62 0 52.1503 0 40V22Z"
        fill="#4AD991"
      />
      <circle cx="31" cy="31" r="13.75" stroke="#4AD991" strokeWidth="2.5" />
      <path
        d="M18 35.0833H23.6426L25.8735 28.9836C25.9956 28.6498 26.4597 28.6291 26.6111 28.9506L30.44 37.0835C30.5946 37.4118 31.0712 37.3813 31.1826 37.036L35.0028 25.1985C35.1226 24.8272 35.6488 24.8296 35.7652 25.202L38.8534 35.0833H43"
        stroke="#4AD991"
        strokeWidth="2.5"
        strokeLinecap="round"
      />
    </svg>
  );
};
