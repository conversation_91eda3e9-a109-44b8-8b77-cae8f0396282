.ChartMessage {
  background: rgba(246, 246, 248, 0.7);
  padding: 8px;
  border-radius: 16px;
}

.ChartMessage-content {
  background: #fff;
  border-radius: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ChartMessage-title {
  font-size: 14px;
  height: 46px;
  line-height: 46px;
  color: #84909F;
  border-bottom: 1px solid #E5E5EF;
  margin: 0 24px;
}

.ChartMessage-body {
  flex: 1;
}
