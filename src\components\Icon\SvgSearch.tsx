/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-27 11:11:01
 * @LastEditTime: 2025-01-27 11:18:49
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

interface IProps {
  className?: string;
  color?: string;
}

export const SvgSearch = (props: IProps) => {
  const { className, color = '667085' } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="11"
        cy="10"
        r="7.25"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.7061 15.7058L21.0002 19.9999"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
