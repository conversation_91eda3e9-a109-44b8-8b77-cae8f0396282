import React, { useMemo } from 'react';
import AreaRankingPanel from './AreaRankingPanel';
import TrendPanel from './TrendPanel';
import CompositionPanel from './CompositionPanel';
import HabitatRankingPanel from './HabitatRankingPanel';
import GrowthRatePanel from './GrowthRatePanel';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import useMosquitoModel from '@/models/useMosquito';

const DashboardPage: React.FC<any> = (props) => {
  const { range, updatePage } = props;
  const [startTime, endTime] = getRangeByTimeUnitAndFormat(TIME_UNITS.MONTH, DATE_FMT.DATE_TIME);
  const { mosquitoList } = useMosquitoModel();
  const mosquitoType = useMemo(() => {
    return mosquitoList.map((item: any) => item.code).join(',') || '';
  }, [mosquitoList]);

  return (
    <div className="flex-1 flex flex-col gap-[20px]">
      {/* 第一行 */}
      <div className="flex flex-1 min-h-0 gap-[20px]">
        <div className="flex-1 min-w-0">
          <AreaRankingPanel
            startTime={startTime}
            endTime={endTime}
            range={range}
            updatePage={updatePage}
            mosquitoType={mosquitoType}
          />
        </div>
        <div className="flex-1 min-w-0">
          <TrendPanel
            startTime={startTime}
            endTime={endTime}
            range={range}
            mosquitoType={mosquitoType}
          />
        </div>
      </div>
      {/* 第二行 */}
      <div className="flex flex-1 min-h-0 gap-[20px]">
        <div className="flex-1 min-w-0">
          <CompositionPanel
            startTime={startTime}
            endTime={endTime}
            range={range}
            mosquitoType={mosquitoType}
          />
        </div>
        <div className="flex-1 min-w-0">
          <HabitatRankingPanel
            startTime={startTime}
            endTime={endTime}
            range={range}
            mosquitoType={mosquitoType}
          />
        </div>
      </div>
      {/* 第三行 */}
      <div className="flex-1 min-h-0">
        <GrowthRatePanel
          startTime={startTime}
          endTime={endTime}
          range={range}
          mosquitoType={mosquitoType}
        />
      </div>
    </div>
  );
};

export default DashboardPage;
