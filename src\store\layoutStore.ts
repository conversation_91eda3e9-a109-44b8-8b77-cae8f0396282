import { create } from 'zustand';

export interface LayoutState {
  layoutMode: LAYOUT_MODE;
  setLayoutMode: (mode: LAYOUT_MODE) => void;
  setSelectedMenu: (menu: any) => void;
  selectedMenu: any;
}
export enum LAYOUT_MODE {
  normal = 'normal',
  ai = 'ai',
}
const layoutState = {
  layoutMode: location.href.includes('/chat') ? LAYOUT_MODE.ai : LAYOUT_MODE.normal,
  selectedMenu: '',
};

export const useLayoutStore = create<LayoutState>((set) => ({
  ...layoutState,
  setLayoutMode: (mode: LAYOUT_MODE) => set({ layoutMode: mode }),
  setSelectedMenu: (menu: any) => set({ selectedMenu: menu }),
}));
