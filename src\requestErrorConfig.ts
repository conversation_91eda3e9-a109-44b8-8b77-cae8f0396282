﻿import type { RequestOptions } from '@@/plugin-request/request';
import { history, type RequestConfig } from '@umijs/max';
import { message } from 'antd';
import { ERROR_CODE } from './constants/request';

// 错误处理方案： 错误类型
// enum ErrorShowType {
//   SILENT = 0,
//   WARN_MESSAGE = 1,
//   ERROR_MESSAGE = 2,
//   NOTIFICATION = 3,
//   REDIRECT = 9,
// }
// 与后端约定的响应数据格式
interface ResponseStructure {
  code: number;
  data: any;
  msg?: string;
  message?: string;
}

export interface ResponseErrorInfo {
  name: string;
  info: ResponseStructure;
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const errorConfig: RequestConfig = {
  baseURL: '/api',
  timeout: 5 * 60 * 1000,
  // 错误处理： umi@3 的错误处理方案。
  errorConfig: {
    // 错误抛出
    errorThrower: (res) => {
      const { code, data, msg } = res as unknown as ResponseStructure;
      if (code !== 200) {
        if (code === 10006) {
          history.push('/user/login');
          return;
        }

        const error: any = new Error(msg);
        error.name = 'BizError';
        error.info = { msg, data };
        throw error; // 抛出自制的错误
      }
    },
    // 错误接收及处理
    errorHandler: (error: any) => {
      console.log('🚀 ~ error:', error);
      if (error.code === 'ERR_BAD_RESPONSE') {
        message.error('系统错误，请联系技术支持。');
        // history.push('/500');
        return;
      }
      if (error.code === 'ERR_NETWORK') {
        message.error('网络异常，请检查网络连接。');
        return;
      }

      // if (opts?.skipErrorHandler) throw error;
      // // 我们的 errorThrower 抛出的错误。
      // if (error.name === 'BizError') {
      //   const errorInfo: ResponseStructure | undefined = error.info;
      //   if (errorInfo) {
      //     const { errorMessage, errorCode } = errorInfo;
      //     switch (errorInfo.showType) {
      //       case ErrorShowType.SILENT:
      //         // do nothing
      //         break;
      //       case ErrorShowType.WARN_MESSAGE:
      //         message.warning(errorMessage);
      //         break;
      //       case ErrorShowType.ERROR_MESSAGE:
      //         message.error(errorMessage);
      //         break;
      //       case ErrorShowType.NOTIFICATION:
      //         notification.open({
      //           description: errorMessage,
      //           message: errorCode,
      //         });
      //         break;
      //       case ErrorShowType.REDIRECT:
      //         // TODO: redirect
      //         break;
      //       default:
      //         message.error(errorMessage);
      //     }
      //   }
      // } else if (error.response) {
      //   // Axios 的错误
      //   // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
      //   message.error(`Response status:${error.response.status}`);
      // } else if (error.request) {
      //   // 请求已经成功发起，但没有收到响应
      //   // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
      //   // 而在node.js中是 http.ClientRequest 的实例
      //   message.error('None response! Please retry.');
      // } else {
      //   // 发送请求时出了点问题
      //   message.error('Request error, please retry.');
      // }
    },
  },
  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      const data = localStorage.getItem('token');
      if (data) {
        const { access_token, token_type } = JSON.parse(data);
        const header = { Authorization: `${token_type} ${access_token}` };

        return { ...config, headers: header };
      }

      return { ...config };
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // console.log('🚀 ~ response:', response);
      // 拦截响应数据，进行个性化处理
      const { status, data } = response as unknown as any;

      if (status !== 200) {
        message.error('请求失败！');
        return response;
      }
      if (ERROR_CODE.NONE_LOGIN === data?.code) {
        history.push('/user/login');
      }

      if (ERROR_CODE.NONE_PERMISSION === data?.code) {
        // message.error('无权限访问');
        history.push('/welcome');
      }

      if (data?.code !== 200) {
        // if (data.msg) {
        //   message.error(data.msg);
        // }
      }
      return response;
    },
  ],
};
