/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-23 20:38:06
 * @LastEditTime: 2025-01-29 17:47:20
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @Description:
 */
/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

import { STATIC_PERMISSION_CODE } from '../src/utils/permission';
import _ from 'lodash';

export const ALL_ROUTES = [
  {
    path: '/data',
    name: '数据',
    icon: 'data',
    permission: [STATIC_PERMISSION_CODE.数据权限],
    routes: [
      {
        path: '/data/overview',
        name: '监测总览',
        icon: 'DataOverview',
        component: './data/overview',
        permission: [STATIC_PERMISSION_CODE.监测总览],
      },
      {
        path: '/data/monitor',
        name: '监测大屏',
        icon: 'DataMonitor',
        component: './data/BigScreen',
        permission: [STATIC_PERMISSION_CODE.数字监测中心],
      },
      {
        path: '/data/audit',
        name: '监测记录',
        icon: 'Audit',
        component: './data/audit',
        permission: [STATIC_PERMISSION_CODE.监测记录],
      },
      {
        path: '/data/report',
        name: '报表中心',
        icon: 'Chart',
        permission: [STATIC_PERMISSION_CODE.监测数据报表],
        routes: [
          {
            path: '/data/report',
            exact: true,
            hide: true,
            redirect: '/data/report/mosquito',
          },
          {
            name: '蚊虫成蚊监测表',
            path: '/data/report/mosquito',
            component: './data/report',
            permission: [STATIC_PERMISSION_CODE.蚊虫成蚊监测表],
            layout: false,
          },
          {
            name: '监测数据表',
            path: '/data/report/monitor',
            component: './data/report',
            permission: [STATIC_PERMISSION_CODE.监测数据报表],
            layout: false,
          },
        ],
      },
      {
        path: '/data/analysis',
        name: '数据分析',
        icon: 'DataAnalysis',
        permission: [STATIC_PERMISSION_CODE.数据分析],
        routes: [
          {
            path: '/data/analysis',
            exact: true,
            hide: true,
            redirect: '/data/analysis/region',
          },
          {
            name: '蚊虫高发区域',
            path: '/data/analysis/region',
            permission: [STATIC_PERMISSION_CODE.地域与构成分布],
            component: './data/analysis',
            layout: false,
          },
          {
            name: '蚊虫密度与数量',
            path: '/data/analysis/density',
            permission: [STATIC_PERMISSION_CODE.密度与活跃分布],
            component: './data/analysis',
            layout: false,
          },
          {
            name: '蚊虫增长率',
            path: '/data/analysis/growth',
            permission: [STATIC_PERMISSION_CODE.增长率],
            component: './data/analysis',
            layout: false,
            hide: true,
          },
          {
            name: '蚊虫高发生境',
            path: '/data/analysis/habitat',
            permission: [STATIC_PERMISSION_CODE.生境分布],
            component: './data/analysis',
            layout: false,
          },
          {
            name: '环境与数量',
            path: '/data/analysis/environment',
            permission: [STATIC_PERMISSION_CODE.环境分布],
            component: './data/analysis',
            layout: false,
          },
        ],
      },
    ],
  },
  {
    path: '/ews',
    name: '预警',
    permission: [STATIC_PERMISSION_CODE.预警记录],
    routes: [
      {
        name: '预警中心',
        icon: 'Ews',
        path: '/ews',
        permission: [STATIC_PERMISSION_CODE.预警记录],
        component: './ews/list',
      },
    ],
  },
  {
    path: '/device',
    name: '设备',
    permission: [STATIC_PERMISSION_CODE.设备管理],
    routes: [
      {
        path: '/device',
        exact: true,
        hide: true,
        redirect: '/device/manage/list',
      },
      {
        path: '/device/manage',
        name: '设备管理',
        icon: 'Device',
        routes: [
          {
            path: '/device/manage',
            exact: true,
            hide: true,
            redirect: '/device/manage/list',
          },
          {
            path: '/device/manage/group',
            name: '监测网清单',
            component: './device/group',
          },
          {
            path: '/device/manage/list',
            name: '设备管理',
            component: './device/list',
          },
        ],
      },
      // {
      //   path: '/device/log',
      //   name: '运行日志',
      //   icon: 'Log',
      //   hide: true,
      //   component: './device/log',
      //   permission: [STATIC_PERMISSION_CODE.设备运行日志],
      // },
    ],
  },
  {
    path: '/settings',
    name: '设置',
    permission: [
      STATIC_PERMISSION_CODE.企业管理,
      STATIC_PERMISSION_CODE.日志管理,
      STATIC_PERMISSION_CODE.预警规则,
    ],
    routes: [
      {
        name: '规则管理',
        icon: 'Rule',
        path: '/settings/rule/list',
        component: './rule/index',
        permission: [STATIC_PERMISSION_CODE.预警规则],
      },
      {
        name: '企业管理',
        icon: 'Enterprise',
        path: '/settings/enterprise',
        permission: [STATIC_PERMISSION_CODE.企业管理],
        routes: [
          {
            path: '/settings/enterprise',
            hide: true,
            exact: true,
            redirect: '/settings/enterprise/user',
          },
          {
            name: '用户管理',
            path: '/settings/enterprise/user',
            layout: false,
            component: './enterprise',
            permission: [STATIC_PERMISSION_CODE.用户管理],
          },
          {
            name: '部门管理',
            layout: false,
            path: '/settings/enterprise/org',
            component: './enterprise',
            permission: [STATIC_PERMISSION_CODE.部门管理],
          },
          {
            name: '角色管理',
            layout: false,
            path: '/settings/enterprise/role',
            component: './enterprise',
            permission: [STATIC_PERMISSION_CODE.角色管理],
          },
        ],
      },
      {
        name: '日志管理',
        icon: 'Log',
        path: '/settings/log',
        permission: [STATIC_PERMISSION_CODE.日志管理],
        routes: [
          {
            path: '/settings/log',
            exact: true,
            hide: true,
            redirect: '/settings/log/audit',
          },
          {
            path: '/settings/log/audit',
            name: '验证日志',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.蚊媒验证日志],
          },
          {
            path: '/settings/log/ai-audit',
            name: 'AI验证日志',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.AI验证日志],
          },
          {
            name: '操作日志',
            path: '/settings/log/operate',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.操作日志],
          },
          {
            name: '设备运行日志',
            layout: false,
            path: '/settings/log/deviceRun',
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.设备运行日志],
          },
          {
            path: '/settings/log/mqtt',
            name: 'MQTT日志',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.MQTT日志],
          },
          {
            path: '/settings/log/health',
            name: '设备健康日志',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.设备健康日志],
          },
          {
            path: '/settings/log/bind',
            name: '设备绑定日志',
            layout: false,
            component: './log_manage',
            permission: [STATIC_PERMISSION_CODE.设备绑定日志],
          },
        ],
      },
    ],
  },
  {
    path: '/user',
    layout: false,
    hide: true,
    routes: [
      {
        path: '/user',
        redirect: '/user/login',
      },
      {
        name: '登录',
        layout: false,
        path: '/user/login',
        component: './user/logins',
      },
    ],
  },
  {
    path: '/',
    hide: true,
    routes: [
      {
        path: '/',
        hide: true,
        redirect: '/welcome',
      },
      {
        path: '/welcome',
        name: '列表页',
        hide: true,
        component: './welcome',
      },
    ],
  },
  {
    name: 'AI对话',
    path: '/chat',
    hide: true,
    component: './Chat',
  },
  {
    path: '/500',
    layout: false,
    hide: true,
    component: './500',
  },
  {
    path: '/404',
    layout: false,
    hide: true,
    component: './404',
  },
  {
    path: '/map',
    layout: false,
    hide: true,
    component: './map/map',
  },
  {
    path: '/403',
    hide: true,
    routes: [
      {
        path: '/403',
        hide: true,
        component: './403',
      },
    ],
  },
  {
    path: '*',
    hide: true,
    routes: [
      {
        path: '*',
        hide: true,
        redirect: '/welcome',
      },
    ],
  },
];

export default _.cloneDeep(ALL_ROUTES);
