import { useEffect, useRef, useState } from 'react';
import { But<PERSON>, message, Space } from 'antd';
import { orgApi } from '@/api';
import { useRequest } from '@umijs/max';
import { ProFormInstance, ProFormText } from '@ant-design/pro-components';
import { NAME_REG_EXP } from '@/constants/regex';
import { compareArrayIdList } from '@/utils/common.util';
import XcAntdForm from '@/components/XcAntdForm';
import XcDrawer from '@/components/XcDrawer';
import XcOrgTreeSelect from './XcOrgTreeSelect';
import { GLOBAL_MODAL_TYPE } from '@/constants/common';

export const OrgAddOrEdit = (props: any) => {
  const { modalType, currentItem, onChange: queryTableData, visible, onClose, xcTheme } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  useEffect(() => {
    formRef.current?.setFieldsValue(currentItem);
  }, [currentItem]);
  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      formRef.current?.setFieldsValue(currentItem);
    } else {
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
  };
  /**
   * 新增请求接口
   */
  const orgAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: orgApi.postOrg,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const orgEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: orgApi.putOrg(id),
      data: { ...currentItem, ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await orgEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success(result.message || '修改成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  // 提交新增部门
  const onFinishAdd = async (item: any) => {
    try {
      const result = await orgAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增部门成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };
  return (
    <>
      <XcDrawer
        xcTheme={xcTheme}
        title={false}
        closable={false}
        mask={true}
        maskClosable={false}
        open={drawerVisible}
        width={600}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px] w-[600px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}部门`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            {modalType === GLOBAL_MODAL_TYPE.edit ? null : (
              <ProFormText
                name="code"
                label={'部门编号'}
                className="h-[40px]"
                rules={[
                  {
                    required: true,
                    message: '请填写部门编号',
                  },
                  {
                    pattern: NAME_REG_EXP,
                    message: '请填写正确的部门编号',
                  },
                ]}
              ></ProFormText>
            )}
            <ProFormText
              name="name"
              label={'部门名称'}
              className="h-[40px]"
              rules={[
                {
                  required: true,
                  message: '请填写部门名称',
                },
                {
                  pattern: NAME_REG_EXP,
                  message: '请填写正确的部门名称',
                },
              ]}
            ></ProFormText>
            {currentItem.parent_id ? (
              <XcOrgTreeSelect currentItem={currentItem} disabled></XcOrgTreeSelect>
            ) : (
              <ProFormText
                label={'上级部门'}
                className="h-[40px]"
                disabled
                initialValue={'最高级组织架构'}
                placeholder={'最高级组织架构'}
              ></ProFormText>
            )}
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              保存
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
};

export default OrgAddOrEdit;
