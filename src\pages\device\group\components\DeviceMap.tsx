import { regxUtil } from '@/utils/regx.util';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import deviceIcon from '@/pages/data/BigScreen/assets/mp-device-icon.svg';
import deviceIconGray from '@/pages/data/BigScreen/assets/map-device-icon-gray.svg';
import icon from './assets/icon.svg';
import _ from 'lodash';
import { getBoundaryPoints, sortPolygonPoints } from '@/components/BaiduMap/util';
import { CityListControl, CustomOverlay, Map, Polygon } from 'react-bmapgl';
import '@/css/vw/CircleRing.css';
import { useModel } from '@umijs/max';

const DeviceOverlay = (props: any) => {
  const { toggleDevice } = props;
  const { gps, status, checked } = props.data;
  const overlayRef = useRef<any>(null);
  const [lng, lat] = (gps || '').split(',');

  const clickDevice = useMemoizedFn(() => {
    toggleDevice(props.data);
  });

  if (!lng || !lat) return null;
  return (
    <CustomOverlay position={new BMapGL.Point(+lng, +lat)} ref={overlayRef} enableMassClear>
      <div
        className={'circle-container js-device-point' + (checked ? ' is-checked' : '')}
        onClick={clickDevice}
      >
        <div className="center-dot"></div>
        <div
          className="device-icon"
          style={{ backgroundImage: `url(${status === 1 ? deviceIcon : deviceIconGray})` }}
        ></div>
        {status === 1 ? <div className="ring ring-1"></div> : ''}
      </div>
    </CustomOverlay>
  );
};

const DeviceMap = (props: any) => {
  const { use2d = false, address, onChange } = props;
  const bmapRef = useRef<any>(null);
  const gpsSetRef = useRef<any>(new Set<string>());
  const [checkedDevice, setCheckedDevice] = useState<any[]>([]);
  const maskRef = useRef<any>(null);
  const polygonRef = useRef<any>([]);
  const [deviceList, setDeviceList] = useState<any>([]);
  const { cityDeviceList, provinceDeviceList } = useModel('useDevice', (m) => ({
    cityDeviceList: m.cityDeviceList,
    provinceDeviceList: m.provinceDeviceList,
  }));

  const showPolygon = useMemoizedFn((list?: any) => {
    if (!bmapRef.current) return;
    const points = list.map((item: any) => {
      const [lng, lat] = item.gps.split(',');
      return new BMapGL.Point(+lng, +lat);
    });
    const sortList = sortPolygonPoints(points);
    if (polygonRef.current) {
      bmapRef.current.removeOverlay(polygonRef.current);
    }
    if (sortList.length >= 3) {
      let polygon = new window.BMapGL.Polygon(sortList, {
        strokeColor: 'blue',
        strokeWeight: 2,
        strokeOpacity: 1,
        fillColor: '#f70',
        fillOpacity: 1,
        zIndex: 1000, // 使用 zIndex 控制层级
      });
      polygonRef.current = polygon;
      bmapRef.current.addOverlay(polygon);
    }
  });

  const renderCityMask = async (city: string) => {
    const simplified = await getBoundaryPoints(city);
    if (!bmapRef.current) return;
    maskRef.current = new window.BMapGL.MapMask(simplified, {
      isBuildingMask: true,
      isPoiMask: true,
      isMapMask: true,
      showRegion: 'inside',
      topFillColor: '#eee',
      topFillOpacity: 0.5,
      sideFillColor: '#eee',
      sideFillOpacity: 0.9,
    });
    bmapRef.current.addOverlay(maskRef.current);
  };

  const deviceClickHandler = useMemoizedFn((e) => {
    if (e.data.type === 'deviceClick') {
      const gps = e.data?.device?.gps || '';
      if (gps) {
        gpsSetRef.current.add(e.data?.device);
      }
    }
  });

  useEffect(() => {
    window.addEventListener('message', deviceClickHandler);
    return () => {
      window.removeEventListener('message', deviceClickHandler);
    };
  }, []);

  useEffect(() => {
    if (!address) return;

    const [province, city] = address.split('.');
    if (bmapRef.current) {
      bmapRef.current.clearOverlays();
      bmapRef.current.centerAndZoom(city, 11);
    }
    let devices: any = [];
    if (province) {
      devices = provinceDeviceList[province] || [];
      setDeviceList(devices);
    } else if (city) {
      devices = cityDeviceList[`${province}.${city}`] || [];
      setDeviceList(devices);
    }
    renderCityMask(city);

    return () => {
      setCheckedDevice([]);
      showPolygon([]);
    };
  }, [address]);

  const center = useMemo(() => {
    if (deviceList.length) {
      const [lng, lat] = deviceList[0].gps?.split(',') || [];
      return { lng: +lng, lat: +lat };
    }
    return { lng: 112.69372738813117, lat: 22.739690000276422 };
  }, [deviceList]);

  const toggleDevice = useMemoizedFn((device) => {
    const list = [...checkedDevice];
    const index = list.findIndex((item: any) => item.id === device.id);
    if (index > -1) {
      device.checked = false;
      list.splice(index, 1);
    } else {
      device.checked = true;
      list.push({ ...device });
    }
    const deviceIds = list.map((item: any) => item.id);

    showPolygon(list);
    setCheckedDevice(list);
    onChange?.(deviceIds);
  });

  return (
    <div className="w-full p-[12px] bg-[rgba(246,246,248,0.7)] rounded-[4px]">
      <div className="flex items-center h-[28px]">
        <img src={icon} alt="" className="size-[16px]" />
        <div className="ml-[6px] text-[14px] text-[#313747]">监测网设备</div>
        <div className="text-[12px] text-[#999]">
          （在地图上直接点击选中多个设备，设备将自动围合成一个区域网）
        </div>
      </div>
      <div className="map-container w-full h-[50vh] mt-[12px] min-h-[400px]">
        <Map
          zoom={11}
          enableScrollWheelZoom
          enableDragging
          center={center}
          mapStyleV2={use2d ? { styleId: '' } : { styleId: 'f775090aa3e983de0ddfb12bda62dc8c' }}
          style={{
            height: '100%',
          }}
          ref={(ref) => (bmapRef.current = ref?.map)}
        >
          {/* @ts-ignore */}
          {/* <CityListControl /> */}
          {deviceList.map((item: any, index: number) => {
            return (
              <DeviceOverlay
                key={index}
                data={item}
                map={bmapRef.current}
                toggleDevice={toggleDevice}
              />
            );
          })}
        </Map>
      </div>
    </div>
  );
};

export default DeviceMap;
