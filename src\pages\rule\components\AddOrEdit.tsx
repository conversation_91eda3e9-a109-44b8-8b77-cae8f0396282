import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Space, Steps } from 'antd';
import { request, useRequest } from '@umijs/max';
import {
  ProFormDigit,
  ProFormInstance,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { NAME_REG_EXP } from '@/constants/regex';
import { compareArrayIdList } from '@/utils/common.util';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';
import { permissionApi } from '@/api/permission';
import { ewsAPI } from '@/api/ews';
import './AddOrEdit.css';

export default function AddOrEdit(props: any) {
  const { modalType, currentItem, queryTableData, visible, onClose, ruleList } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const [operator, setOperator] = useState<string>('');
  const [range, setRange] = useState<any>([]);
  const [currentStep, setCurrentStep] = useState(0);

  const [subList, setSubList] = useState<any>([]);
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';
  const [subTypeCode, setSubTypeCode] = useState(0);
  const isBetween = ['between'].includes(operator);
  const mainTypeList = ruleList.map((item: any) => {
    return {
      label: item.name,
      value: item.code,
    };
  });

  // 步骤配置
  const steps = [
    {
      title: '基础信息填写',
      description: '填写规则基本信息',
    },
    {
      title: '规则明细配置',
      description: '配置预警规则详情',
    },
    {
      title: '完成',
      description: '规则创建完成',
    },
  ];
  const subTypeItem = useMemo(() => {
    return subList.find((item: any) => item.value === subTypeCode);
  }, [subList, subTypeCode]);

  const onSubTypeChange = useCallback((value: any) => {
    setSubTypeCode(value);
  }, []);

  useEffect(() => {
    request(permissionApi.getDataPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
        type: 'operator',
      },
    }).then((res: any) => {
      let list = res?.data?.list?.[0]?.children || [];
      list = list.map((item: any) => {
        return {
          label: item.name,
          value: item.code,
        };
      });
      setRange(list);
    });
  }, []);

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  const handleMainTypeChange = useCallback(
    (value: any) => {
      const selectedParent = ruleList.find((item: any) => item.code === value);
      if (selectedParent && selectedParent.children) {
        setSubList(
          selectedParent.children.map((item: any) => {
            return {
              label: item.name,
              value: item.code,
              description: item.description,
            };
          }),
        );
      } else {
        setSubList([]);
      }
    },
    [ruleList],
  );
  const updateForm = (data: any = {}) => {
    setSubTypeCode(0);
    handleMainTypeChange(data.main_type);
    formRef.current?.setFieldsValue(data);
    setOperator(data.operator);
    setSubTypeCode(currentItem?.sub_type);
  };
  useEffect(() => {
    if (modalType !== 1) {
      updateForm(currentItem);
    }
  }, [currentItem, modalType]);

  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      if (modalType !== 1) {
        updateForm(currentItem);
        setCurrentStep(0); // 编辑模式下也从第一步开始
      }
    } else {
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
    setCurrentStep(0); // 重置步骤
  };
  /**
   * 新增请求接口
   */
  const userAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: ewsAPI.postRule,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const userEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: ewsAPI.putRule(id),
      data: { ...currentItem, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await userEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success(result.message || '修改成功。');
        await queryTableData();
        setCurrentStep(0); // 重置步骤
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  const onFinishAdd = async (item: any) => {
    try {
      const result = await userAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增规则成功。');
        await queryTableData();
        setCurrentStep(0); // 重置步骤
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  // 下一步
  const handleNext = async () => {
    try {
      if (currentStep === 0) {
        // 验证第一步表单
        await formRef.current?.validateFields(['name', 'message']);
      } else if (currentStep === 1) {
        // 验证第二步表单
        const fieldsToValidate = ['main_type', 'sub_type', 'operator', 'threshold_value', 'level', 'status'];
        if (isBetween) {
          fieldsToValidate.push('threshold_value2');
        }
        await formRef.current?.validateFields(fieldsToValidate);
      }
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 上一步
  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };

  const validateSecondValue = (_: any, value: number) => {
    const firstValue = formRef.current?.getFieldValue('threshold_value');
    if (value && firstValue !== undefined && value <= firstValue) {
      return Promise.reject('触发条件2必须大于触发条件1');
    }
    return Promise.resolve(true);
  };
  const handleValue1Change = (value: any) => {
    const form = formRef.current;
    if (form) {
      form.validateFields(['threshold_value2']);
    }
  };
  const onOperatorChange = (value: string) => {
    setOperator(value);
  };

  const isCategory = subTypeItem?.label?.indexOf('种类') > -1;

  useEffect(() => {
    if (isCategory) {
      formRef.current?.setFieldsValue({
        operator: 'gte',
        threshold_value: 1,
      });
    } else {
      formRef.current?.setFieldsValue({
        operator: undefined,
        threshold_value: '',
      });
    }
  }, [isCategory]);

  // 渲染第一步：基础信息填写
  const renderStep1 = () => (
    <>
      <ProFormText
        name="name"
        label={'规则名称'}
        required={true}
        fieldProps={{
          maxLength: 20,
        }}
        rules={[
          {
            required: true,
            message: '规则名称不能为空。',
          },
          {
            max: 20,
            message: '规则名称超过20个字。',
          },
          {
            pattern: NAME_REG_EXP,
            message: '规则名称格式错误。',
          },
        ]}
        placeholder={'输入规则名称'}
      />
      <ProFormText
        label="通知对象"
        name="notify_objects"
        disabled
        initialValue="设备关联人"
      />
      <ProFormText
        label="通知渠道"
        disabled
        name="notify_channels"
        initialValue="平台推送"
      />
      <ProFormTextArea
        label="预警文案"
        name="message"
        required={true}
        placeholder={'请输入预警文案'}
        rules={[
          {
            required: true,
            message: '请输入预警文案',
          },
        ]}
      />
    </>
  );

  // 渲染第二步：规则明细配置
  const renderStep2 = () => (
    <>
      <ProFormSelect
        label="预警类型"
        name="main_type"
        options={mainTypeList}
        onChange={handleMainTypeChange}
        placeholder="请选择预警类型"
        rules={[
          {
            required: true,
            message: '请选择预警类型',
          },
        ]}
      />
      <div className="bg-white p-[24px] rounded-[8px] mb-[24px] border-solid border-[1px] border-line-border pb-0 ">
        <ProFormSelect
          label="触发类型"
          required
          className="mb-0"
          onChange={onSubTypeChange}
          extra={subTypeItem?.description || ''}
          name="sub_type"
          options={subList}
          placeholder="请选择触发类型"
          rules={[
            {
              required: true,
              message: '请选择触发类型',
            },
          ]}
        />
      </div>
      <ProFormSelect
        label="统计范围"
        name="operator"
        options={range}
        placeholder="请选择统计范围"
        disabled={isCategory}
        rules={[
          {
            required: true,
            message: '请选择统计范围',
          },
        ]}
        onChange={onOperatorChange}
      />
      <ProFormDigit
        name="threshold_value"
        label={'触发条件值'}
        disabled={isCategory}
        fieldProps={{ precision: 0, onChange: handleValue1Change }}
        rules={[
          {
            required: true,
            message: '请填写触发条件值',
          },
        ]}
        placeholder={'请填写触发条件值'}
      />
      {isBetween ? (
        <ProFormDigit
          name="threshold_value2"
          fieldProps={{ precision: 0 }}
          label={'触发条件值2'}
          required={true}
          rules={[
            {
              required: true,
              message: '请填写触发条件值',
            },
            {
              validator: (_, value) => validateSecondValue(_, value),
            },
          ]}
          placeholder={'请填写触发条件值'}
        />
      ) : null}
      <ProFormRadio.Group
        label="预警等级"
        required={true}
        name="level"
        initialValue="low"
        options={[
          { value: 'high', label: '高级' },
          { value: 'medium', label: '中等' },
          { value: 'low', label: '低级' },
        ]}
      />
      <ProFormSelect
        label="是否启用"
        name="status"
        required={true}
        initialValue={1}
        options={[
          {
            label: '启用',
            value: 1,
          },
          {
            label: '禁用',
            value: 2,
          },
        ]}
      />
    </>
  );

  // 渲染第三步：完成
  const renderStep3 = () => {
    const mainTypeValue = formRef.current?.getFieldValue('main_type');
    const mainTypeLabel = mainTypeList.find(item => item.value === mainTypeValue)?.label || '';
    const ruleName = formRef.current?.getFieldValue('name') || '';

    return (
      <div className="add-or-edit-step3">
        <div className="success-icon">
          <div className="checkmark">✓</div>
        </div>
        <div className="success-text">已添加到规则模板</div>
        <div className="success-desc">
          规则名称：{ruleName}
        </div>
        <div className="success-desc">
          预警类型：{mainTypeLabel}
        </div>
      </div>
    );
  };

  return (
    <>
      <XcDrawer
        title={false}
        closable={false}
        mask={true}
        maskClosable={false}
        open={drawerVisible}
        width={600}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}规则`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>

        {/* 步骤导航 */}
        <div className="add-or-edit-steps-container">
          <Steps
            current={currentStep}
            items={steps}
            className="add-or-edit-steps"
          />
        </div>

        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            {currentStep === 0 && renderStep1()}
            {currentStep === 1 && renderStep2()}
            {currentStep === 2 && renderStep3()}
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            {currentStep > 0 && (
              <Button onClick={handlePrev}>
                上一步
              </Button>
            )}
            {currentStep < 2 && (
              <Button
                type="primary"
                onClick={handleNext}
                className="xc-form-button-submit"
              >
                下一步
              </Button>
            )}
            {currentStep === 2 && modalType === 1 && (
              <Button
                type="primary"
                htmlType="submit"
                className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
                onClick={() => {
                  formRef.current?.submit();
                }}
              >
                保存
              </Button>
            )}
            {currentStep === 2 && modalType !== 1 && (
              <Button
                type="primary"
                htmlType="submit"
                className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
                onClick={() => {
                  formRef.current?.submit();
                }}
              >
                更新
              </Button>
            )}
          </Space>
        </div>
      </XcDrawer>
    </>
  );
}
