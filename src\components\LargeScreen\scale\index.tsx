/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-25 18:18:07
 * @LastEditTime: 2025-01-11 12:56:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import imgBg from '@/assets/img_bigscreen.jpg';
import { memo } from 'react';
import styles from './index.less';
import { useFitScreen } from './useFitScreen';

export const LargeScreen = memo((props: any) => {
  const { previewRef } = useFitScreen();

  return (
    <div ref={previewRef}>
      <div
        className={styles['page-container']}
        style={{
          backgroundImage: `url(${imgBg})`,
          backgroundRepeat: 'repeat-y',
          backgroundSize: 'cover',
        }}
      >
        {props.children}
      </div>
    </div>
  );
});
