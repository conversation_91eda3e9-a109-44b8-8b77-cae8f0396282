.BigScreenTree {
  position: relative;
  height: 40px;
  width: 100%;
  z-index: 999;
}

.BigScreenTree-tree {
  width: 100% !important;
  height: 40px;
  font-weight: 500;
  border-radius: 8px;
  background: rgba(27, 99, 167, 0.15);
  box-shadow: 0px 4px 12px 0px rgba(7, 30, 62, 0.50), 0px 4px 10px 0px rgba(69, 196, 255, 0.15) inset;
  border: 1px solid rgba(42, 116, 234, 0.44);
  backdrop-filter: blur(40px);
  transition-duration: all 1s;

  .ant-select-selection-item {
    font-size: 14px;
  }

  .ant-select-selector {
    background: transparent !important;
    border: none !important;
    color: #fff !important;
    border-radius: 8px;
  }

  &.ant-select-open .ant-select-selection-item {
    color: #fff !important;
  }

  .ant-select-arrow {
    color: #fff !important;
    opacity: 0.5;
  }


}
.is-2dMap  .BigScreenTree-tree{
  background: rgba(17, 19, 41, 0.99);
  box-shadow: none;
  border: none;
}

.BigScreenTree-tree-popup {
  background: linear-gradient(179deg, #0D4180 0.8%, rgba(9, 36, 106, 0.79) 102.33%);
  box-shadow: -25px 28px 48px 0px rgba(0, 0, 0, 0.21), 0px 0px 40px 0px rgba(109, 153, 255, 0.25) inset;
  backdrop-filter: blur(30px);
  border-radius: 8px;

  .ant-select-tree {
    background-color: transparent !important;
  }

  .ant-select-tree-treenode {
    line-height: 36px !important;
  }

  .ant-select-tree-switcher-icon {
    color: #fff !important;
  }

  .ant-select-tree-list {
    padding: 12px !important;
  }

  .ant-select-tree-treenode-selected {
    border-radius: 6px;
    background: #2953FF;
  }

  .ant-select-tree-node-content-wrapper {
    background-color: transparent !important;
  }

  .ant-select-tree-title {
    font-size: 14px !important;
    color: #fff !important;
    vertical-align: top;
  }

  .ant-select-tree-node-selected .ant-select-tree-title {
    font-weight: 600;
  }
}
