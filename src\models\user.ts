/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:32:57
 * @LastEditTime: 2024-12-24 14:26:39
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { history, useModel } from '@umijs/max';

export default () => {
  const { setInitialState } = useModel('@@initialState');
  const { setPersist } = useModel('persist');
  const logout = () => {
    setInitialState((s) => ({
      ...s,
      user: null,
    }));
    setPersist('token', {});
    setPersist('user', {});
    localStorage.removeItem('lastRoute');
    // setTimeout(() => {
    //   history.push('/user/login');
    // }, 10);
  };

  return {
    logout,
  };
};
