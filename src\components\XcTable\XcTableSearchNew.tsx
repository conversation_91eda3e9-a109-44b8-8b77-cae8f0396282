import { ConfigProvider, Form, Input, Select, DatePicker } from 'antd';
import { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { IconClose } from '../Icon/IconClose';
import { IconSearch } from '../Icon/IconSearch';
import { IColumns } from './interface/search.interface';
import { ProForm } from '@ant-design/pro-components';
import { styled } from '@umijs/max';
import React from 'react';
import './XcTableSearchNew.css';
const { RangePicker } = DatePicker;
interface IProps {
  columns?: IColumns[];
  children?: React.ReactNode;
  requestTable: (criteria?: any) => void;
  className?: string;
  searchTitle?: React.ReactNode;
}
const StyledFormItem = styled(Form.Item)`
  margin-inline-end: 8px !important;
`;
export const XcTableSearchNew = memo(
  forwardRef((props: IProps, ref: any) => {
    const { columns, children, className, requestTable, searchTitle } = props;
    const formRef = useRef<any>(null);
    const disabledDate = (current: any, info: any, rangeLimit: any) => {
      const { from } = info;
      if (rangeLimit) {
        const [time, unit] = rangeLimit;

        return Math.abs(current.diff(from, unit)) >= time;
      }
      return false;
    };

    useImperativeHandle(ref, () => {
      return {
        getFieldsValue: () => {
          return formRef.current?.getFieldsValue();
        },
      };
    });

    useEffect(() => {
      const data = columns?.reduce((acc, cur) => {
        const { key, initialValue } = cur;
        if (key && initialValue) {
          acc[key] = initialValue;
        }
        return acc;
      }, {} as any);
      formRef.current.setFieldsValue(data);
    }, []);

    return (
      <div
        className={
          'flex justify-between mt-[24px] mb-[16px] bg-white  py-[12px] px-[16px] items-center rounded-[8px]' +
          className
        }
      >
        <div className="flex flex-shrink-0 text-[16px] font-medium text-txt-main">
          {searchTitle}
        </div>
        <ProForm
          layout="inline"
          submitter={false}
          onValuesChange={requestTable}
          formRef={formRef}
          className="items-center flex-wrap gap-y-[12px] flex-1 justify-end"
        >
          {columns?.map((item, index) => {
            const {
              valueType,
              valueEnum = {} as any,
              maxValue,
              minValue,
              rangeLimit,
              placeholder,
              fieldProps,
              width,
              height,
              render,
              key,
            } = item;
            if (render) {
              return (
                <StyledFormItem key={key} name={key}>
                  {render('', item, index, {
                    onChange: (data: any) => {
                      requestTable();
                    },
                  })}
                </StyledFormItem>
              );
            } else if (valueType === 'drop') {
              let options = [];
              if (Array.isArray(valueEnum)) {
                options = valueEnum.map((item) => {
                  return {
                    label: item.name,
                    value: item.id,
                    ...item,
                  };
                });
              } else {
                options = Object.keys(valueEnum).map((key) => {
                  return { label: valueEnum[key], value: key };
                });
              }

              return (
                <StyledFormItem name={key} key={key}>
                  <Select
                    style={{ width }}
                    onSelect={(e) => {
                      requestTable();
                    }}
                    options={options}
                    placeholder={placeholder}
                  />
                </StyledFormItem>
              );
            } else if (valueType === 'rangePicker' && key) {
              return (
                <StyledFormItem key={key} name={key}>
                  <RangePicker
                    maxDate={maxValue}
                    minDate={minValue}
                    style={width ? { width } : undefined}
                    disabledDate={(current, info) => disabledDate(current, info, rangeLimit)}
                    onChange={(e) => {
                      requestTable();
                    }}
                    allowClear={{
                      clearIcon: (
                        <IconClose
                          onClick={() => {
                            setTimeout(() => {
                              requestTable();
                            }, 0);
                          }}
                        />
                      ),
                    }}
                    {...fieldProps}
                  />
                </StyledFormItem>
              );
            } else {
              if (key) {
                return (
                  <StyledFormItem key={key} name={key}>
                    <Input
                      allowClear={{
                        clearIcon: (
                          <IconClose
                            onClick={() => {
                              setTimeout(() => {
                                requestTable();
                              }, 0);
                            }}
                          />
                        ),
                      }}
                      placeholder={placeholder}
                      prefix={<IconSearch className="mr-[8px] size-[20px] text-txt-sub " />}
                      onPressEnter={requestTable}
                      onBlur={requestTable}
                      onChange={requestTable}
                    />
                  </StyledFormItem>
                );
              }
              return null;
            }
          })}
        </ProForm>
        {children ? <div className="XcTableSearchNew-operator">{children}</div> : null}
      </div>
    );
  }),
);
