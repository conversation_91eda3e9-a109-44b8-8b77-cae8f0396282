/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-25 10:51:00
 * @LastEditTime: 2025-01-14 22:30:46
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export const IconDeviceInfo = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#0080FF" />
      <path
        d="M12 5.25C8.68661 5.25 6 7.93661 6 11.25C6 14.5634 8.68661 17.25 12 17.25C15.3134 17.25 18 14.5634 18 11.25C18 7.93661 15.3134 5.25 12 5.25ZM12 6.75C12.3549 6.75 12.6429 7.03795 12.6429 7.39286C12.6429 7.74777 12.3549 8.03571 12 8.03571C11.6451 8.03571 11.3571 7.74777 11.3571 7.39286C11.3571 7.03795 11.6451 6.75 12 6.75ZM12 14.25C10.3433 14.25 9 12.9067 9 11.25C9 9.5933 10.3433 8.25 12 8.25C13.6567 8.25 15 9.5933 15 11.25C15 12.9067 13.6567 14.25 12 14.25Z"
        fill="white"
      />
      <path
        d="M16.305 17.3375C16.5717 17.5367 16.5632 17.9689 16.2895 18.1574C15.0459 19.008 13.5763 19.5 12 19.5C10.4237 19.5 8.9541 19.008 7.71047 18.1559C7.43676 17.9689 7.42834 17.5367 7.69503 17.3359C7.84241 17.2256 8.03611 17.221 8.18911 17.3252C8.51195 17.5459 8.85023 17.739 9.20394 17.9015C10.0882 18.3122 11.0287 18.5191 12 18.5191C12.9713 18.5191 13.9118 18.3122 14.7975 17.903C15.1512 17.739 15.4895 17.5474 15.8123 17.3267C15.9639 17.2225 16.1576 17.2256 16.305 17.3375Z"
        fill="white"
      />
      <circle cx="12" cy="11.25" r="1.5" fill="white" />
    </svg>
  );
};
