/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-11 11:25:59
 * @LastEditTime: 2025-02-11 11:26:11
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconMonitor = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="8" fill="#2953FF" />
      <rect
        x="6.75"
        y="7.41675"
        width="10.5"
        height="9.16667"
        rx="2.25"
        stroke="white"
        strokeWidth="1.5"
      />
      <path
        d="M6.6665 13.5555H9.25687L9.95361 11.7412C10.0786 11.4158 10.5311 11.3948 10.6856 11.7074L11.7207 13.8014C11.8786 14.1208 12.3438 14.09 12.4582 13.7525L13.5707 10.4692C13.6941 10.1052 14.2098 10.1077 14.3296 10.4729L15.3412 13.5555H17.3332"
        stroke="white"
        strokeWidth="1.2"
        strokeLinecap="round"
      />
    </svg>
  );
};
