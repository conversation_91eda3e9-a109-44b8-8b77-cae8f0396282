/* GroupProgressList 组件样式 */
.gpl-card-container {
  background: rgba(17, 19, 41, 0.99);
  backdrop-filter: blur(80px);
  border-radius: 8px;
  padding: 24px 30px 30px;
  width: 450px;
  display: flex;
  flex-direction: column;

  .gpl-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 26px;
    font-size: 18px;
  }

  .gpl-split-line {
    background: rgba(255, 255, 255, .05);
    height: 1px;
    margin: 20px auto 20px;
    width: 100%;
  }

  .gpl-header-title {
    font-family: 'PingFang SC', sans-serif;
    font-weight: 500;
    font-size: 18px;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .gpl-header-action {
    display: flex;
    align-items: center;
    gap: 2px;
    cursor: pointer;
  }

  .gpl-header-action-text {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
  }

  .gpl-header-arrow {
    width: 16px;
    height: 16px;
    display: inline-block;
  }

  .gpl-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 170px;
  }

  .gpl-list-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
    width: 390px;
  }

  .gpl-index-badge {
    background: rgba(50, 92, 145, 0.2);
    border-radius: 10px;
    padding: 3px 7px;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .gpl-index-badge span {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 400;
  }

  .gpl-group-name {
    font-family: 'PingFang SC', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    min-width: 100px;
  }

  .gpl-progress-bar-wrap {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    justify-content: space-between;
  }

  .gpl-progress-bar-bg {
    width: 160px;
    height: 6px;
    background: rgba(237, 239, 247, 0.1);
    border-radius: 5px;
    position: relative;
    overflow: hidden;
  }

  .gpl-progress-bar-fg {
    height: 6px;
    background: #0080FF;
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 0;
  }

  .gpl-progress-value {
    font-family: 'PingFang SC', sans-serif;
    font-size: 16px;
    line-height: 20px;
    color: #fff;
    font-weight: 400;
    min-width: 40px;
    text-align: right;
  }

  .gpl-list-row-empty {
    font-size: 14px;
    color: #666;
    font-weight: 400;
    text-align: center;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
