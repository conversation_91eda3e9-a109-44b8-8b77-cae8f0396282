/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:39:09
 * @LastEditTime: 2025-02-08 14:55:37
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import { BigScreenAPI } from '@/api';
import { formatNumberWithCommas } from '@/utils/number.util';
import { useRequest } from '@umijs/max';
import { forwardRef, memo, useEffect, useImperativeHandle, useState } from 'react';
import { SvgDeviceMonitor } from '../icon/SvgDeviceMonitor';
import { SvgDeviceMonitorPercent } from '../icon/SvgDeviceMonitorPercent';
import { SvgDeviceTotal } from '../icon/SvgDeviceTotal';
import { SvgDeviceUnMonitor } from '../icon/SvgDeviceUnMonitor';
import { IconEwsStat } from '@/assets/svg';

interface IProps {
  className?: string;
  title?: string;
  value?: string;
  icon?: React.ReactNode;
}

const CustomTableDataItem = (props: IProps) => {
  const { className, title, value, icon } = props;
  return (
    <div
      className={`shrink-0 p-[20px] h-[122px] rounded-[16px] flex justify-between items-center ${className}`}
    >
      <div className="flex flex-col">
        <p className="font-normal text-[14px] text-txt-sub">{title}</p>
        <p className="font-bold text-[28px] text-txt-main">{value}</p>
      </div>
      {icon}
    </div>
  );
};
const defaultData = {
  total: 0,
  high: 0,
  medium: 0,
  low: 0,
};
export const StatData = memo(
  forwardRef((props: any, ref: any) => {
    const [stats, setStats] = useState(props.data || defaultData);
    useEffect(() => {
      setStats(props.data || defaultData);
    }, [props.data]);
    return (
      <div className="grid gap-[20px] grid-cols-4 grid-rows-1  h-[122px] mb-[16px]">
        <CustomTableDataItem
          className="bg-[#C8C6FF]/25"
          title="预警总数"
          value={formatNumberWithCommas(stats.total)}
          icon={
            <div className="flex justify-center items-center w-[62px] h-[62px] bg-[#8280FF] bg-opacity-20 rounded-[20px] text-[#8280FF]">
              <IconEwsStat className="size-[40px]" />
            </div>
          }
        />
        <CustomTableDataItem
          className="bg-[#FEBFA7]/20"
          title="高等预警"
          value={`${stats.high}`}
          icon={
            <div className="flex justify-center items-center w-[62px] h-[62px] bg-[#FF9066] bg-opacity-20 rounded-[20px] text-[#FF9066]">
              <IconEwsStat className="size-[40px]" />
            </div>
          }
        />
        <CustomTableDataItem
          className="bg-[#FCE4A8]/30"
          title="中等预警"
          value={stats?.medium}
          icon={
            <div className="flex justify-center items-center w-[62px] h-[62px] bg-[#FFB85B] bg-opacity-20 rounded-[20px] text-[#FFB85B]">
              <IconEwsStat className="size-[40px]" />
            </div>
          }
        />
        <CustomTableDataItem
          className="bg-[#ADEDCE]/30"
          title="低等预警"
          value={stats?.low}
          icon={
            <div className="flex justify-center items-center w-[62px] h-[62px] bg-[#4AD991] bg-opacity-20 rounded-[20px] text-[#4AD991]">
              <IconEwsStat className="size-[40px]" />
            </div>
          }
        />
      </div>
    );
  }),
);
