import { useEffect, useRef, useState } from 'react';

export default function XxcBlurHide({
  children,
  onHide,
  visible,
}: {
  children: React.ReactNode;
  onHide: () => void;
  visible: boolean;
}) {
  const wrapperRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = (event: React.FocusEvent<HTMLDivElement>) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.relatedTarget as Node)) {
      setIsFocused(false);
      onHide();
    }
  };

  useEffect(() => {
    if (visible) {
      wrapperRef.current?.focus();
    }
  }, [visible]);

  return visible ? (
    <div ref={wrapperRef} tabIndex={0} onFocus={handleFocus} onBlur={handleBlur}>
      {children}
    </div>
  ) : null;
}
