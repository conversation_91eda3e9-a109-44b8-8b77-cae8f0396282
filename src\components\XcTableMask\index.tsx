import { useEffect, useMemo, useState } from 'react';

type IProps = {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  width?: number;
  left?: number;
  top?: number;
  isFormChanged?: boolean;
};
export default function XcTableMask(props: IProps) {
  const { visible, setVisible, left = 230, isFormChanged = false } = props;
  const [top, setTop] = useState(0);
  const [width, setWidth] = useState(0);
  useEffect(() => {
    function changePosition() {
      let t = props.top || 0;
      if (props.top === undefined) {
        t = document.querySelector('.ant-table-header')?.getBoundingClientRect?.().top || 0;
      }
      setTop(t);
      let w = 0;
      if (props.width === undefined) {
        const rect = document
          .querySelector('.ant-table-body tr td:nth-of-type(2)')
          ?.getBoundingClientRect?.() || { left: 230, width: 160 };
        w = rect.left + rect.width - left;
      } else {
        w = props.width;
      }
      setWidth(w);
    }

    const timer = setTimeout(() => {
      changePosition();
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, [props.top, props.width]);
  return (
    <>
      <div
        onClick={() => setVisible(false)}
        className={`fixed left-0  top-0 bottom-0 z-[99]  ${visible ? 'visible' : 'hidden'}`}
        style={{
          width: left,
        }}
      />
      <div
        onClick={() => setVisible(false)}
        className={`fixed left-[270px] top-0 z-[99]  ${visible ? 'visible' : 'hidden'}`}
        style={{
          left: left,
          height: top,
          width: width,
        }}
      />
      <div
        className={`fixed bottom-0 z-[99] ${visible ? 'visible' : 'hidden'}`}
        style={{
          left: left,
          top: top,
          width: width,
          pointerEvents: isFormChanged ? 'auto' : 'none',
        }}
      />
      <div
        onClick={() => setVisible(false)}
        className={`fixed right-0 top-0 bottom-0 z-[99]  ${visible ? 'visible' : 'hidden'}`}
        style={{
          left: left + width,
        }}
      />
    </>
  );
}
