/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 22:14:39
 * @LastEditTime: 2025-01-21 10:37:24
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

// TODO 改成 zustand
export default () => {
  const getPersist = (key: string) => {
    const data = localStorage.getItem(key);
    if (data) {
      return JSON.parse(data);
    }
    return null;
  };

  const setPersist = (key: string, value: any) => {
    localStorage.setItem(key, JSON.stringify(value));
  };

  return { getPersist, setPersist };
};
