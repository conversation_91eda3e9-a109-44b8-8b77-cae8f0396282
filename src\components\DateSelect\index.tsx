import { getRangeByTimeUnit, TIME_UNITS } from '@/utils/time.util';
import { useMemoizedFn } from 'ahooks';
import { Button, DatePicker } from 'antd';
import dayjs from 'dayjs';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';

export enum DATE_TYPE {
  None = 'none',
  Today = 'today',
  Yesterday = 'yesterday',
  Custom = 'custom',
  Last3day = 'last3day',
  Last7day = 'last7day',
}
const { RangePicker } = DatePicker;

interface DateSelectProps {
  onChange?: (date: any) => void;
  defaultRange?: any[];
  defaultType?: DATE_TYPE.Today;
  defaultValue?: any[];
}

export default forwardRef((props: DateSelectProps, ref: any) => {
  const {
    onChange = () => {},
    defaultRange,
    defaultType = DATE_TYPE.Today,
    defaultValue = [],
  } = props;
  const [dateType, setDateType] = useState<DATE_TYPE>(defaultType);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const rangeRef = useRef<any>([]);
  const [value, setValue] = useState<any>([]);

  useEffect(() => {
    if (defaultRange) {
      rangeRef.current = defaultRange;
    } else {
      rangeRef.current = getRangeByTimeUnit(TIME_UNITS.MONTH);
    }
  }, [defaultRange]);

  useEffect(() => {
    const [start, end] = defaultValue || [];
    if (!start || !end) {
      return;
    }
    const now = dayjs();
    // const yesterday = now.subtract(1, 'day');
    if (start >= now.startOf('day') && end <= now.endOf('day')) {
      setDateType(DATE_TYPE.Today);
    }
    // else if (start >= yesterday.startOf('day') && end <= yesterday.endOf('day')) {
    //   setDateType(DATE_TYPE.Yesterday);
    // }
    else {
      setDateType(DATE_TYPE.Custom);
      setShowDatePicker(true);
    }
  }, []);

  useImperativeHandle(ref, () => {
    return {
      value: value,
    };
  });

  const handleDateTypeChange = useMemoizedFn((type: DATE_TYPE) => {
    setDateType(type);
    if (type === DATE_TYPE.Custom) {
      setValue([...rangeRef.current]);
      onChange([...rangeRef.current]);
      setShowDatePicker(true);
    } else {
      setShowDatePicker(false);
      if (type === DATE_TYPE.Today) {
        const range = [dayjs().startOf('day'), dayjs().endOf('day')];
        setValue([...range]);
        onChange(range);
      } else if (type === DATE_TYPE.Yesterday) {
        const range = [
          dayjs().subtract(1, 'day').startOf('day'),
          dayjs().subtract(1, 'day').endOf('day'),
        ];
        setValue([...range]);
        onChange(range);
      } else if (type === DATE_TYPE.Last3day) {
        const range = [dayjs().subtract(2, 'day').startOf('day'), dayjs().endOf('day')];
        setValue([...range]);
        onChange(range);
      } else if (type === DATE_TYPE.Last7day) {
        const range = [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
        setValue([...range]);
        onChange(range);
      }
    }
  });

  const handleDateChange = useMemoizedFn((date: any) => {
    const range = [date[0].startOf('day'), date[1].endOf('day')];
    setValue([...range]);
    onChange(range);
  });

  return (
    <>
      <Button
        type={dateType === DATE_TYPE.Today ? 'primary' : 'default'}
        onClick={() => handleDateTypeChange(DATE_TYPE.Today)}
        className="mr-[10px]"
      >
        今天
      </Button>
      {/* <Button
        type={dateType === DATE_TYPE.Yesterday ? 'primary' : 'default'}
        onClick={() => handleDateTypeChange(DATE_TYPE.Yesterday)}
        className="mr-[16px]"
      >
        昨天
      </Button> */}
      <Button
        type={dateType === DATE_TYPE.Last3day ? 'primary' : 'default'}
        onClick={() => handleDateTypeChange(DATE_TYPE.Last3day)}
        className="mr-[10px]"
      >
        近3天
      </Button>
      <Button
        type={dateType === DATE_TYPE.Last7day ? 'primary' : 'default'}
        onClick={() => handleDateTypeChange(DATE_TYPE.Last7day)}
        className="mr-[10px]"
      >
        近7天
      </Button>
      {showDatePicker ? (
        <RangePicker
          className="w-[300px] mr-[10px] border-[#0080FF] "
          allowClear={false}
          onChange={handleDateChange}
          defaultValue={rangeRef.current}
        />
      ) : (
        <Button
          type={dateType === DATE_TYPE.Custom ? 'primary' : 'default'}
          onClick={() => handleDateTypeChange(DATE_TYPE.Custom)}
          className="mr-[10px]"
        >
          自定义
        </Button>
      )}
    </>
  );
});
