/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-24 23:16:10
 * @LastEditTime: 2025-02-12 17:07:43
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { useRafInterval } from 'ahooks';
import dayjs from 'dayjs';
import { useState } from 'react';

const weekMap: Record<string, string> = {
  0: '星期日',
  1: '星期一',
  2: '星期二',
  3: '星期三',
  4: '星期四',
  5: '星期五',
  6: '星期六',
};

const StyledContainer = styled.div`
  display: flex;
  width: ${() => px2vw(175)};
  .time {
    width: ${() => px2vw(125)};
  }
  .week {
    width: ${() => px2vw(45)};
  }
`;

export const CurrentTime = () => {
  const [time, setTime] = useState<string>('');
  const [week, setWeek] = useState('');

  useRafInterval(
    () => {
      setTime(dayjs().format('MM月DD日 HH:mm:ss'));
      setWeek(weekMap[dayjs().day()]);
    },
    1000,
    {
      immediate: true,
    },
  );

  return (
    <StyledContainer>
      <div className="time">{time}</div>
      <div className="week text-right">{week}</div>
    </StyledContainer>
  );
};
