import { COLUMN_RENDER_TYPE, IColumns } from '@/components/XcTable/interface/search.interface';
import { useRequest } from '@umijs/max';
import { message } from 'antd';
import { useMemo, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { logApi } from '@/api/log';
import { XcTableNew } from '@/components/XcTable/XcTableNew';
import { useMemoizedFn } from 'ahooks';
import JsonEditor from '@/components/JsonEditor/JsonEditor';

export default () => {
  const logListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: logApi.getBindLog,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.step === 'all') {
      delete others.step;
    }
    if (others.status === 'all') {
      delete others.status;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await logListRequest.run(queryPayload);

    if (result.code === 200) {
      let list = result.data.list || [];
      // 给每一行添加一个唯一的key
      list = list.map((item: any, index: number) => {
        return {
          ...item,
          __rowKey: item.id + '_' + index,
        };
      });
      return {
        total: result.data.total || 0,
        data: list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };
  const [editorData, setEditorData] = useState<Record<string, any> | null>(null);

  const closeEditorModal = () => {
    setEditorData(null);
  };

  const showJsonModal = useMemoizedFn((data: Record<string, any>) => {
    setEditorData(data);
  });

  const columns: IColumns[] = useMemo(
    () => [
      {
        title: '绑定状态',
        width: 140,
        key: 'status',
        hideInTable: true,
        placeholder: '全部状态',
        valueType: 'drop',
        valueEnum: [
          { label: '全部状态', value: 'all' },
          { label: '进行中', value: 1 },
          { label: '成功', value: 2 },
          { label: '失败', value: 3 },
          { label: '超时', value: 4 },
        ],
      },
      {
        title: '绑定步骤',
        width: 140,
        key: 'step',
        hideInTable: true,
        placeholder: '全部步骤',
        valueType: 'drop',
        valueEnum: [
          { label: '全部步骤', value: 'all' },
          { label: '接收设备请求', value: 1 },
          { label: '参数验证', value: 2 },
          { label: '签名验证', value: 3 },
          { label: '检查设备唯一性', value: 4 },
          { label: '创建设备', value: 5 },
          { label: '注册MQTT凭证', value: 6 },
          { label: '绑定完成', value: 7 },
        ],
      },
      {
        title: '创建时间',
        key: 'range_time',
        initialValue: [dayjs().subtract(30, 'day'), dayjs()],
        maxValue: dayjs(),
        minValue: dayjs('2024-12-01'),
        rangeLimit: [18, 'month'],
        hideInTable: true,
        valueType: 'rangePicker',
      },
      {
        title: '设备标识',
        width: 320,
        key: 'device_unique_id',
        hideInTable: true,
        placeholder: '搜索设备标识',
        valueType: 'input',
      },
      {
        title: '序号',
        dataIndex: 'id',
        key: 'id',
        hideInSearch: true,
        width: 100,
        renderType: COLUMN_RENDER_TYPE.text_ellipsis,
      },
      {
        title: '设备标识',
        dataIndex: 'device_unique_id',
        key: 'device_unique_id',
        hideInSearch: true,
        width: 210,
        renderType: COLUMN_RENDER_TYPE.text_ellipsis,
      },
      {
        title: '请求数据',
        dataIndex: 'request_data',
        key: 'request_data',
        render: (value: string, record: any) => {
          return (
            <div
              className="text-one-row cursor-pointer"
              onClick={() =>
                showJsonModal({
                  json: value,
                  title: `日志编号-${record.id} 请求数据：`,
                })
              }
            >
              {value}
            </div>
          );
        },
      },
      {
        title: '响应数据',
        dataIndex: 'response_data',
        key: 'response_data',
        render: (value: string, record: any) => {
          return (
            <div
              className="text-one-row cursor-pointer"
              onClick={() =>
                showJsonModal({
                  json: value,
                  title: `日志编号-${record.id} 响应数据：`,
                })
              }
            >
              {value}
            </div>
          );
        },
      },
      {
        title: '绑定步骤',
        dataIndex: 'step',
        key: 'step',
        hideInSearch: true,
        width: 140,
        renderType: COLUMN_RENDER_TYPE.text_ellipsis,
      },
      {
        title: '绑定状态',
        dataIndex: 'status',
        key: 'status',
        hideInSearch: true,
        width: 100,
        renderType: COLUMN_RENDER_TYPE.text_ellipsis,
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        hideInSearch: true,
        width: 160,
        render: (value: string) => {
          return dayjs(value).format(DATE_FMT.DATE_TIME);
        },
      },
    ],
    [],
  );
  const paginationRef = useRef({
    current: 1,
    defaultPageSize: 50,
    total: 0,
  });

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={logListRequest.loading}
        columns={columns}
        rowKey="__rowKey"
        request={requestTable}
        pagination={paginationRef.current}
        extend={null}
        batchRender={() => null}
        operator={null}
        rowSelection={null}
        searchTitle="设备绑定日志"
      />
      {editorData ? (
        <JsonEditor editorData={editorData} onClose={closeEditorModal}></JsonEditor>
      ) : null}
    </div>
  );
};
